import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:police/common/model/analyze_model.dart';
import 'package:police/page/analyzeDetail/index.dart';

import '../../analyze/controller.dart';

class AnalyzeIntro extends StatelessWidget {
  const AnalyzeIntro(
      {super.key, required this.detail, required this.controller});

  final AnalyzeModel detail;
  final AnalyzedetailController controller;

  @override
  Widget build(BuildContext context) {
    String year = controller.getYear(detail);
    String month = controller.getMonth(detail);
    String quarter = controller.getQuarter(detail);
    String day = controller.getDay(detail);

    return Column(
      children: [
        Text(
          detail.types == AnalyzeSortTypes.day
              ? '云冈派出所警情日报'
              : '每${analyzeSortTypesStr[detail.types]}警情',
          style: TextStyle(
            fontSize: 50,
            color: Colors.red,
            letterSpacing: detail.types == AnalyzeSortTypes.day ? 0 : 30,
          ),
        ),
        Text(
          detail.types == AnalyzeSortTypes.month
              ? '($year) 第 $month 期'
              : detail.types == AnalyzeSortTypes.day
                  ? ''
                  : detail.types == AnalyzeSortTypes.quarter
                      ? '第 $quarter 季 度'
                      : '($year)',
          style: const TextStyle(color: Colors.white),
        ),
        detail.types == AnalyzeSortTypes.day
            ? Text(
                '云冈派字【$year】  $month 月 $day 号',
                style: const TextStyle(color: Colors.white),
              )
            : Row(
                children: [
                  const Text(
                    '云冈派出所综合指挥室',
                    style: TextStyle(color: Colors.white),
                  ),
                  const Spacer(),
                  Text(
                    _setDate(detail.yearMonthDay),
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ),
      ],
    );
  }

  /// 设置日期字符串
  String _setDate(String? yearMonthDay) {
    List<String> yearMonthDayList = yearMonthDay!.split('-');

    int days = _getDaysInMonth(
        int.parse(yearMonthDayList[0]), int.parse(yearMonthDayList[1]));

    return yearMonthDay.isEmpty
        ? '新建时未设置日期'
        : '${yearMonthDayList[0]}-${yearMonthDayList[1]}-$days';
  }

  /// 获取每月天数
  int _getDaysInMonth(int year, int month) {
    switch (month) {
      case 1: // January
      case 3: // March
      case 5: // May
      case 7: // July
      case 8: // August
      case 10: // October
      case 12: // December
        return 31;
      case 4: // April
      case 6: // June
      case 9: // September
      case 11: // November
        return 30;
      case 2: // February
        if (_isLeapYear(year)) {
          return 29;
        } else {
          return 28;
        }
      default:
        throw ArgumentError('Invalid month: $month');
    }
  }

  /// 判断是否为闰年
  bool _isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }
}
