import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// class CornerCard extends StatefulWidget {
//   const CornerCard({
//     super.key,
//     required this.child,
//     this.width,
//     this.height,
//     this.duration = const Duration(milliseconds: 600),
//   });

//   // 接受一个Widget类型的child参数
//   final Widget child;
//   final double? width;
//   final double? height;
//   final Duration duration;

//   @override
//   State<CornerCard> createState() => _CornerCardState();
// }

// class _CornerCardState extends State<CornerCard> with TickerProviderStateMixin {
//   late AnimationController _controller;
//   late Animation<double> _animation;

//   @override
//   void initState() {
//     super.initState();
//     _controller = AnimationController(vsync: this, duration: widget.duration);
//     _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
//     _controller.forward(); // 开始动画
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SizeTransition(
//       sizeFactor: _animation,
//       child: SizedBox(
//         width: widget.width,
//         height: widget.height,
//         child: CustomPaint(
//           painter: AcuteAngle(),
//           child: Container(
//             padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
//             color: const Color(0xff3d8fff).withOpacity(.1),
//             child: widget.child,
//           ),
//         ),
//       ),
//     );
//   }
// }

// 定义自定义组件
class CornerCard extends StatelessWidget {
  // 接受一个Widget类型的child参数
  final Widget child;
  final double? width;
  final double? height;
  final Duration duration;
  final EdgeInsets padding;

  final Color? backgroundColor;

  // 构造函数要求传入一个child
  const CornerCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.duration = const Duration(milliseconds: 500),
    this.padding = const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        painter: AcuteAngle(),
        child: AnimatedContainer(
          duration: duration,
          padding: padding,
          color: backgroundColor ?? const Color(0xff3d8fff).withOpacity(.1),
          child: child,
        ),
      ),
    );
  }
}

class AcuteAngle extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xff3d8fff)
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    const double lineLength = 20.0;
    const double radius = 2.0; // 圆角半径

    // 左上角
    final leftTopPath = Path()
      ..moveTo(0, lineLength)
      ..lineTo(0, radius)
      ..arcToPoint(
        const Offset(radius, 0),
        radius: const Radius.circular(radius),
      )
      ..lineTo(lineLength, 0);
    canvas.drawPath(leftTopPath, paint);

    // 右上角
    final rightTopPath = Path()
      ..moveTo(size.width - lineLength, 0)
      ..lineTo(size.width - radius, 0)
      ..arcToPoint(
        Offset(size.width, radius),
        radius: const Radius.circular(radius),
      )
      ..lineTo(size.width, lineLength);
    canvas.drawPath(rightTopPath, paint);

    // 左下角
    final leftBottomPath = Path()
      ..moveTo(0, size.height - lineLength)
      ..lineTo(0, size.height - radius)
      ..arcToPoint(
        Offset(radius, size.height),
        radius: const Radius.circular(radius),
      )
      ..lineTo(lineLength, size.height);
    canvas.drawPath(leftBottomPath, paint);

    // 右下角
    final rightBottomPath = Path()
      ..moveTo(size.width, size.height - lineLength)
      ..lineTo(size.width, size.height - radius)
      ..arcToPoint(
        Offset(size.width - radius, size.height),
        radius: const Radius.circular(radius),
      )
      ..lineTo(size.width - lineLength, size.height);
    canvas.drawPath(rightBottomPath, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
