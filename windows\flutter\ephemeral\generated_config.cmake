# Generated code do not commit.
file(TO_CMAKE_PATH "E:\\fvm\\versions\\3.19.0" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\Flutter_Project\\police" PROJECT_DIR)

set(FLUTTER_VERSION "1.1.5+115" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 5 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 115 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=E:\\fvm\\versions\\3.19.0"
  "PROJECT_DIR=D:\\Flutter_Project\\police"
  "FLUTTER_ROOT=E:\\fvm\\versions\\3.19.0"
  "FLUTTER_EPHEMERAL_DIR=D:\\Flutter_Project\\police\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\Flutter_Project\\police"
  "FLUTTER_TARGET=D:\\Flutter_Project\\police\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC8wNDgxN2M5OWM5ZmQ0OTU2ZjI3NTA1MjA0ZjdlMzQ0MzM1ODEwYWVkLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\Flutter_Project\\police\\.dart_tool\\package_config.json"
)
