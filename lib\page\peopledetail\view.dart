import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/widget/float_square_button.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/scaffold_widget.dart';
import 'package:police/page/peopledetail/widgets/foucs_people_info.dart';
import 'package:police/page/peopledetail/widgets/people_info.dart';
import 'package:police/page/peopledetail/widgets/people_other_info.dart';

import '../../common/model/people_detail_model.dart';
import 'index.dart';

class PeopledetailPage extends GetView<PeopledetailController> {
  PeopledetailPage(
    this.peopleDetail, {
    super.key,
    this.fromHouse = false,
  });

  final bool fromHouse;
  PeopleDetailModel peopleDetail;

  // 主视图
  Widget _buildView(BuildContext context) {
    return GetBuilder<PeopledetailController>(
        id: 'detail',
        builder: (context) {
          return controller.code == 0 && peopleDetail.id != null
              ? const LoadingWidget()
              : controller.code == 404
                  ? const HttpErrorWidget()
                  : SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(0, 60, 0, 60),
                        child: Center(
                          child: Container(
                            width: Get.width * .7,
                            decoration: BoxDecoration(
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(12)),
                              color: peopleDetail.isKeyPersonnel
                                  ? Colors.red.withOpacity(.2)
                                  : const Color(0xca073773),
                            ),
                            margin: const EdgeInsets.fromLTRB(0, 0, 0, 50),
                            padding: const EdgeInsets.symmetric(
                                vertical: 30, horizontal: 50),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                PeopleInfo(
                                  controller: controller,
                                  detail: peopleDetail,
                                  fromHouse: fromHouse,
                                ),
                                const SizedBox(height: 30),
                                PeopleOtherInfo(
                                  controller: controller,
                                  detail: peopleDetail,
                                ),
                                const SizedBox(height: 30),
                                FocusPeopleInfo(
                                  controller: controller,
                                  detail: peopleDetail,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
        });
  }

  @override
  Widget build(BuildContext context) {
    PeopleDetailModel newDetail = PeopleDetailModel();
    return GetBuilder<PeopledetailController>(
      init: PeopledetailController(),
      id: "peopledetail",
      builder: (contro) {
        if (peopleDetail.id != null) {
          controller.getHouse(peopleDetail.id!);
        }
        return ScaffoldWidget(
          body: _buildView(context),
          floatButton: [
            Padding(
              padding: const EdgeInsets.all(36),
              child: Column(
                children: [
                  Visibility(
                    visible: peopleDetail.id != null,
                    child: FloatSquareButton(
                      onTap: () => controller.deletePeople(
                          context, peopleDetail.id!, peopleDetail.name!),
                      icon: Icons.delete,
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(height: 10),
                  FloatSquareButton(
                    onTap: () async {
                      // if (peopleDetail.name == null ||
                      //     peopleDetail.name!.isEmpty) {
                      //   showToast('姓名不能为空');
                      //   return;
                      // }
                      newDetail = await contro.save(
                          context, peopleDetail.id, peopleDetail.toJson());
                      // controller.isSave = true;
                      if (peopleDetail.id == null) {
                        peopleDetail = PeopleDetailModel();
                        controller.update(['peopledetail']);
                      }
                    },
                    icon: FontAwesomeIcons.floppyDisk,
                  ),
                ],
              ),
            ),
          ],
          backResult: newDetail,
          // canBack: controller.isSave,
          // onBack: () {
          //   print(controller.isSave);
          //   if (!controller.isSave) {
          //     showDialog(
          //         context: context,
          //         builder: (_) {
          //           return UnconstrainedBox(
          //             child: CornerCard(
          //                 width: 500,
          //                 child: Material(
          //                   color: Colors.transparent,
          //                   child: Column(
          //                     crossAxisAlignment: CrossAxisAlignment.start,
          //                     children: [
          //                       const Text(
          //                         '修改未保存',
          //                         style: TextStyle(
          //                           fontSize: 20,
          //                           color: Colors.white,
          //                           fontWeight: FontWeight.bold,
          //                         ),
          //                       )
          //                     ],
          //                   ),
          //                 )),
          //           );
          //         });
          //   }
          // },
        );
      },
    );
  }
}
