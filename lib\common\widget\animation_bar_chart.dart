import 'package:flutter/material.dart';

class AnimatedBar<PERSON>hart extends StatefulWidget {
  final List<SortChartModel> datas;
  final TextStyle valueStyle;
  final TextStyle labelStyle;
  final bool hideZeroValues;
  final double maxBarSpacing;

  const AnimatedBarChart({
    super.key,
    required this.datas,
    this.valueStyle = const TextStyle(color: Colors.white, fontSize: 12),
    this.labelStyle = const TextStyle(color: Color(0xff4994ec), fontSize: 12),
    this.hideZeroValues = false,
    this.maxBarSpacing = 50,
  });

  @override
  _AnimatedBarChartState createState() => _AnimatedBarChartState();
}

class _AnimatedBarChartState extends State<AnimatedBarChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedBarChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.datas != widget.datas) {
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double availableHeight = constraints.maxHeight;
        double barHeight = 20.0; // 固定柱形的高度
        double minBarSpacing = 5.0; // 柱形的最小间距
        // double maxBarSpacing = 50.0; // 柱形的最大间距

        // 计算所有柱形和间隔需要的总高度
        double totalBarsHeight = widget.datas.length * barHeight;
        double remainingHeight = availableHeight - totalBarsHeight;
        double barSpacing = remainingHeight / (widget.datas.length + 1);

        // Ensure bar spacing is within the defined min and max limits
        barSpacing = barSpacing.clamp(minBarSpacing, widget.maxBarSpacing);

        // Calculate the total height needed for the bars and spacing
        double totalHeightNeeded =
            totalBarsHeight + (widget.datas.length + 1) * barSpacing;

        Widget chart = CustomPaint(
          size: Size(double.infinity, totalHeightNeeded),
          painter: BarChartPainter(
            _animation,
            widget.datas,
            labelStyle: widget.labelStyle,
            valueStyle: widget.valueStyle,
            hideZeroValues: widget.hideZeroValues,
          ),
        );

        // Wrap in SingleChildScrollView if total height exceeds available height
        if (totalHeightNeeded > availableHeight) {
          return ScrollConfiguration(
            behavior: const ScrollBehavior().copyWith(scrollbars: false),
            child: SingleChildScrollView(child: chart),
          );
        } else {
          return chart;
        }
      },
    );
  }
}

class BarChartPainter extends CustomPainter {
  final Animation<double> animation;
  final List<SortChartModel> datas;
  final TextStyle? valueStyle;
  final TextStyle? labelStyle;
  final bool hideZeroValues;

  BarChartPainter(
    this.animation,
    this.datas, {
    this.labelStyle,
    this.valueStyle,
    this.hideZeroValues = false,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final List<SortChartModel> displayData =
        hideZeroValues ? datas.where((data) => data.count > 0).toList() : datas;

    if (displayData.isEmpty) {
      // If there are no data, draw a placeholder or message
      final textPainter = TextPainter(
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      );
      textPainter.text = const TextSpan(text: '');
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          (size.width - textPainter.width) / 2,
          (size.height - textPainter.height) / 2,
        ),
      );
      return;
    }

    // 可用高度，给顶部和底部留出一些空间
    double availableHeight = size.height - 40.0;
    double barHeight = 20.0; // 固定柱形的高度
    double minBarSpacing = 5.0; // 柱形的最小间距
    double maxBarSpacing = 50.0; // 柱形的最大间距

    // 根据数据的数量动态计算柱形的间隔，使其正好占满高度
    double totalBarsHeight = displayData.length * barHeight; // 所有柱形的总高度
    double remainingHeight = availableHeight - totalBarsHeight; // 剩余空间用于间隔
    double barSpacing;

    if (remainingHeight / (displayData.length + 1) > maxBarSpacing) {
      // 如果剩余高度可以给足每个柱形的最大间距
      barSpacing = maxBarSpacing;
    } else {
      // 否则，计算出新的间距
      barSpacing = (remainingHeight / displayData.length)
          .clamp(minBarSpacing, maxBarSpacing);
    }

    final linePaint = Paint()
      ..color = Colors.grey
      ..strokeWidth = 1.0;

    final textPainter = TextPainter(
      textAlign: TextAlign.right, // 右对齐
      textDirection: TextDirection.ltr,
    );

    final valueTextStyle = valueStyle;

    // 计算标签的最大宽度
    double maxLabelWidth = 0;
    for (var data in displayData) {
      textPainter.text = TextSpan(
        text: data.label,
        style: labelStyle,
      );
      textPainter.layout();
      if (textPainter.width > maxLabelWidth) {
        maxLabelWidth = textPainter.width;
      }
    }

    final lineOffset = maxLabelWidth + 30.0; // 左侧坐标线距离左边的偏移量，位于最长标签后面

    // 绘制左侧坐标线
    canvas.drawLine(
      Offset(lineOffset, 0),
      Offset(lineOffset, size.height),
      linePaint,
    );

    // 底部坐标线数值（取数据中的最大值向上取整）
    double maxValue = displayData
        .map((e) => e.count)
        .reduce((a, b) => a > b ? a : b)
        .toDouble();
    if (maxValue == 0) {
      maxValue = 1; // 如果所有值都是0，设置最大值为1
    } else {
      // 向上取整到最接近的10的倍数
      maxValue = (maxValue / 10).ceil() * 10;
    }

    // 绘制底部坐标线
    canvas.drawLine(
      Offset(lineOffset, size.height), // 底部线的起点（与左侧垂直线的末端对齐）
      Offset(size.width - 10, size.height), // 底部线的终点
      linePaint,
    );

    // 绘制底部最大值刻度
    textPainter.text = TextSpan(
      text: maxValue.toStringAsFixed(0),
      style: labelStyle,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(size.width - 20, size.height), // 底部最大值刻度的偏移
    );

    final maxBarLength = size.width - maxLabelWidth - 30; // 为了留出间距和标签空间

    for (int i = 0; i < displayData.length; i++) {
      final barYPosition = (i + 1) * barSpacing + i * barHeight; // 每个柱形的位置

      // 根据动画值调整柱子的长度，并动态计算最大值
      final barLength =
          (displayData[i].count / maxValue) * maxBarLength * animation.value;

      // 绘制柱形
      final paint = Paint()
        ..color = Color(displayData[i].color) // 使用自定义颜色
        ..style = PaintingStyle.fill;

      canvas.drawRect(
        Rect.fromLTWH(
            lineOffset, barYPosition, barLength, barHeight), // 矩形柱子，留出左侧空间
        paint,
      );

      // 绘制垂直标签
      textPainter.text = TextSpan(
        text: displayData[i].label,
        style: labelStyle,
      );
      textPainter.layout();
      textPainter.paint(
          canvas,
          Offset(
              lineOffset - textPainter.width - 10, barYPosition)); // 标签位置调整为右对齐

      // 绘制柱形上的数值标签
      String barValueText = displayData[i].count.toStringAsFixed(0);
      textPainter.text = TextSpan(
        text: barValueText,
        style: valueTextStyle,
      );
      textPainter.layout();

      // 判断数值标签的宽度与柱形图的长度，决定绘制位置
      double valueXPosition;
      if (textPainter.width + 5 < barLength) {
        // 数字宽度小于柱形长度，绘制在柱形内部
        valueXPosition = lineOffset + barLength - textPainter.width - 5;
      } else {
        // 数字宽度大于柱形长度，绘制在柱形外部
        valueXPosition = lineOffset + barLength + 5;
      }

      textPainter.paint(canvas, Offset(valueXPosition, barYPosition)); // 数值标签位置
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class SortChartModel {
  String label;
  int count;
  int color;

  SortChartModel(this.label, this.count, this.color);
}
