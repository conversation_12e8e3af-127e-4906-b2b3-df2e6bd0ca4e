import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/widget/empty_widget.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/keep_alive_widget.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/scaffold_widget.dart';
import 'package:police/page/analyze/widgets/float_button.dart';
import 'package:police/page/analyze/widgets/table_data.dart';
import 'package:police/page/analyze/widgets/table_header.dart';

import '../../common/widget/turn_page.dart';
import 'index.dart';

class AnalyzePage extends GetView<AnalyzeController> {
  const AnalyzePage({super.key});

  // 主视图
  Widget _buildView(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 80, 60, 20),
            child: InkWell(
              onTapDown: (details) => _showMenu(context, details, controller),
              child: GetBuilder<AnalyzeController>(
                id: 'type',
                builder: (context) {
                  return Text(
                    '筛选：${analyzeSortTypesStr[controller.currentType]}',
                    style: const TextStyle(fontSize: 20, color: Colors.white),
                  );
                },
              ),
            ),
          ),
          controller.code == 0
              ? const LoadingWidget()
              : controller.code == 404
                  ? const HttpErrorWidget()
                  : controller.code == 200 && controller.analyzeList.isEmpty
                      ? const EmptyWidget()
                      : Column(
                          children: [
                            const AnalyzeTableHeader(),
                            const AnalyzeTableData(),
                            const SizedBox(height: 50),
                            GetBuilder<AnalyzeController>(
                                id: 'turnpage',
                                builder: (context) {
                                  return Visibility(
                                    visible: controller.analyzeList.isNotEmpty,
                                    child: Align(
                                      alignment: Alignment.bottomCenter,
                                      child: TurnPage(
                                        itemCount: controller.maxpages,
                                        currentIndex: controller.currentPage,
                                        onTap: (index, total) =>
                                            controller.turnPage(index, total),
                                      ),
                                    ),
                                  );
                                }),
                            const SizedBox(height: 50),
                          ],
                        ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return KeepAliveWidget(
      child: GetBuilder<AnalyzeController>(
        init: AnalyzeController(),
        id: "analyze",
        builder: (_) {
          return ScaffoldWidget(
            body: _buildView(context),
            strTitle: '警情研判',
            floatButton: const [AnalyzeFloatButton()],
          );
        },
      ),
    );
  }

  void _showMenu(BuildContext context, TapDownDetails details,
      AnalyzeController controller) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    if (controller.loading) {
      showToast('请先等待加载完成');
      return;
    }
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        details.globalPosition.dx,
        details.globalPosition.dy,
        overlay.size.width - details.globalPosition.dx,
        overlay.size.height - details.globalPosition.dy,
      ),
      items: List.generate(analyzeSortTypesStr.keys.length, (index) {
        final key = analyzeSortTypesStr.keys.elementAt(index);
        return PopupMenuItem(
            value: key, child: Text(analyzeSortTypesStr[key]!));
      }),
    ).then((value) {
      if (value != null) {
        controller.currentPage = 1;
        controller.loading = true;
        controller.currentType = value;
        controller.update(['type']);
        controller.getAnalyze();
      }
    });
  }
}
