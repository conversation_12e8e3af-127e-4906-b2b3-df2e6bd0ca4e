import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:police/common/model/dropdown_menu_item_model.dart';

class DropdownWidget extends StatelessWidget {
  const DropdownWidget({
    super.key,
    required this.hint,
    required this.selectedValue,
    required this.items,
    required this.onChanged,
  });

  final String hint;
  final dynamic selectedValue;
  final List<DropdownMenuItemModel> items;
  final Function(dynamic) onChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
          image: DecorationImage(
        image: AssetImage('assets/pic/234-2.png'),
        fit: BoxFit.fill,
      )),
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: DropdownButtonHideUnderline(
          child: DropdownButton2(
        hint: Text(
          hint,
          style: const TextStyle(color: Color(0xd8cfe0ff)),
        ),
        value: selectedValue,
        items: items
            .map((e) => DropdownMenuItem(
                  value: e.value,
                  child: Text(
                    e.text,
                    style: const TextStyle(color: Color(0xf0e5edff)),
                  ),
                ))
            .toList(),
        dropdownStyleData: DropdownStyleData(
          decoration: BoxDecoration(
            color: const Color(0x1a000000),
            borderRadius: BorderRadius.circular(5),
          ),
        ),
        onChanged: (value) => onChanged(value),
      )),
    );
  }
}
