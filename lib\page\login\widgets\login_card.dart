import 'package:flutter/material.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/page/login/index.dart';

class LoginCard extends StatelessWidget {
  const LoginCard({super.key, required this.controller});

  final LoginController controller;

  @override
  Widget build(BuildContext context) {
    // return FlipCard(
    //   key: cardKey,
    //   front: _buildLoginChild(context, cardKey),
    //   back: _buildSignupChild(context, cardKey),
    // );
    return _buildLoginChild(context);
  }

  Widget _buildLoginChild(
    BuildContext context,
    // GlobalKey<FlipCardState> key,
  ) {
    TextEditingController server = TextEditingController(text: Apis.baseUrl);
    TextEditingController username = TextEditingController();
    TextEditingController password = TextEditingController();
    TextEditingController key = TextEditingController();
    return Container(
      width: 400,
      height: 600,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/pic/3312.png'),
          fit: BoxFit.fitWidth, // 使用BoxFit.cover使背景图片填满整个容器
        ),
      ),
      padding: const EdgeInsets.all(50),
      child: Column(
        children: [
          InputText(
            lableText: '服务器地址',
            controller: server,
            isDense: false,
            hint: 'xxx.xxx.x.xxx',
            focusedBorder: _inputBorder(),
            enabledBorder: _inputBorder(),
            labelStyle: const TextStyle(
              color: Color(0xff00caee), // 设置标签文本的颜色
            ),
            fillColor: const Color(0xff084a89),
          ),
          const SizedBox(height: 20),
          InputText(
            lableText: '密钥',
            controller: key,
            isDense: false,
            obscureText: true,
            focusedBorder: _inputBorder(),
            enabledBorder: _inputBorder(),
            labelStyle: const TextStyle(
              color: Color(0xff00caee), // 设置标签文本的颜色
            ),
            fillColor: const Color(0xff084a89),
          ),
          const SizedBox(height: 20),
          InputText(
            lableText: '账号',
            maxLength: 10,
            controller: username,
            isDense: false,
            focusedBorder: _inputBorder(),
            enabledBorder: _inputBorder(),
            labelStyle: const TextStyle(
              color: Color(0xff00caee), // 设置标签文本的颜色
            ),
            fillColor: const Color(0xff084a89),
          ),
          const SizedBox(height: 20),
          InputText(
            lableText: '密码',
            maxLength: 16,
            controller: password,
            isDense: false,
            obscureText: true,
            focusedBorder: _inputBorder(),
            enabledBorder: _inputBorder(),
            labelStyle: const TextStyle(
              color: Color(0xff00caee), // 设置标签文本的颜色
            ),
            fillColor: const Color(0xff084a89),
          ),

          const Spacer(),
          // NormolButton(
          //   lable: '去注册',
          //   color: Colors.transparent,
          //   onTap: () => key.currentState!.toggleCard(),
          // ),
          // const SizedBox(height: 20),
          NormolButton(
            lable: '登录',
            onTap: () => controller.login(
              context,
              server.text.trim(),
              username.text.trim(),
              password.text.trim(),
              key.text.trim(),
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildSignupChild(BuildContext context, GlobalKey<FlipCardState> key) {
  //   TextEditingController server = TextEditingController(text: Apis.baseUrl);
  //   TextEditingController username = TextEditingController();
  //   TextEditingController password = TextEditingController();
  //   TextEditingController password2 = TextEditingController();
  //   return Container(
  //     width: 400,
  //     height: 600,
  //     decoration: const BoxDecoration(
  //       image: DecorationImage(
  //         image: AssetImage('assets/pic/3312.png'),
  //         fit: BoxFit.fitWidth, // 使用BoxFit.cover使背景图片填满整个容器
  //       ),
  //     ),
  //     padding: const EdgeInsets.all(50),
  //     child: Column(
  //       children: [
  //         InputText(
  //           lableText: '服务器地址',
  //           controller: server,
  //           isDense: false,
  //           hint: 'xxx.xxx.x.xxx',
  //           focusedBorder: _inputBorder(),
  //           enabledBorder: _inputBorder(),
  //           labelStyle: const TextStyle(
  //             color: Color(0xff00caee), // 设置标签文本的颜色
  //           ),
  //           fillColor: const Color(0xff084a89),
  //         ),
  //         const SizedBox(height: 20),
  //         InputText(
  //           lableText: '账号',
  //           controller: username,
  //           isDense: false,
  //           maxLength: 10,
  //           focusedBorder: _inputBorder(),
  //           enabledBorder: _inputBorder(),
  //           labelStyle: const TextStyle(
  //             color: Color(0xff00caee), // 设置标签文本的颜色
  //           ),
  //           fillColor: const Color(0xff084a89),
  //         ),
  //         const SizedBox(height: 20),
  //         InputText(
  //           lableText: '密码',
  //           maxLength: 16,
  //           isDense: false,
  //           controller: password,
  //           obscureText: true,
  //           focusedBorder: _inputBorder(),
  //           enabledBorder: _inputBorder(),
  //           labelStyle: const TextStyle(
  //             color: Color(0xff00caee), // 设置标签文本的颜色
  //           ),
  //           fillColor: const Color(0xff084a89),
  //         ),
  //         const SizedBox(height: 20),
  //         InputText(
  //           lableText: '重复密码',
  //           maxLength: 16,
  //           controller: password2,
  //           isDense: false,
  //           obscureText: true,
  //           focusedBorder: _inputBorder(),
  //           enabledBorder: _inputBorder(),
  //           labelStyle: const TextStyle(
  //             color: Color(0xff00caee), // 设置标签文本的颜色
  //           ),
  //           fillColor: const Color(0xff084a89),
  //         ),
  //         const Spacer(),
  //         NormolButton(
  //           lable: '注册',
  //           onTap: () => controller.signup(
  //             context,
  //             username.text.trim(),
  //             password.text.trim(),
  //             password2.text.trim(),
  //           ),
  //         ),
  //         const SizedBox(height: 20),
  //         NormolButton(
  //           lable: '去登录',
  //           color: Colors.transparent,
  //           onTap: () => key.currentState!.toggleCard(),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  OutlineInputBorder _inputBorder() {
    return const OutlineInputBorder(
      borderSide: BorderSide(
        color: Color(0xff00bcdd), // 聚焦时的边框颜色
        width: 2.0,
      ),
    );
  }
}
