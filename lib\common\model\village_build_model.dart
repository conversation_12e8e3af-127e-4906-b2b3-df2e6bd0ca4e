class VillageBuildModel {
  int? id;
  int? villageAreaId;
  String? name;
  DateTime? createTime;

  VillageBuildModel({
    this.id,
    this.villageAreaId,
    this.name,
    this.createTime,
  });

  factory VillageBuildModel.fromJson(Map<String, dynamic> json) {
    return VillageBuildModel(
      id: json['id'] as int?,
      villageAreaId: json['village_area_id'] as int?,
      name: json['name'] as String?,
      createTime: json['create_time'] == null
          ? null
          : DateTime.parse(json['create_time'] as String),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'village_area_id': villageAreaId,
        'name': name,
        'create_time': createTime?.toIso8601String(),
      };
}
