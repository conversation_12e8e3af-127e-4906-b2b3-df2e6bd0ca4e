import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/corner_card.dart';
import 'package:police/page/foucs_peoples/index.dart';
import 'package:police/page/peoples/index.dart';

class FocusDataGrid extends StatelessWidget {
  const FocusDataGrid({
    super.key,
    required this.controller,
    required this.crossAxisCount,
  });

  final FoucsPeoplesController controller;
  final int crossAxisCount;
  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: List.generate(controller.focusData.length, (index) {
        return InkWell(
          onTap: () async {
            await Get.find<PeoplesController>().getPeopleList(
              page: 1,
              personType: controller.focusData[index]['name'],
              fromFoucs: true,
              clear: false,
            );
            jump2page(
              PeoplesPage(
                fromFoucs: true,
                personType: controller.focusData[index]['name'],
              ),
            );
          },
          child: Corner<PERSON>ard(
            width: 120,
            height: 120,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '共 ${controller.focusData[index]['count']} 人',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
                Text(
                  controller.focusData[index]['name'],
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
