import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/slide_animation_widget.dart';
import 'package:police/page/analyze/index.dart';
import 'package:police/page/analyzeDetail/index.dart';

import '../../../common/model/analyze_model.dart';

class AnalyzeTableData extends StatelessWidget {
  const AnalyzeTableData({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AnalyzeController>(
      id: 'data',
      builder: (controller) {
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: controller.analyzeList.length,
          itemBuilder: (_, index) {
            return SlideAnimationWidget(
              actions: [
                InkWell(
                  // onTap: () => controller.deleteCase(
                  //   context,
                  //   index,
                  //   id: controller.cases[index].id,
                  //   name: controller.cases[index].title,
                  // ),
                  onTap: () => controller.deleteAnalyze(
                      context,
                      controller.analyzeList[index].id!,
                      controller.analyzeList[index].yearMonthDay ?? '',
                      index),
                  child: Container(
                    height: 50,
                    color: Colors.red,
                    alignment: Alignment.center,
                    child: const Icon(Icons.delete, color: Colors.white),
                  ),
                ),
                InkWell(
                  onTap: () => jump2page(
                      AnalyzedetailPage(controller.analyzeList[index])),
                  child: Container(
                    height: 50,
                    color: Colors.blue,
                    alignment: Alignment.center,
                    child: const Icon(Icons.list, color: Colors.white),
                  ),
                ),
              ],
              child: Row(
                children: List.generate(
                  2,
                  (i) {
                    List<String> date =
                        controller.analyzeList[index].yearMonthDay!.split('-');
                    String year = date[0];
                    String month = date[1];
                    String day = date[2];
                    return Expanded(
                        child: Container(
                      height: 50,
                      color: index % 2 == 1
                          ? const Color(0xd3021a37)
                          : const Color(0xd804254c),
                      alignment: Alignment.center,
                      margin: const EdgeInsets.all(.5),
                      child: Text(
                        i == 0
                            ? controller.currentType == AnalyzeSortTypes.year
                                ? year
                                : controller.currentType ==
                                        AnalyzeSortTypes.month
                                    ? '$year-$month'
                                    : controller.currentType ==
                                            AnalyzeSortTypes.day
                                        ? '$year-$month-$day'
                                        : _getQuarter(
                                            controller.analyzeList[index],
                                            year,
                                            month)
                            : controller.analyzeList[index].graphics ?? '',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ));
                  },
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 获取第几季度
  String _getQuarter(AnalyzeModel detail, String year, String month) {
    String str = '';

    switch (month) {
      case '01' || '1' || '02' || '2' || '03' || '3':
        str = '一';
      case '04' || '4' || '05' || '5' || '06' || '6':
        str = '二';
      case '07' || '7' || '08' || '8' || '09' || '9':
        str = '三';
      default:
        str = '四';
    }

    return '（$year）第$str季度';
  }
}
