import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/enums.dart';

jump2page(Widget page,
    {dynamic args,
    JumpPageType jumpPageType = JumpPageType.to,
    Function(dynamic result)? onBack}) {
  switch (jumpPageType) {
    case JumpPageType.off:
      Get.off(page, transition: Transition.fadeIn, arguments: args)!
          .then((result) => onBack?.call(result));
      break;
    case JumpPageType.offAll:
      Get.offAll(page, transition: Transition.fadeIn, arguments: args)!
          .then((result) => onBack?.call(result));
      break;
    default:
      Get.to(page, transition: Transition.fadeIn, arguments: args)!
          .then((result) => onBack?.call(result));
  }
}

pageback({dynamic result}) => Get.back(result: result);

/// 新窗口
// newWindow(String title) async {
//   return await DesktopMultiWindow.createWindow()
//     ..setFrame(Offset.zero & const Size(1280, 720))
//     ..center()
//     ..setTitle(title)
//     ..show();
// }
