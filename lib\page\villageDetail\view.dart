import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/scaffold_widget.dart';
import 'package:police/page/villageDetail/widgets/float_button.dart';
import 'package:police/page/villageDetail/widgets/tab_bar_title.dart';
import 'index.dart';
import 'widgets/place.dart';
import 'widgets/police.dart';
import 'widgets/special_person.dart';
import 'widgets/village_intro.dart';
import 'widgets/work_group.dart';

class VillagedetailPage extends GetView<VillagedetailController> {
  const VillagedetailPage({super.key, this.id});

  final int? id;

  // 主视图
  Widget _buildView() {
    return controller.code == 0 && id != null
        ? const Center(child: LoadingWidget())
        : controller.code == 404 && id != null
            ? const Center(child: HttpErrorWidget())
            : SingleChildScrollView(
                controller: controller.scrollController,
                physics: const BouncingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.all(60),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const VillageIntro(),
                      const SizedBox(height: 100),
                      Visibility(
                        visible: id != null,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Police(),
                            SpecialPerson(
                              specialPerson:
                                  controller.villageDetail.personInterest ?? [],
                              specialPersonDetail: controller
                                      .villageDetail.personInterestDetails ??
                                  [],
                            ),
                            const SizedBox(height: 100),
                            Place(place: controller.villageDetail.place ?? []),
                            const SizedBox(height: 100),
                            WorkGroup(
                              workgroup:
                                  controller.villageDetail.organize ?? [],
                              // detail: controller.villageDetail,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
  }

  @override
  Widget build(BuildContext context) {
    if (id != null) Get.put(VillagedetailController()).getVillageDetail(id!);

    return GetBuilder<VillagedetailController>(
      init: VillagedetailController(),
      id: VillageDetailControllerIDS.villageDetail,
      builder: (_) {
        return ScaffoldWidget(
          body: _buildView(),
          floatButton: [
            FloatButton(controller: controller, id: id),
          ],
          title: TabbarTitle(controller: controller),
        );
      },
    );
  }
}
