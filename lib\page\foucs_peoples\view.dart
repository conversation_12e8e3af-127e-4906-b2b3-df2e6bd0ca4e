import 'dart:math';

import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/scaffold_widget.dart';

import 'index.dart';
import '../../common/widget/pie_chart.dart';
import 'widgets/focsu_data_grid.dart';

class FoucsPeoplesPage extends GetView<FoucsPeoplesController> {
  const FoucsPeoplesPage({super.key});

  // 主视图
  Widget _buildView(BuildContext context, int crossAxisCount) {
    if (controller.focusData.isEmpty) {
      return const LoadingWidget();
    }
    if (controller.code == 404) {
      return const HttpErrorWidget();
    }

    // for (var item in controller.focusData) {
    //   item['count'] = item['count'] + Random().nextInt(100);
    // }
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 60, 0, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          PieChart([
            charts.Series<Map<String, dynamic>, String>(
              id: 'focusPeople',
              data: controller.focusData
                  .where((item) => (item['count'] as num) > 0)
                  .toList(),
              domainFn: (Map<String, dynamic> item, _) =>
                  "${item['name']} (${item['count']})",
              measureFn: (Map<String, dynamic> item, _) => item['count'] as num,
              colorFn: (Map<String, dynamic> item, _) =>
                  _setColor(item['name']),
            )
          ]),
          // const SizedBox(height: 40),
          FocusDataGrid(controller: controller, crossAxisCount: crossAxisCount)
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<FoucsPeoplesController>(
      init: FoucsPeoplesController(),
      id: "foucs_peoples",
      builder: (_) {
        return ScaffoldWidget(
          strTitle: '重点人员',
          body: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
              final crossAxisCount = (constraints.maxWidth / 570).round();
              return GetBuilder<FoucsPeoplesController>(
                init: FoucsPeoplesController(),
                id: "peoples",
                builder: (_) {
                  return _buildView(context, crossAxisCount);
                },
              );
            },
          ),
        );
      },
    );
  }

  charts.Color _setColor(String name) {
    switch (name) {
      case '刑满释放':
        return charts.Color.fromHex(code: '#a2d2ff');
      case '吸毒涉毒':
        return charts.Color.fromHex(code: '#809bce');
      case '缠访、闹访':
        return charts.Color.fromHex(code: '#a5a09a');
      case '监视居住':
        return charts.Color.fromHex(code: '#ff9f1c');
      case '取保候审':
        return charts.Color.fromHex(code: '#003f88');
      case '精神疾病':
        return charts.Color.fromHex(code: '#f3de8a');
      case '邪教':
        return charts.Color.fromHex(code: '#ff595e');
      default:
        return charts.Color.fromHex(code: '#2a9d8f');
    }
  }
}
