import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Response;
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/util/crypt_util.dart';
import 'package:police/common/util/logger.dart';
import 'package:police/common/util/shared.dart';
import 'package:police/page/login/controller.dart';
import '../widget/corner_card.dart';
import '../widget/input_text.dart';
import '../widget/normol_button.dart';
import '../widget/scale_animated_widget.dart';

class Https {
  static late Dio _dio;
  static late CancelToken _cancelToken;
  // static int code = 0;

  static _init({
    Map<String, dynamic>? headers,
    String? contentType = 'application/x-www-form-urlencoded',
    bool pageBack = false,
    Function(Response)? fail,
  }) {
    bool isLogin = Get.find<LoginController>().isLogin;
    String token = Get.find<LoginController>().token;
    BaseOptions options = BaseOptions(
      baseUrl: Apis.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      contentType: contentType,
      headers: isLogin ? {'Authorization': 'Bearer $token'} : {},
    );
    _dio = Dio(options);
    _cancelToken = CancelToken();
    // Error 拦截器
    // _dio.interceptors.add(ErrorInterceptor(pageBack: pageBack, fail: fail));
    _dio.interceptors.add(InterceptorsWrapper(
      onError: (DioException e, handler) async {
        if (e.response != null) {
          if (pageBack) pageback();
          if (e.response!.statusCode == 404) {
            showToast('请求地址不存在', backgroundColor: Colors.red);
            fail?.call(e.response!);
            return;
          } else if (e.response!.statusCode.toString().startsWith('5')) {
            showToast('服务器异常', backgroundColor: Colors.red);
            fail?.call(e.response!);
            return;
          } else {
            if (e.response!.data != null) {
              try {
                final results = await CryptUtil.decrypt(
                    e.response!.data['data'], e.response!.data['iv']);

                logger.e('请求异常: $results');
                showToast('${json.decode(results)['detail']}',
                    backgroundColor: Colors.red);
              } catch (e) {
                showToast('解密失败，请重新输入密钥');
                _showKeyErrorDialog();
                return;
              }
            } else {
              showToast('请求异常: ${e.message}', backgroundColor: Colors.red);
            }

            fail?.call(e.response!);
            return;
          }
        } else {
          showToast('请求异常: ${e.message}', backgroundColor: Colors.red);
          fail?.call(Response(
            requestOptions: e.requestOptions,
            statusCode: 500, // 可以定义一个默认的错误码
            statusMessage: 'Unknown Error',
          ));
        }

        if (pageBack) pageback();
        handler.next(e);
      },
    ));
  }

  static Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    bool pageBack = false,
    String? contentType,
    Function(Response)? fail,
  }) async {
    _init(
      headers: headers,
      pageBack: pageBack,
      fail: fail,
      contentType: contentType,
    );
    logger.d('get 请求$path');
    Response response = await _dio.get(path, queryParameters: queryParameters);

    if (pageBack) pageback();

    String results = '';

    try {
      results =
          await CryptUtil.decrypt(response.data['data'], response.data['iv']);

      logger.i('get 请求结果$path: $results');

      return Response(
          requestOptions: response.requestOptions, data: json.decode(results));
    } catch (e) {
      showToast('解密失败，请重新输入密钥');
      // jump2page(const SplashPage(keyError: true),
      //     jumpPageType: JumpPageType.offAll);
      _showKeyErrorDialog();
      // getx.Get.put(UserController()).sureLogout();

      return Response(requestOptions: response.requestOptions);
    }
  }

  static Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    String? contentType,
    bool pageBack = false,
    Function(int, int)? onSendProgress,
    Function(Response)? fail,
  }) async {
    _init(
        headers: headers,
        contentType: contentType,
        pageBack: pageBack,
        fail: fail);
    // code = 0;

    logger.d('post 请求$path: $data');

    Response response = await _dio.post(
      path,
      data: data,
      queryParameters: queryParameters,
      onSendProgress: (count, total) => onSendProgress?.call(count, total),
    );
    // code = 200;
    if (pageBack) pageback();
    String results = '';
    try {
      results =
          await CryptUtil.decrypt(response.data['data'], response.data['iv']);
      logger.i('post 请求结果$path: $results');
      return Response(
          requestOptions: response.requestOptions, data: json.decode(results));
    } catch (e) {
      showToast('解密失败，请重新输入密钥');
      // jump2page(const SplashPage(keyError: true),
      //     jumpPageType: JumpPageType.offAll);
      _showKeyErrorDialog();
      // getx.Get.put(UserController()).sureLogout();
      return Response(requestOptions: response.requestOptions);
    }
  }

  static Future<Response> put(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    bool pageBack = false,
    Function(int, int)? onSendProgress,
    Function(Response)? fail,
  }) async {
    _init(
      headers: headers,
      pageBack: pageBack,
      fail: fail,
      contentType: 'application/x-www-form-urlencoded',
    );
    // code = 0;

    logger.d('put 请求$path: $data');

    Response response = await _dio.put(
      path,
      data: data,
      queryParameters: queryParameters,
      onSendProgress: (count, total) => onSendProgress?.call(count, total),
    );
    // code = 200;
    if (pageBack) pageback();
    String results = '';

    try {
      results =
          await CryptUtil.decrypt(response.data['data'], response.data['iv']);
      logger.i('put 请求结果$path: $results');
      return Response(
          requestOptions: response.requestOptions, data: json.decode(results));
    } catch (e) {
      showToast('解密失败，请重新输入密钥');
      // jump2page(const SplashPage(keyError: true),
      //     jumpPageType: JumpPageType.offAll);
      _showKeyErrorDialog();

      // getx.Get.put(UserController()).sureLogout();
      return Response(requestOptions: response.requestOptions);
    }
  }

  static Future<Response> delete(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    bool pageBack = false,
    Function(Response)? fail,
  }) async {
    _init(headers: headers, pageBack: pageBack, fail: fail);
    // code = 0;

    logger.d('delete 请求$path: $data');

    Response response =
        await _dio.delete(path, data: data, queryParameters: queryParameters);
    if (pageBack) pageback();
    // code = 200;
    String results = '';

    try {
      results =
          await CryptUtil.decrypt(response.data['data'], response.data['iv']);
      logger.i('delete 请求结果$path: $results');
      return Response(
          requestOptions: response.requestOptions, data: json.decode(results));
    } catch (e) {
      showToast('解密失败，请重新输入密钥');
      // jump2page(const SplashPage(keyError: true),
      //     jumpPageType: JumpPageType.offAll);
      _showKeyErrorDialog();

      // getx.Get.put(UserController()).sureLogout();
      return Response(requestOptions: response.requestOptions);
    }
  }

  static Future<Response> patch(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    bool pageBack = false,
    Function(int, int)? onSendProgress,
    Function(Response)? fail,
  }) async {
    _init(
      headers: headers,
      contentType: 'application/json',
      pageBack: pageBack,
      fail: fail,
    );
    logger.d('patch 请求$path: $data');

    Response response = await _dio.patch(
      path,
      data: data,
      queryParameters: queryParameters,
      onSendProgress: (count, total) => onSendProgress?.call(count, total),
    );
    if (pageBack) pageback();
    String results = '';

    try {
      results =
          await CryptUtil.decrypt(response.data['data'], response.data['iv']);
      logger.i('patch 请求结果$path: $results');
      return Response(
          requestOptions: response.requestOptions, data: json.decode(results));
    } catch (e) {
      showToast('解密失败，请重新输入密钥');
      // jump2page(const SplashPage(keyError: true),
      //     jumpPageType: JumpPageType.offAll);
      _showKeyErrorDialog();

      // getx.Get.put(UserController()).sureLogout();
      return Response(requestOptions: response.requestOptions);
    }
  }

  static void cancel() => _cancelToken.cancel();

  static void _showKeyErrorDialog() {
    Get.generalDialog(
      barrierDismissible: true,
      barrierLabel: '',
      pageBuilder: (context, animation, secondaryAnimation) {
        TextEditingController key = TextEditingController();
        return UnconstrainedBox(
          child: ScaleAnimatedWidget(
            begin: 0,
            end: 1,
            duration: const Duration(milliseconds: 300),
            userMouse: false,
            child: Material(
              color: Colors.transparent,
              child: CornerCard(
                width: 500,
                height: 300,
                child: Padding(
                  padding: const EdgeInsets.all(30),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '输入密钥',
                        style: TextStyle(fontSize: 20, color: Colors.white),
                      ),
                      const SizedBox(height: 20),
                      Expanded(
                        child: InputText(
                          controller: key,
                          maxLines: null,
                          isDense: false,
                          hint: '请输入密钥',
                        ),
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          NormolButton(
                            lable: '取消',
                            width: 80,
                            onTap: () => pageback(),
                          ),
                          const SizedBox(width: 10),
                          NormolButton(
                            lable: '确认',
                            width: 80,
                            color: Colors.red.withOpacity(.45),
                            onTap: () async {
                              await SharedUtil.setString(
                                  'keyStr', key.text.trim());
                              pageback();
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class ErrorInterceptor extends Interceptor {
  final bool pageBack;
  final Function(Response)? fail;

  ErrorInterceptor({
    this.pageBack = false,
    this.fail,
  });

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (pageBack) pageback();

    switch (err.type) {
      case DioExceptionType.cancel:
        fail?.call(err.response!);
        showToast('取消请求', backgroundColor: Colors.red);
        break;
      case DioExceptionType.connectionTimeout:
        fail?.call(err.response!);
        showToast('连接超时', backgroundColor: Colors.red);
        break;
      case DioExceptionType.sendTimeout:
        fail?.call(err.response!);
        showToast('发送超时', backgroundColor: Colors.red);
        break;
      case DioExceptionType.receiveTimeout:
        fail?.call(err.response!);
        showToast('接收超时', backgroundColor: Colors.red);
        break;
      case DioExceptionType.unknown:
        // 捕获所有未知错误
        debugPrint('Unknown error: ${err.toString()}');
        showToast('未知异常: ${err.message}', backgroundColor: Colors.red);
        if (err.response != null) {
          fail?.call(err.response!);
        } else {
          // 可以选择在没有 response 的情况下也调用 fail，传递空的 response
          fail?.call(Response(
            requestOptions: err.requestOptions,
            statusCode: 500, // 可以定义一个默认的错误码
            statusMessage: 'Unknown Error',
          ));
        }
        break;
      case DioExceptionType.badResponse:
        if (err.response != null && err.response!.data != null) {
          fail?.call(err.response!);
          showToast(err.response!.data['detail'].toString(),
              backgroundColor: Colors.red);
        } else {
          showToast('响应异常', backgroundColor: Colors.red);
        }
        break;
      case DioExceptionType.connectionError:
        fail?.call(err.response!);
        showToast('连接异常', backgroundColor: Colors.red);
        break;
      case DioExceptionType.badCertificate:
        fail?.call(err.response!);
        showToast('证书异常', backgroundColor: Colors.red);
        break;
      default:
        // 处理其他未覆盖的异常类型
        debugPrint('Unhandled error: ${err.toString()}');
        showToast('未处理的错误: ${err.message}', backgroundColor: Colors.red);
        fail?.call(err.response!);
        break;
    }

    // 继续将错误传递给下一个拦截器
    handler.next(err);
  }
}
