import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/page/home/<USER>';

class PoliceStationIntroductionWidget extends StatelessWidget {
  const PoliceStationIntroductionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find();
    final double screenHeight = MediaQuery.of(context).size.height;

    return LayoutBuilder(builder: (context, constraints) {
      return Container(
        width: double.infinity,
        height: Get.height,
        padding: const EdgeInsets.symmetric(vertical: 60),
        child: Stack(
          children: [
            Align(
              alignment: Alignment.bottomRight,
              child: Image.asset(
                width: 660,
                'assets/pic/image3.png',
              ),
            ),
            Align(
              alignment: Alignment.center,
              child: Container(
                constraints: BoxConstraints(
                  minHeight: screenHeight,
                ),
                child: Si<PERSON><PERSON><PERSON>(
                  width: 600,
                  height: 500,
                  child: Stack(
                    children: [
                      Container(
                        width: 600,
                        height: 500,
                        padding: const EdgeInsets.only(
                            top: 70, left: 30, right: 30, bottom: 20),
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage('assets/pic/99239.png'),
                            fit: BoxFit.fill,
                          ),
                          borderRadius: BorderRadius.all(Radius.circular(
                              8)), // Ensure this matches the ClipRRect radius
                        ),
                        child: InputText(
                          defaultText: controller.introStr,
                          maxLines: null,
                          textInputAction: TextInputAction.newline,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            height: 2,
                          ),
                          onChanged: (text) {
                            controller.introStr = text;
                            if (controller.oldIntroStr == controller.introStr) {
                              controller.introStrIsChanged = false;
                            } else {
                              controller.introStrIsChanged = true;
                            }
                            controller.update(['intro_button']);
                          },
                        ),
                      ),
                      GetBuilder<HomeController>(
                        id: 'intro_button',
                        builder: (controller) {
                          return Visibility(
                            visible: controller.introStrIsChanged,
                            child: Padding(
                              padding: const EdgeInsets.all(5),
                              child: Align(
                                alignment: Alignment.topRight,
                                child: NormolButton(
                                  width: 100,
                                  lable: '保存',
                                  onTap: () => controller.saveIntro(
                                    context,
                                    controller.introStr,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      )
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
}
