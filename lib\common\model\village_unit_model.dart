class VillageUnitModel {
  int? id;
  int? villageBuildingId;
  String? name;
  int? residents;
  int? residentsCount;
  int? merchants;
  DateTime? createTime;

  VillageUnitModel({
    this.id,
    this.villageBuildingId,
    this.name,
    this.residents,
    this.residentsCount,
    this.merchants,
    this.createTime,
  });

  factory VillageUnitModel.fromJson(Map<String, dynamic> json) =>
      VillageUnitModel(
        id: json['id'] as int?,
        villageBuildingId: json['village_building_id'] as int?,
        name: json['name'] as String?,
        residents: json['residents'] as int?,
        residentsCount: json['residents_count'] as int?,
        merchants: json['merchants'] as int?,
        createTime: json['create_time'] == null
            ? null
            : DateTime.parse(json['create_time'] as String),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'village_building_id': villageBuildingId,
        'name': name,
        'residents': residents,
        'residents_count': residentsCount,
        'merchants': merchants,
        'create_time': createTime?.toIso8601String(),
      };

  VillageUnitModel copyWith({
    int? id,
    int? villageBuildingId,
    String? name,
    int? residents,
    int? residentsCount,
    int? merchants,
    DateTime? createTime,
  }) {
    return VillageUnitModel(
      id: id ?? this.id,
      villageBuildingId: villageBuildingId ?? this.villageBuildingId,
      name: name ?? this.name,
      residents: residents ?? this.residents,
      residentsCount: residentsCount ?? this.residentsCount,
      merchants: merchants ?? this.merchants,
      createTime: createTime ?? this.createTime,
    );
  }
}
