import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:police/page/analyzeDetail/controller.dart';
import '../../../common/model/analyze_details_model/month_analyze.dart';
import '../../../common/model/analyze_model.dart';
import '../../../common/widget/corner_card.dart';

class DayChart extends StatelessWidget {
  const DayChart({super.key, required this.detail, required this.controller});

  final AnalyzedetailController controller;
  final AnalyzeModel detail;

  @override
  Widget build(BuildContext context) {
    List<MonthAnalyze> datas = controller.details.monthAnalyze ?? [];
    return CornerCard(
      height: 300,
      child: Column(
        children: [
          Text(
            '${controller.getYearMonth(detail)}每日警情分布',
            style: const TextStyle(fontSize: 25, color: Colors.white),
          ),
          Expanded(
            child: Bar<PERSON>hart(
              BarChartData(
                barTouchData: _buildBarTouchData(),
                titlesData: _buildTitles(datas),
                borderData: FlBorderData(show: false),
                barGroups: _buildBarGroups(datas),
                gridData: const FlGridData(show: false),
                alignment: BarChartAlignment.spaceAround,
                // maxY: 100.toDouble(),
              ),
            ),
          )
        ],
      ),
    );
  }

  BarTouchData _buildBarTouchData() {
    return BarTouchData(
      enabled: false,
      touchTooltipData: BarTouchTooltipData(
        getTooltipColor: (group) => Colors.transparent,
        tooltipPadding: EdgeInsets.zero,
        tooltipMargin: 8,
        getTooltipItem: (
          BarChartGroupData group,
          int groupIndex,
          BarChartRodData rod,
          int rodIndex,
        ) {
          return BarTooltipItem(
            rod.toY.round().toString(),
            const TextStyle(
              color: Colors.cyan,
              fontWeight: FontWeight.bold,
            ),
          );
        },
      ),
    );
  }

  FlTitlesData _buildTitles(List<MonthAnalyze> datas) {
    return FlTitlesData(
      show: true,
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          getTitlesWidget: (value, meta) {
            return SideTitleWidget(
              axisSide: meta.axisSide,
              child: Text(datas[value.toInt()].day ?? '',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  )),
            );
          },
        ),
      ),
    );
  }

  List<BarChartGroupData> _buildBarGroups(List<MonthAnalyze> datas) {
    return List.generate(datas.length, (index) {
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: datas[index].count!.toDouble(),
            // color: colors[index % colors.length], // 使用不同的颜色
          )
        ],
        showingTooltipIndicators: [0],
      );
    });
  }
}
