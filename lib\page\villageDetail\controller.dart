import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/people_api.dart';
import 'package:police/common/api/village_api.dart';
import 'package:police/common/model/people_detail_model.dart';
import 'package:police/common/routes/jump_page.dart';

import '../../common/dialog.dart';
import '../../common/model/village_area_model/village_area_model.dart';
import '../../common/model/village_model/village_model.dart';

class VillageDetailControllerIDS {
  static const String specialPersonType = 'specialPersonType';
  static const String workGroup = 'workGroup';
  static const String place = 'place';
  static const String villageDetail = 'villageDetail';
  static const String progress = 'progress';
  static const String placePhoto = 'place_photo';
  static const String villagePhoto = 'village_photo';
  static const String police = 'police';
  static const String menu = 'menu';
}

class VillagedetailController extends GetxController {
  ScrollController scrollController = ScrollController();

  GlobalKey intro = GlobalKey();
  GlobalKey police = GlobalKey();
  List<GlobalKey> peopleKeys = [];
  List<GlobalKey> placeKeys = [];
  List<GlobalKey> workgroupKeys = [];

  bool isSave = true;

  int code = 0;

  VillageModel villageDetail = VillageModel();

  List<VillageAreaModel> areas = [];

  /// 获取村庄详情
  Future getVillageDetail(int id) async {
    code = 0;
    update([VillageDetailControllerIDS.villageDetail]);

    villageDetail = await VillageApi.getVillageDetail(id, fail: () {
      code = 404;
      update([VillageDetailControllerIDS.villageDetail]);
    });

    code = 200;

    peopleKeys = List.generate(
        villageDetail.personInterest!.length, (index) => GlobalKey());

    placeKeys =
        List.generate(villageDetail.place!.length, (index) => GlobalKey());

    workgroupKeys =
        List.generate(villageDetail.organize!.length, (index) => GlobalKey());

    update([VillageDetailControllerIDS.villageDetail]);
  }

  /// 修改村庄详情
  Future updateVillageDetail(BuildContext context,
      {int? id, required Map<String, dynamic> data}) async {
    Dialogs.showLoadingDialog(context);
    if (id != null) {
      await VillageApi.updateVillageDetail(id, data);
      villageDetail = await VillageApi.getVillageDetail(id);
    } else {
      villageDetail = await VillageApi.createVillage(data);
    }

    isSave = true;
    showToast('保存成功');
    update([VillageDetailControllerIDS.villageDetail]);
    if (id == null) pageback(result: villageDetail);
  }

  /// 删除村庄
  deleteVillage(BuildContext context, String name, int villageId) async {
    Dialogs.showConfirmDialog(
      context,
      title: '是否确认删除',
      subTitle: ' $name',
      content: '删除后无法恢复',
      onConfirm: () async {
        Dialogs.showLoadingDialog(context);
        await VillageApi.deleteVillage(
          villageId,
          // fail: () => showToast('删除失败'),
          fail: () {},
        );
        pageback();
        showToast('删除成功');
        pageback(result: 'remove');
      },
    );
  }

  /// 获取人员详情
  Future<PeopleDetailModel> getPersonDetail(int id) async {
    return await PeopleApi.getPeopleDetail(id, pageBack: true);
  }

  /// 跳转到指定位置
  scrollToOffset(GlobalKey key) {
    RenderBox box = key.currentContext!.findRenderObject() as RenderBox;
    double offset =
        box.localToGlobal(Offset.zero).dy + scrollController.offset - 60;

    scrollController.animateTo(offset,
        duration: const Duration(milliseconds: 300), curve: Curves.linear);
  }
}
