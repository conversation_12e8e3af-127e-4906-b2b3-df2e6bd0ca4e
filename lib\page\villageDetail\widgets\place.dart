import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/widget/image_upload.dart';
import 'package:police/common/widget/float_square_button.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/page/villageDetail/index.dart';
import '../../../common/api/uplaod_image_api.dart';
import '../../../common/model/village_model/place.dart';
import '../../../common/routes/jump_page.dart';
import '../../../common/util/image_picker.dart';
import '../../../common/widget/image.dart';

class Place extends StatelessWidget {
  const Place({super.key, required this.place});

  final List<PlaceModel> place;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VillagedetailController>(
      id: VillageDetailControllerIDS.place,
      builder: (controller) {
        return Column(
          children: [
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (_, __) => const SizedBox(height: 200),
              itemCount: place.length,
              itemBuilder: (_, index) {
                return _buildPlaceItem(context, index, controller);
              },
            ),
            const SizedBox(height: 40),
            NormolButton(
              lable: '新增重点区域',
              width: 300,
              onTap: () {
                place.add(PlaceModel());
                Get.find<VillagedetailController>().placeKeys.add(GlobalKey());
                controller.update([
                  VillageDetailControllerIDS.place,
                  VillageDetailControllerIDS.menu
                ]);
              },
              // color: const Color(0xca073773),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPlaceItem(
      BuildContext context, int index, VillagedetailController controller) {
    PageController pageController = PageController();
    ScrollController scrollController = ScrollController();
    final double screenHeight = MediaQuery.of(context).size.height;

    return Container(
      key: controller.placeKeys[index],
      constraints: BoxConstraints(
        minHeight: screenHeight,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center, // 垂直居中
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(4)),
            child: Column(
              children: [
                Container(
                  color: const Color(0xca073773),
                  width: double.infinity,
                  height: 600,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      ImageUploadWidget(
                        photoUrl: place[index].photo ?? '',
                        width: double.infinity,
                        fit: BoxFit.fill,
                        onUploaded: (String url) {
                          place[index].photo = url;
                          controller.isSave = false;
                        },
                      ),
                      Align(
                        alignment: Alignment.topRight,
                        child: FloatSquareButton(
                          onTap: () {
                            _remove(index);
                            Get.find<VillagedetailController>()
                                .placeKeys
                                .removeAt(index);
                            controller.update([
                              VillageDetailControllerIDS.place,
                              VillageDetailControllerIDS.menu
                            ]);
                          },
                          icon: Icons.delete,
                          margin: const EdgeInsets.all(10),
                          color: const Color(0x883875a6),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(color: Color(0xca073773)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 100,
                        child: Scrollbar(
                          controller: scrollController,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            controller: scrollController,
                            itemCount: place[index].pics!.length + 1,
                            itemBuilder: (_, i) {
                              return InkWell(
                                onTap: () async {
                                  if (i == place[index].pics!.length) {
                                    XFile? file = await imagePicker();
                                    if (file != null) {
                                      Dialogs.showLoadingDialog(context);
                                      place[index].pics!.add(file.path);
                                      controller.update(
                                          [VillageDetailControllerIDS.place]);
                                      final result = await uploadImage(
                                        file.path,
                                        fail: (p0) {
                                          pageback();
                                          place[index].pics!.remove(file.path);
                                          controller.update([
                                            VillageDetailControllerIDS.place
                                          ]);
                                        },
                                      );
                                      pageback();
                                      place[index].pics![i] =
                                          result['files'][0];
                                      controller.update(
                                          [VillageDetailControllerIDS.place]);
                                    }
                                  } else {
                                    pageController =
                                        PageController(initialPage: i);

                                    showDialog(
                                        context: context,
                                        builder: (context) {
                                          return _buildPhotoView(pageController,
                                              index, context, controller);
                                        });
                                  }
                                },
                                child: SizedBox(
                                  width: 100,
                                  height: 100,
                                  child: i < place[index].pics!.length
                                      ? !place[index].pics![i].contains(':\\')
                                          ? ImageWidget.network(
                                              place[index].pics![i])
                                          : ImageWidget.file(
                                              place[index].pics![i])
                                      : Container(
                                          color: Colors.grey.withOpacity(.3),
                                          child: const Icon(Icons.photo),
                                        ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(70, 0, 70, 30),
                        child: Column(
                          children: [
                            InputText(
                              defaultText: place[index].name,
                              textAlign: TextAlign.center,
                              hint: '请输入区域名称',
                              hintStyle: const TextStyle(
                                fontSize: 100,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey,
                                shadows: [
                                  Shadow(
                                    blurRadius: 10.0,
                                    color: Colors.black,
                                    offset: Offset(5.0, 5.0),
                                  ),
                                ],
                              ),
                              style: const TextStyle(
                                fontSize: 100,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                shadows: [
                                  Shadow(
                                    blurRadius: 10.0,
                                    color: Colors.black,
                                    offset: Offset(5.0, 5.0),
                                  ),
                                ],
                              ),
                              onChanged: (text) {
                                place[index].name = text;
                                controller.isSave = false;
                                controller
                                    .update([VillageDetailControllerIDS.menu]);
                              },
                            ),
                            const SizedBox(height: 30),
                            InputText(
                              defaultText: place[index].introduce,
                              maxLines: null,
                              textInputAction: TextInputAction.newline,
                              hint: '请输入描述',
                              onChanged: (text) {
                                place[index].introduce = text;
                                controller.isSave = false;
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoView(PageController pageController, int index,
      BuildContext context, VillagedetailController controller) {
    return Stack(
      children: [
        PageView.builder(
            controller: pageController,
            itemCount: place[index].pics!.length,
            itemBuilder: (_, i) {
              return Stack(
                children: [
                  Center(
                    child: ImageWidget.network(
                      place[index].pics![i],
                      fit: BoxFit.contain,
                    ),
                  ),
                  Center(
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            pageController.animateToPage(i - 1,
                                duration: const Duration(microseconds: 500),
                                curve: Curves.linear);
                          },
                          icon: const Icon(
                            CupertinoIcons.chevron_left,
                            size: 50,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () {
                            pageController.animateToPage(i + 1,
                                duration: const Duration(microseconds: 500),
                                curve: Curves.linear);
                          },
                          icon: const Icon(
                            CupertinoIcons.chevron_right,
                            size: 50,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 50),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          FloatSquareButton(
                            onTap: () {
                              Dialogs.showConfirmDialog(
                                context,
                                title: '是否确认删除此图片',
                                content: '删除后无法恢复！',
                                onConfirm: () {
                                  place[index].pics!.removeAt(i);

                                  pageback();
                                  controller.update(
                                      [VillageDetailControllerIDS.place]);
                                },
                              );
                            },
                            icon: Icons.delete,
                            color: Colors.red,
                          ),
                          const SizedBox(width: 20),
                          FloatSquareButton(
                            onTap: () async {
                              XFile? file = await imagePicker();
                              if (file != null) {
                                pageback();

                                Dialogs.showLoadingDialog(context);

                                place[index].pics![i] = file.path;

                                controller
                                    .update([VillageDetailControllerIDS.place]);
                                final result = await uploadImage(
                                  file.path,
                                  fail: (p0) {
                                    pageback();
                                    place[index].pics!.remove(file.path);
                                    controller.update(
                                        [VillageDetailControllerIDS.place]);
                                  },
                                );
                                pageback();
                                place[index].pics![i] = result['files'][0];
                                controller
                                    .update([VillageDetailControllerIDS.place]);
                              }
                            },
                            icon: Icons.cached,
                            color: Colors.blue,
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              );
            }),
        Padding(
          padding: const EdgeInsets.fromLTRB(0, 70, 20, 0),
          child: Align(
            alignment: Alignment.topRight,
            child: FloatSquareButton(
              onTap: () => pageback(),
              icon: Icons.close,
            ),
          ),
        )
      ],
    );
  }

  void _remove(int index) {
    place.removeAt(index);
    Get.find<VillagedetailController>()
        .update([VillageDetailControllerIDS.place]);
  }
}
