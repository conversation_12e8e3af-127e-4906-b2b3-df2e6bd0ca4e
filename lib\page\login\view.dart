import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/scaffold_widget.dart';
import 'package:police/page/login/widgets/login_card.dart';
import 'package:police/page/login/widgets/welcome_text.dart';

import 'index.dart';

class LoginPage extends GetView<LoginController> {
  const LoginPage({super.key});

  // 主视图
  Widget _buildView() {
    return Container(
      decoration: const BoxDecoration(
          image: DecorationImage(
        fit: BoxFit.cover,
        image: AssetImage('assets/pic/5e05bacc65e351577433804359.jpg'),
      )),
      child: Row(children: [
        const WelcomeText(),
        <PERSON><PERSON><PERSON><PERSON>(controller: controller),
        const SizedBox(width: 80)
      ]),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LoginController>(
      init: LoginController(),
      id: "login",
      builder: (_) {
        return ScaffoldWidget(
          body: _buildView(),
          showBack: false,
        );
      },
    );
  }
}
