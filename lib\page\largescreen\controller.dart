import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart' hide Response;
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/model/big_screen_model/big_screen_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/util/https.dart';
import 'package:police/page/analyzeDetail/view.dart';

import '../../common/model/analyze_model.dart';

class LargescernnIDS {
  static String sort = 'sort';
  static String cases = 'cases';
  static String newcases = 'newcases';
  static String peoples = 'peoples';
  static String village = 'village';
  static String time = 'time';
}

class TimeRange {
  static String today = 'today';
  static String month = 'this_month';
  static String lastMonth = 'last_month';
  static String quarter = 'this_quarter';
  static String year = 'this_year';
}

Map<String, String> TimeRangeStr = {
  'today': '今日',
  'this_month': '本月',
  'last_month': '上月',
  'this_quarter': '上季度',
  'this_year': '今年',
};

class LargescreenController extends GetxController {
  late Timer _timer;

  late Timer _timer2;

  @override
  void onReady() async {
    super.onReady();
    loading = true;
    await getBigScreenData();
    update([LargescernnIDS.time]);

    // 更新大屏数据
    _timer = Timer.periodic(const Duration(seconds: 60), (timer) async {
      await getBigScreenData();
    });

    // 更新时间
    _timer2 = Timer.periodic(const Duration(seconds: 1), (timer) async {
      update([LargescernnIDS.time]);
    });
  }

  @override
  void onClose() {
    _timer.cancel();
    _timer2.cancel();
    super.onClose();
  }

  BigScreenModel bigscreenData = BigScreenModel();
  String timeRange = TimeRange.month;
  bool loading = false;
  List<String> headers = ['警情内容', '警情类型', '警情分类', '警情地点', '警情时间'];

  /// 获取大屏数据
  getBigScreenData() async {
    Response response = await Https.get(
      Apis.bigScreen,
      queryParameters: {'time_range': timeRange},
      fail: (p0) => loading = false,
    );

    bigscreenData = BigScreenModel.fromJson(response.data);
    loading = false;
    update([
      LargescernnIDS.cases,
      LargescernnIDS.newcases,
      LargescernnIDS.sort,
      LargescernnIDS.peoples,
      LargescernnIDS.village
    ]);
  }

  /// 获取警情
  getAnalyze(BuildContext context, String type) async {
    Dialogs.showLoadingDialog(context);
    Response response = await Https.get(
      Apis.enterJudgment,
      queryParameters: {'types': type},
      pageBack: true,
      // fail: (p0) => showToast('获取失败'),
      fail: (p0) {},
    );

    AnalyzeModel detail = AnalyzeModel.fromJson(response.data);

    jump2page(AnalyzedetailPage(detail));
  }
}
