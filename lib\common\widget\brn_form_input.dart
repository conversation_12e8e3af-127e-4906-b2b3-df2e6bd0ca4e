import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';

class BrnFormInput extends StatelessWidget {
  const BrnFormInput({
    super.key,
    required this.title,
    this.defaultText = '',
    this.hint = '',
    this.fontColor = const Color(0xd8cfe0ff),
    this.fontSize = 20,
    this.onChanged,
  });

  final String title;
  final String defaultText;
  final String hint;
  final Color fontColor;
  final double fontSize;
  final Function(String text)? onChanged;

  @override
  Widget build(BuildContext context) {
    return BrnTextInputFormItem(
      controller: TextEditingController()..text = defaultText,
      title: '$title：',
      hint: hint,
      backgroundColor: Colors.transparent,
      themeData: BrnFormItemConfig(
        titleTextStyle: BrnTextStyle(color: fontColor, fontSize: fontSize),
        contentTextStyle: BrnTextStyle(color: fontColor, fontSize: fontSize),
        titlePaddingLg: EdgeInsets.zero,
        titlePaddingSm: EdgeInsets.zero,
      ),
      onChanged: (value) => onChanged?.call(value.trim()),
    );
  }
}
