import 'package:auto_size_text/auto_size_text.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/util/date_format.dart';
import 'package:police/page/largescreen/controller.dart';
import '../../../common/widget/corner_card.dart';

class PersonnelData extends StatelessWidget {
  const PersonnelData({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      List<Map<String, dynamic>> tips = [
        {
          'title': '高发',
          'color': Colors.red,
        },
        {
          'title': '频发',
          'color': Colors.orange,
        },
        {
          'title': '低发',
          'color': Colors.blue,
        },
      ];
      return GetBuilder<LargescreenController>(
          id: LargescernnIDS.village,
          builder: (controller) {
            return CornerCard(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30), // 增加左右间距
                child: Column(
                  children: [
                    const Text(
                      "警情发生区",
                      style: TextStyle(fontSize: 16, color: Color(0xe7ffffff)),
                    ),
                    const SizedBox(height: 10),
                    Text(
                        dateFormat(DateTime.now(),
                            format: [yyyy, '年', mm, '月', dd, '日']),
                        style: const TextStyle(
                          color: Colors.greenAccent,
                          fontSize: 50,
                          letterSpacing: 2,
                        )),
                    // const SizedBox(height: 10),
                    GetBuilder<LargescreenController>(
                        id: LargescernnIDS.time,
                        builder: (contro) {
                          return Text(
                              dateFormat(DateTime.now(), format: [HH, ':', nn]),
                              style: const TextStyle(
                                color: Colors.greenAccent,
                                fontSize: 50,
                                letterSpacing: 2,
                              ));
                        }),
                    const SizedBox(height: 10),
                    Expanded(
                      child: ScrollConfiguration(
                        behavior:
                            const ScrollBehavior().copyWith(scrollbars: false),
                        child: SingleChildScrollView(
                          child: Wrap(
                            spacing: 40.0, // 增加水平间距
                            runSpacing: 20.0, // 增加垂直间距
                            children: controller.bigscreenData.villageList!
                                .map((village) {
                              return _buildVerticalTextWithNumber(
                                controller.timeRange,
                                village.name ?? '',
                                village.alarmCount.toString(),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: tips.map((e) {
                        return Row(
                          children: [
                            Container(
                              width: 10,
                              height: 10,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(3),
                                  color: e['color']),
                              margin: const EdgeInsets.symmetric(horizontal: 5),
                            ),
                            Text(
                              e['title'],
                              style: const TextStyle(color: Color(0xe7ffffff)),
                            ),
                          ],
                        );
                      }).toList(),
                    )
                  ],
                ),
              ),
            );
          });
    });
  }

  Widget _buildVerticalTextWithNumber(timeRange, String text, String number) {
    var textColor = Colors.white;
    int parsedNumber = int.parse(number);

    if (timeRange == "today") {
      // 日
      textColor = parsedNumber >= 2
          ? Colors.red
          : parsedNumber >= 1
              ? Colors.orange
              : parsedNumber >= 0
                  ? Colors.white
                  : Colors.white; // 默认颜色
    } else if (timeRange == "this_month" || timeRange == "last_month") {
      // 月
      textColor = parsedNumber >= 15
          ? Colors.red
          : parsedNumber >= 10
              ? Colors.orange
              : parsedNumber >= 1
                  ? Colors.blue
                  : Colors.white;
    } else if (timeRange == "this_quarter") {
      // 季
      textColor = parsedNumber >= 45
          ? Colors.red
          : parsedNumber >= 30
              ? Colors.orange
              : parsedNumber >= 1
                  ? Colors.blue
                  : Colors.white;
    } else if (timeRange == "this_year") {
      // 年
      textColor = parsedNumber >= 180
          ? Colors.red
          : parsedNumber >= 120
              ? Colors.orange
              : parsedNumber >= 1
                  ? Colors.blue
                  : Colors.white;
    }

    double fontSize = 14.0; // 默认字体大小

    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Column(
        children: [
          ...text.split('').map((c) =>
              Text(c, style: TextStyle(color: textColor, fontSize: fontSize))),
          Text(number,
              style: TextStyle(
                color: textColor,
                fontSize: fontSize,
              )),
        ],
      ),
    );
  }
}
