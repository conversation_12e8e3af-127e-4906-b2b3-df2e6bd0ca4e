import 'package:flutter/material.dart';
import 'package:police/common/widget/form_text.dart';

import '../../../common/widget/scale_animated_widget.dart';

class FactoryInfo extends StatelessWidget {
  const FactoryInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return ScaleAnimatedWidget(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: const BoxDecoration(color: Colors.white, boxShadow: [
            BoxShadow(
              offset: Offset(0, 3),
              color: Colors.grey,
              blurRadius: 3,
            )
          ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                'assets/pic/4162280569_048dd611ec_c.jpg',
              ),
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text(
                      "工厂名",
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 25),
                    ),
                  ),
                  FormText(
                    labelText: '地址',
                    valueText: '百户村',
                    icon: Icons.location_on_rounded,
                    maxLines: 2,
                    padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
                  ),
                  FormText(
                    labelText: '员工数',
                    valueText: '1000',
                    icon: Icons.people,
                    padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
                  ),
                  FormText(
                    labelText: '负责民警',
                    valueText: '啊水水',
                    icon: Icons.person,
                    padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
                  ),
                  FormText(
                    labelText: '电话',
                    valueText: '123245454564',
                    icon: Icons.phone_android_rounded,
                    padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
