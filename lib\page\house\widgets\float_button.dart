import 'package:flutter/material.dart';
import 'package:police/common/model/house_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/page/house/index.dart';
import 'package:police/page/housedetail/index.dart';

import '../../../common/widget/float_square_button.dart';

class FloatButton extends StatelessWidget {
  const FloatButton(
      {super.key, required this.controller, required this.unitId});

  final HouseController controller;
  final int unitId;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.all(36),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FloatSquareButton(
                onTap: () => controller.getHouses(unitId), icon: Icons.refresh),
            const SizedBox(height: 10),
            FloatSquareButton(
              onTap: () => jump2page(
                HousedetailPage(detail: HouseModel(), unitId: unitId),
                // HousedetailPage(unitId: unitId),
                onBack: (result) {
                  if (result != null) {
                    if (result.villageUnitId != null) {
                      controller.houses.add(result);
                      controller.update([HouseControllerIDS.housesItem]);
                    }
                  }
                },
              ),
              icon: Icons.add,
            ),
          ],
        ),
      ),
    );
  }
}
