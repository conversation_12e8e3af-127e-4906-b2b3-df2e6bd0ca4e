import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/empty_widget.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/scaffold_widget.dart';
import 'package:police/page/unit/widgets/float_button.dart';
import 'package:police/page/unit/widgets/table_data.dart';
import 'package:police/page/unit/widgets/table_header.dart';

import '../../common/widget/turn_page.dart';
import 'index.dart';

class UnitPage extends GetView<UnitController> {
  const UnitPage({
    super.key,
    required this.buildId,
    required this.buildName,
  });

  final String buildName;
  final int buildId;

  // 主视图
  Widget _buildView() {
    return Padding(
      padding: const EdgeInsets.only(top: 60),
      child: GetBuilder<UnitController>(
          id: UnitControllerIDS.unitItem,
          builder: (contro) {
            return controller.code == 0
                ? const Center(child: LoadingWidget())
                : controller.code == 404
                    ? const Center(child: HttpErrorWidget())
                    : controller.code == 200 && controller.units.isEmpty
                        ? const Center(child: EmptyWidget())
                        : SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                UnitTableHeader(controller: controller),
                                const UnitTableData(),
                                const SizedBox(height: 50),
                                GetBuilder<UnitController>(
                                    id: UnitControllerIDS.turnpage,
                                    builder: (context) {
                                      return Visibility(
                                        visible: controller.units.isNotEmpty,
                                        child: Align(
                                          alignment: Alignment.bottomCenter,
                                          child: TurnPage(
                                            itemCount: controller.maxpages,
                                            currentIndex:
                                                controller.currentPage,
                                            onTap: (index, total) => controller
                                                .turnPage(index, total,
                                                    buildId: buildId),
                                          ),
                                        ),
                                      );
                                    }),
                                const SizedBox(height: 50),
                              ],
                            ),
                          );
          }),
    );
  }

  @override
  Widget build(BuildContext context) {
    Get.put(UnitController()).getUnits(buildId);
    print(buildId);
    return GetBuilder<UnitController>(
      init: UnitController(),
      id: "unit",
      builder: (_) {
        return ScaffoldWidget(
          body: _buildView(),
          floatButton: [
            FloatButton(
              controller: controller,
              buildId: buildId,
              buildName: buildName,
            )
          ],
          strTitle: buildName,
        );
      },
    );
  }
}
