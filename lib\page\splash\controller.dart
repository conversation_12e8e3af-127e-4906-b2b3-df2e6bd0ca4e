import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/enums.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/secret_key.dart';
import 'package:police/common/util/shared.dart';
import 'package:police/common/widget/corner_card.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/common/widget/scale_animated_widget.dart';
import 'package:police/page/home/<USER>';
import 'package:police/page/home/<USER>';
import 'package:police/page/login/index.dart';
import 'package:police/page/main/main_page.dart';
import 'package:police/page/user/controller.dart';

import '../../common/model/user_info_model.dart';
import '../cases/controller.dart';

class SplashController extends GetxController {
  int countdown = 10;
  late Timer timer;

  UserInfoModel userInfo = UserInfoModel();

  /// 开始倒计时
  startCountdown() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer2) {
      countdown--;
      if (countdown == 0) {
        timer.cancel();
        update(['server_button']);
      }
    });
  }

  /// 跳转主页
  jump2Home(bool keyError) async {
    String? server = await SharedUtil.getString('server'); // 获取服务器地址
    secretKey = await SharedUtil.getString('keyStr'); // 获取密钥
    Apis.baseUrl = server ?? ''; // 设置服务器地址

    /// 获取登录状态
    LoginController loginController = Get.put(LoginController());
    bool isLogin = await loginController.getIsLogin();
    if (keyError) {
      timer.cancel();
      countdown = 0;
      update(['server_button']);
      return;
    }
    if (isLogin) {
      await loginController.getUserInfo();
      await Get.put(CasesController()).getAllVillage();
      await Get.put(HomeController()).getIntro();
      await Get.put(UserController()).getUserList();
    }
    timer.cancel();

    jump2page(
      loginController.isLogin ? const MainPage() : const LoginPage(),
      jumpPageType: JumpPageType.offAll,
    );
  }

  /// 更改服务器地址
  changeServer(BuildContext context) async {
    // await SharedUtil.setString('server', server);
    // jump2Home();
    TextEditingController server =
        TextEditingController(text: await SharedUtil.getString('server'));
    TextEditingController key = TextEditingController(text: secretKey);
    showDialog(
        context: context,
        builder: (_) {
          return ScaleAnimatedWidget(
            begin: 0,
            end: 1,
            userMouse: false,
            child: UnconstrainedBox(
              child: CornerCard(
                padding: const EdgeInsets.all(20),
                child: SizedBox(
                  width: 400,
                  // height: 200,
                  child: Column(
                    children: [
                      InputText(
                        lableText: '服务器地址',
                        controller: server,
                        isDense: false,
                        focusedBorder: _inputBorder(),
                        enabledBorder: _inputBorder(),
                        labelStyle: const TextStyle(
                          color: Color(0xff00caee), // 设置标签文本的颜色
                        ),
                        fillColor: const Color(0xff084a89),
                      ),
                      // const SizedBox(height: 10),
                      // InputText(
                      //   lableText: '密钥',
                      //   controller: key,
                      //   isDense: false,
                      //   obscureText: true,
                      //   focusedBorder: _inputBorder(),
                      //   enabledBorder: _inputBorder(),
                      //   labelStyle: const TextStyle(
                      //     color: Color(0xff00caee), // 设置标签文本的颜色
                      //   ),
                      //   fillColor: const Color(0xff084a89),
                      // ),
                      // const Spacer(),
                      const SizedBox(height: 40),
                      NormolButton(
                        lable: '确认',
                        onTap: () async {
                          await SharedUtil.setString(
                              'server', server.text.trim());
                          // await SharedUtil.setString('keyStr', key.text.trim());
                          pageback();
                          countdown = 10;
                          startCountdown();
                          jump2Home(false);
                        },
                      ),
                      const SizedBox(height: 10),
                      NormolButton(
                        lable: '取消',
                        color: Colors.blue.withOpacity(.3),
                        onTap: () => pageback(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }

  OutlineInputBorder _inputBorder() {
    return const OutlineInputBorder(
      borderSide: BorderSide(
        color: Color(0xff00bcdd), // 聚焦时的边框颜色
        width: 2.0,
      ),
    );
  }
}
