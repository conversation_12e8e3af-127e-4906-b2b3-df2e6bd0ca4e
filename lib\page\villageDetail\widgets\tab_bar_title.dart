import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/custom_popup_menu.dart';
import 'package:police/page/villageDetail/controller.dart';

import '../../../common/model/village_model/organize/organize.dart';
import '../../../common/model/village_model/place.dart';

class TabbarTitle extends StatelessWidget {
  const TabbarTitle({super.key, required this.controller});

  final VillagedetailController controller;

  @override
  Widget build(BuildContext context) {
    List<String> titles = [
      '介绍',
      '民警',
      '关注人员',
      '重点区域',
      '群防群治',
    ];

    return GetBuilder<VillagedetailController>(
        id: VillageDetailControllerIDS.menu,
        builder: (context) {
          return Visibility(
            visible: controller.code == 200,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(titles.length, (index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: index < 2
                      ? InkWell(
                          onTap: () => controller.scrollToOffset(index == 1
                              ? controller.police
                              : controller.intro),
                          child: Text(
                            titles[index],
                            style: const TextStyle(
                                color: Colors.white, fontSize: 17),
                          ),
                        )
                      : CustomPopupMenu(
                          buttonBuilder: (_, showMenu) => Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: Builder(builder: (context) {
                              return InkWell(
                                onTap: () {
                                  final renderBox =
                                      context.findRenderObject() as RenderBox;
                                  final globalPosition =
                                      renderBox.localToGlobal(Offset.zero);
                                  final buttonSize = renderBox.size;
                                  showMenu(context, globalPosition, buttonSize);
                                },
                                child: Text(
                                  titles[index],
                                  style: const TextStyle(
                                      color: Colors.white, fontSize: 17),
                                ),
                              );
                            }),
                          ),
                          menuItems: _buildMenu(index),
                        ),
                );
              }),
            ),
          );
        });
  }

  List<PopupMenuEntry> _buildMenu(int index) {
    List<PopupMenuEntry> content = [];
    switch (index) {
      case 2:
        List<dynamic> focus = controller.villageDetail.personInterest ?? [];

        content = List.generate(focus.length, (index) {
          return PopupMenuItem(
            onTap: () {
              controller.scrollToOffset(controller.peopleKeys[index]);
              // pageback();
            },
            child: Text(
              focus[index] == null || focus[index].isEmpty
                  ? '未命名'
                  : focus[index],
            ),
          );
        });

        break;
      case 3:
        List<PlaceModel> places = controller.villageDetail.place ?? [];

        content = List.generate(places.length, (index) {
          return PopupMenuItem(
            onTap: () {
              controller.scrollToOffset(controller.placeKeys[index]);
              // pageback();
            },
            child: Text(
                places[index].name == null || places[index].name!.isEmpty
                    ? '未命名'
                    : places[index].name!),
          );
        });
        break;
      case 4:
        List<Organize> workgroup = controller.villageDetail.organize ?? [];

        content = List.generate(workgroup.length, (index) {
          return PopupMenuItem(
            onTap: () {
              controller.scrollToOffset(controller.workgroupKeys[index]);
              // pageback();
            },
            child: Text(
                workgroup[index].name == null || workgroup[index].name!.isEmpty
                    ? '未命名'
                    : workgroup[index].name!),
          );
        });
        break;
      default:
    }

    return content;
  }
}
