import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/page/cases/controller.dart';

class TableHeader extends StatelessWidget {
  const TableHeader({super.key, required this.controller});

  final CasesController controller;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CasesController>(
        id: CaseControllerIDS.header,
        builder: (context) {
          return Row(
            children: List.generate(controller.tabHeader.length, (index) {
              return Expanded(
                flex: index == 2 ? 3 : 2,
                child: Container(
                  color: const Color(0xd3041e3e),
                  alignment: Alignment.center,
                  margin: const EdgeInsets.all(.5),
                  // padding: const EdgeInsets.symmetric(vertical: 15),
                  height: 70,
                  child: Text(
                    controller.tabHeader[index],
                    style: const TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                        color: Color(0xf0e5edff)),
                  ),
                ),
              );
            }),
          );
        });
  }
}
