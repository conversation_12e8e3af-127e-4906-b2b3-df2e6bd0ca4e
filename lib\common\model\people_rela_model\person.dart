class Person {
  int? id;
  int? villageUnitId;
  String? address;
  String? propertyOwner;
  String? propertyTelephone;
  String? propertyIdNumber;
  String? housingPurposes;
  bool? residencePermit;
  int? numberResidents;
  String? graphics;
  bool? isAppDownload;
  String? dogSize;
  bool? dogDocuments;
  List<String>? vehicle;
  String? vehicleAdditional;
  bool? domesticViolence;
  bool? cohabitantIssues;
  bool? needsCounseling;
  String? caseStatus;
  String? overseasResident;
  String? overseasDocument;
  List<String>? specialPopulation;
  List<String>? disadvantagedGroup;
  List<String>? recentAlerts;
  String? recentAlertsAdditional;
  List<String>? feedback;
  String? feedbackAdditional;
  String? feedbackSummary;
  bool? securityRisk;
  List<String>? securityConflicts;
  String? securityConflictsAdditional;
  List<String>? securityPublicSafety;
  String? securityPublicSafetyAdditional;
  List<String>? securityDisposalStatus;
  String? securityDisposalStatusAdditional;
  DateTime? createTime;
  DateTime? updateTime;

  Person({
    this.id,
    this.villageUnitId,
    this.address,
    this.propertyOwner,
    this.propertyTelephone,
    this.propertyIdNumber,
    this.housingPurposes,
    this.residencePermit,
    this.numberResidents,
    this.graphics,
    this.isAppDownload,
    this.dogSize,
    this.dogDocuments,
    this.vehicle,
    this.vehicleAdditional,
    this.domesticViolence,
    this.cohabitantIssues,
    this.needsCounseling,
    this.caseStatus,
    this.overseasResident,
    this.overseasDocument,
    this.specialPopulation,
    this.disadvantagedGroup,
    this.recentAlerts,
    this.recentAlertsAdditional,
    this.feedback,
    this.feedbackAdditional,
    this.feedbackSummary,
    this.securityRisk,
    this.securityConflicts,
    this.securityConflictsAdditional,
    this.securityPublicSafety,
    this.securityPublicSafetyAdditional,
    this.securityDisposalStatus,
    this.securityDisposalStatusAdditional,
    this.createTime,
    this.updateTime,
  });

  factory Person.fromJson(Map<String, dynamic> json) => Person(
        id: json['id'] as int?,
        villageUnitId: json['village_unit_id'] as int?,
        address: json['address'] as String?,
        propertyOwner: json['property_owner'] as String?,
        propertyTelephone: json['property_telephone'] as String?,
        propertyIdNumber: json['property_id_number'] as String?,
        housingPurposes: json['housing_purposes'] as String?,
        residencePermit: json['residence_permit'] as bool?,
        numberResidents: json['number_residents'] as int?,
        graphics: json['graphics'] as String?,
        isAppDownload: json['is_app_download'] as bool?,
        dogSize: json['dog_size'] as String?,
        dogDocuments: json['dog_documents'] as bool?,
        vehicle: json['vehicle'] as List<String>?,
        vehicleAdditional: json['vehicle_additional'] as String?,
        domesticViolence: json['domestic_violence'] as bool?,
        cohabitantIssues: json['cohabitant_issues'] as bool?,
        needsCounseling: json['needs_counseling'] as bool?,
        caseStatus: json['case_status'] as String?,
        overseasResident: json['overseas_resident'] as String?,
        overseasDocument: json['overseas_document'] as String?,
        specialPopulation: json['special_population'] as List<String>?,
        disadvantagedGroup: json['disadvantaged_group'] as List<String>?,
        recentAlerts: json['recent_alerts'] as List<String>?,
        recentAlertsAdditional: json['recent_alerts_additional'] as String?,
        feedback: json['feedback'] as List<String>?,
        feedbackAdditional: json['feedback_additional'] as String?,
        feedbackSummary: json['feedback_summary'] as String?,
        securityRisk: json['security_risk'] as bool?,
        securityConflicts: json['security_conflicts'] as List<String>?,
        securityConflictsAdditional:
            json['security_conflicts_additional'] as String?,
        securityPublicSafety: json['security_public_safety'] as List<String>?,
        securityPublicSafetyAdditional:
            json['security_public_safety_additional'] as String?,
        securityDisposalStatus:
            json['security_disposal_status'] as List<String>?,
        securityDisposalStatusAdditional:
            json['security_disposal_status_additional'] as String?,
        createTime: json['create_time'] == null
            ? null
            : DateTime.parse(json['create_time'] as String),
        updateTime: json['update_time'] == null
            ? null
            : DateTime.parse(json['update_time'] as String),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'village_unit_id': villageUnitId,
        'address': address,
        'property_owner': propertyOwner,
        'property_telephone': propertyTelephone,
        'property_id_number': propertyIdNumber,
        'housing_purposes': housingPurposes,
        'residence_permit': residencePermit,
        'number_residents': numberResidents,
        'graphics': graphics,
        'is_app_download': isAppDownload,
        'dog_size': dogSize,
        'dog_documents': dogDocuments,
        'vehicle': vehicle,
        'vehicle_additional': vehicleAdditional,
        'domestic_violence': domesticViolence,
        'cohabitant_issues': cohabitantIssues,
        'needs_counseling': needsCounseling,
        'case_status': caseStatus,
        'overseas_resident': overseasResident,
        'overseas_document': overseasDocument,
        'special_population': specialPopulation,
        'disadvantaged_group': disadvantagedGroup,
        'recent_alerts': recentAlerts,
        'recent_alerts_additional': recentAlertsAdditional,
        'feedback': feedback,
        'feedback_additional': feedbackAdditional,
        'feedback_summary': feedbackSummary,
        'security_risk': securityRisk,
        'security_conflicts': securityConflicts,
        'security_conflicts_additional': securityConflictsAdditional,
        'security_public_safety': securityPublicSafety,
        'security_public_safety_additional': securityPublicSafetyAdditional,
        'security_disposal_status': securityDisposalStatus,
        'security_disposal_status_additional': securityDisposalStatusAdditional,
        'create_time': createTime?.toIso8601String(),
        'update_time': updateTime?.toIso8601String(),
      };
}
