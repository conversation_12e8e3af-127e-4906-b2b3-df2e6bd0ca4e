import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/model/village_model/organize/person.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/float_square_button.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/page/peopledetail/index.dart';
import 'package:police/page/villageDetail/index.dart';
import 'package:bruno/bruno.dart';

import '../../../common/model/village_model/organize/organize.dart';

class WorkGroup extends StatelessWidget {
  const WorkGroup({
    super.key,
    required this.workgroup,
  });

  final List<Organize> workgroup;

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;

    return Column(
      children: [
        GetBuilder<VillagedetailController>(
          id: VillageDetailControllerIDS.workGroup,
          builder: (controller) {
            return Column(
              children: List.generate(workgroup.length, (index) {
                int itemCount = workgroup[index].person!.length;
                return Container(
                  key: controller.workgroupKeys[index],
                  constraints: BoxConstraints(
                    minHeight: screenHeight,
                  ),
                  child: Stack(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(bottom: 100),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            InputText(
                              defaultText: workgroup[index].name ?? '',
                              textAlign: TextAlign.center,
                              hint: '请输入小组名称',
                              style: const TextStyle(
                                  fontSize: 28, color: Colors.white),
                              onChanged: (text) {
                                workgroup[index].name = text;
                                controller.isSave = false;
                                controller
                                    .update([VillageDetailControllerIDS.menu]);
                              },
                            ),
                            const SizedBox(height: 20),
                            Column(
                              children: [
                                Wrap(
                                  spacing: 20,
                                  runSpacing: 20,
                                  children: List.generate(itemCount + 1, (i) {
                                    return i < itemCount
                                        ? _buildPersonInfoItem(
                                            context,
                                            controller,
                                            workgroup[index].person![i],
                                            index,
                                            i)
                                        : _buildAddWidget(context, index);
                                  }),
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                      Align(
                        alignment: Alignment.topRight,
                        child: FloatSquareButton(
                          onTap: () {
                            workgroup.removeAt(index);
                            Get.find<VillagedetailController>()
                                .workgroupKeys
                                .removeAt(index);

                            controller.update([
                              VillageDetailControllerIDS.workGroup,
                              VillageDetailControllerIDS.menu
                            ]);
                          },
                          icon: Icons.delete,
                          color: Colors.blue.withOpacity(.3),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            );
          },
        ),
        const SizedBox(height: 100),
        NormolButton(
          lable: '新增群防群治',
          width: 300,
          onTap: _addNewGroup,
          color: const Color(0xca073773),
        ),
      ],
    );
  }

  /// 构建单个人员信息项
  Widget _buildPersonInfoItem(BuildContext context,
      VillagedetailController controller, Person person, int index, int i) {
    List<Organize> workgroup =
        Get.find<VillagedetailController>().villageDetail.organize ?? [];
    return InkWell(
      onTap: () async {
        Dialogs.showLoadingDialog(context);
        jump2page(PeopledetailPage(
            await controller.getPersonDetail(int.parse(person.id!))));
      },
      child: Container(
        width: 350,
        padding: const EdgeInsets.all(25),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/pic/234.png'),
            fit: BoxFit.fill,
          ),
        ),
        child: Stack(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                ClipOval(
                  child: ImageWidget.network(
                    person.photo ?? '',
                    width: 120,
                    height: 120,
                  ),
                ),
                const SizedBox(height: 20),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          person.name ?? '',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 25,
                            color: Color(0xf0e5edff),
                          ),
                        ),
                      ),
                    ),
                    BrnTextInputFormItem(
                      controller: TextEditingController()
                        ..text = person.job ?? '',
                      title: '职位',
                      hint: '',
                      backgroundColor: Colors.transparent,
                      onChanged: (value) => person.job = value.trim(),
                      themeData: BrnFormItemConfig(
                        titleTextStyle:
                            BrnTextStyle(color: const Color(0xd8cfe0ff)),
                        contentTextStyle:
                            BrnTextStyle(color: const Color(0xd8cfe0ff)),
                      ),
                    ),
                    BrnTextInputFormItem(
                      controller: TextEditingController()
                        ..text = person.telephone ?? '',
                      title: '电话',
                      hint: '',
                      backgroundColor: Colors.transparent,
                      isEdit: false,
                      themeData: BrnFormItemConfig(
                        titleTextStyle:
                            BrnTextStyle(color: const Color(0xd8cfe0ff)),
                        contentTextStyle:
                            BrnTextStyle(color: const Color(0xd8cfe0ff)),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Align(
              alignment: Alignment.bottomRight,
              child: FloatSquareButton(
                color: const Color(0x883875a6),
                onTap: () {
                  workgroup[index].person!.removeAt(i);
                  Get.find<VillagedetailController>()
                      .update([VillageDetailControllerIDS.workGroup]);
                },
                icon: Icons.close,
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 构建新增人员按钮
  InkWell _buildAddWidget(BuildContext context, int index) {
    return InkWell(
      onTap: () => _addNewPerson(context, index),
      child: Container(
        width: 350,
        height: 350,
        padding: const EdgeInsets.all(25),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/pic/234.png'),
            fit: BoxFit.fill,
          ),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add, size: 70, color: Colors.white),
            SizedBox(height: 20),
            Text(
              '新增人员',
              style: TextStyle(color: Colors.white, fontSize: 17),
            ),
          ],
        ),
      ),
    );
  }

  /// 添加新的小组
  void _addNewGroup() {
    workgroup.add(Organize(name: '', person: []));
    Get.find<VillagedetailController>().workgroupKeys.add(GlobalKey());
    Get.find<VillagedetailController>().update([
      VillageDetailControllerIDS.workGroup,
      VillageDetailControllerIDS.menu
    ]);
  }

  /// 添加新人员
  void _addNewPerson(BuildContext context, int index) {
    List<Organize> workgroup =
        Get.find<VillagedetailController>().villageDetail.organize ?? [];
    Dialogs.showPickerPersonDialog(
      context,
      onSelected: (person) {
        pageback();
        workgroup[index].person!.add(
              Person(
                id: person.id.toString(),
                name: person.name ?? '',
                job: '',
                telephone: person.telephone ?? '',
                photo: person.photo ?? '',
              ),
            );
        Get.find<VillagedetailController>()
            .update([VillageDetailControllerIDS.workGroup]);
      },
    );
  }
}
