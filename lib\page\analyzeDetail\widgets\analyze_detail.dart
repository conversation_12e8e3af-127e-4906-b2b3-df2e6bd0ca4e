import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/model/analyze_model.dart';

import '../../../common/widget/input_text.dart';
import '../../analyze/controller.dart';
import '../controller.dart';

class AnalyzeDetails extends StatelessWidget {
  const AnalyzeDetails(
      {super.key, required this.detail, required this.controller});

  final AnalyzedetailController controller;
  final AnalyzeModel detail;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 20, 0, 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible: detail.id == null,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTapDown: (details) => detail.id == null
                      ? _showMenu(context, details, controller)
                      : showToast('此项不可修改'),
                  child: GetBuilder<AnalyzedetailController>(
                    id: 'type',
                    builder: (contro) {
                      return Text(
                        '类型：${analyzeSortTypesStr[detail.types ?? 'month']}',
                        style:
                            const TextStyle(fontSize: 20, color: Colors.white),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
          Visibility(
            visible: detail.id == null,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '日期',
                  style: TextStyle(fontSize: 30, color: Colors.white),
                ),
                const SizedBox(height: 10),
                InputText(
                  canEdit: detail.id == null,
                  defaultText: detail.id != null ? detail.yearMonthDay : '',
                  hint: '请输入日期/季度',
                  onChanged: (text) => detail.yearMonthDay = text,
                ),
                const SizedBox(height: 30),
              ],
            ),
          ),
          // const Text(
          //   '详情',
          //   style: TextStyle(fontSize: 30, color: Colors.white),
          // ),
          InputText(
            defaultText: detail.id != null ? detail.graphics : '',
            hint: '请输入详情',
            maxLines: null,
            minLines: 4,
            style: const TextStyle(
              letterSpacing: 4, // 调整字间距
              color: Colors.white,
              fontSize: 20, // 设置文字大小，比如 16
              height: 1.5, // 设置行高（1.5倍字体大小）
            ),
            textInputAction: TextInputAction.newline,
            onChanged: (text) => detail.graphics = text,
          ),
        ],
      ),
    );
  }

  void _showMenu(BuildContext context, TapDownDetails details,
      AnalyzedetailController controller) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        details.globalPosition.dx,
        details.globalPosition.dy,
        overlay.size.width - details.globalPosition.dx,
        overlay.size.height - details.globalPosition.dy,
      ),
      items: List.generate(analyzeSortTypesStr.keys.length, (index) {
        final key = analyzeSortTypesStr.keys.elementAt(index);
        return PopupMenuItem(
            value: key, child: Text(analyzeSortTypesStr[key]!));
      }),
    ).then((value) {
      if (value != null) {
        detail.types = value;
        controller.update(['type']);
      }
    });
  }
}
