import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../largescreen/controller.dart';
// import 'package:window_manager/window_manager.dart';

class MainController extends GetxController
    with GetSingleTickerProviderStateMixin {
  Offset? lastPosition;
  Offset initialWindowPosition = Offset.zero;

  /// 窗口尺寸
  late Size windowSize;

  List tabs = ['主页', '人员', '村庄', '警情', '大屏'];

  late TabController tabController;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: tabs.length, vsync: this);
    tabController.addListener(() {
      if (tabController.index == 4) {
        Get.put(LargescreenController()).getBigScreenData();
      }
    });
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  void onWindowResize(Size size) {
    windowSize = size;
    update(['peoples', 'factory', 'village']);
  }
}
