import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Response;
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/model/user_info_model.dart';
import 'package:police/common/util/https.dart';

class HomeController extends GetxController {
  ScrollController scrollController = ScrollController();

  String oldIntroStr = '';
  String introStr = '';
  bool introStrIsChanged = false;

  List<UserInfoModel> sir = [];

  GlobalKey person = GlobalKey();
  GlobalKey pic = GlobalKey();
  GlobalKey intro = GlobalKey();
  GlobalKey map = GlobalKey();
  GlobalKey honor = GlobalKey();
  GlobalKey honor2 = GlobalKey();
  GlobalKey honor3 = GlobalKey();
  GlobalKey honorWall = GlobalKey();

  /// 跳转到指定位置
  scrollToOffset(GlobalKey key) {
    RenderBox box = key.currentContext!.findRenderObject() as RenderBox;
    double offset =
        box.localToGlobal(Offset.zero).dy + scrollController.offset - 60;

    scrollController.animateTo(offset,
        duration: const Duration(milliseconds: 300), curve: Curves.linear);
  }

  /// 获取派出所介绍
  getIntro() async {
    await Https.get(Apis.intro).then((value) {
      introStr = value.data['data'];
      oldIntroStr = value.data['data'];
    });
  }

  /// 保存派出所介绍
  saveIntro(BuildContext context, String str) async {
    Dialogs.showLoadingDialog(context);
    await Https.post(
      Apis.intro,
      data: {'text': str},
      pageBack: true,
      contentType: 'application/x-www-form-urlencoded',
      // fail: (p0) => showToast('保存失败'),
    );
    introStrIsChanged = false;
    update(['intro_button']);
  }
}
