import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/analyze_api.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/model/analyze_model.dart';

class AnalyzeSortTypes {
  static const String year = 'year';
  static const String quarter = 'quarter';
  static const String month = 'month';
  static const String day = 'day';
}

Map<String, String> analyzeSortTypesStr = {
  AnalyzeSortTypes.year: '年',
  AnalyzeSortTypes.quarter: '季',
  AnalyzeSortTypes.month: '月',
  AnalyzeSortTypes.day: '日'
};

class AnalyzeController extends GetxController {
  int maxpages = 1;
  int currentPage = 1;
  int code = 0;
  bool loading = false;
  String currentType = AnalyzeSortTypes.month;

  List<AnalyzeModel> analyzeList = [];

  @override
  void onInit() {
    super.onInit();
    getAnalyze();
  }

  /// 获取研判列表
  getAnalyze({int page = 1}) async {
    code = 0;
    loading = true;
    update(['analyze']);
    analyzeList = await AnalyzeApi.getAnalyzeList(
      page: page,
      types: currentType,
      fail: (result) {
        code = 404;
        loading = false;
        update(['analyze']);
      },
    );

    loading = false;
    code = 200;
    update(['analyze']);
  }

  /// 删除研判
  deleteAnalyze(BuildContext context, int id, String name, int index) async {
    Dialogs.showConfirmDialog(
      context,
      title: '是否确认删除 ',
      subTitle: name,
      onConfirm: () async {
        Dialogs.showLoadingDialog(context);
        await AnalyzeApi.deleteAnalyze(
          id: id,
          fail: (result) {
            // showToast('删除失败');
            return;
          },
        );
        showToast('删除成功');
        analyzeList.removeAt(index);
        update(['data']);
      },
    );
  }

  /// 翻页
  Future<void> turnPage(int index, int total) async {
    if (index == 0 && currentPage > 1) {
      currentPage -= 1;
    } else if (index == total) {
      currentPage = maxpages;
    } else if (index > 0 && index < total) {
      currentPage = index;
    }
    await getAnalyze(page: currentPage);
  }
}
