import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/util/https.dart';
import '../model/user_info_model.dart';

class UserApi {
  static Future<Map<String, dynamic>> login(
      String username, String password) async {
    final token = await Https.post(
      Apis.token,
      data: {'username': username, 'password': password},
      contentType: 'application/x-www-form-urlencoded',
      fail: (p0) => pageback(),
      pageBack: true,
    );
    return token.data;
  }

  /// 获取用户信息
  static Future<UserInfoModel> getUserInfo(String token,
      {required Function(Response) fail}) async {
    final userInfo = await Https.get(
      Apis.userInfo,
      headers: {'Authorization': 'Bearer $token}'},
      fail: fail,
    );
    return UserInfoModel.fromJson(userInfo.data);
  }

  /// 获取用户列表
  static Future<List<UserInfoModel>> getUserList(
      {required Function(Response? result) fail, bool isSir = false}) async {
    List<UserInfoModel> userList = [];
    Response results = await Https.get(
      Apis.user,
      queryParameters: {'is_command': isSir},
      fail: fail,
    );

    for (var user in results.data['items']) {
      userList.add(UserInfoModel.fromJson(user));
    }

    return userList;
  }

  /// 创建用户
  static signup(String username, String password) async {
    await Https.post(
      Apis.user,
      data: {'username': username, 'password': password},
      contentType: 'application/x-www-form-urlencoded',
      pageBack: true,
    );
  }

  /// 更改密码
  static Future changedPwd(String password) async {
    Response result = await Https.put(
      Apis.password,
      data: {'password': password},
      pageBack: true,
      // fail: (p0) => showToast('操作失败'),
      fail: (p0) {},
    );
    return result.data;
  }

  /// 高级权限更改密码
  static Future changedPwdAdmin(
      {required String password, required int id}) async {
    Response result = await Https.put(
      '${Apis.user}/$id/password_advanced',
      data: {'password': password},
      pageBack: true,
      // fail: (p0) => showToast('操作失败'),
      fail: (p0) {},
    );
    return result.data;
  }

  /// 更改用户信息
  static Future<UserInfoModel> updateUserInfo(
      Map<String, dynamic> userInfo) async {
    Response response = await Https.patch(
      Apis.user,
      data: userInfo,
      pageBack: true,
    );

    return UserInfoModel.fromJson(response.data);
  }

  /// 冻结/解冻用户
  static Future<void> freezeUser(
      {required id,
      required bool isActive,
      required Function(Response? result) fail}) async {
    await Https.put(
      '${Apis.user}/$id/is_active',
      data: {'is_active': isActive},
      pageBack: true,
      fail: fail,
    );
  }

  /// 设置/取消指挥员
  static Future<void> setCommander(
      {required id,
      required bool isCommand,
      required Function(Response? result) fail}) async {
    await Https.put(
      '${Apis.user}/$id/is_command',
      data: {'is_command': isCommand},
      pageBack: true,
      fail: fail,
    );
  }
}
