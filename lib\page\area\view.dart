import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/scaffold_widget.dart';
import 'package:police/page/area/widgets/build.dart';
import 'package:police/page/area/widgets/drawer.dart';

import 'index.dart';

class AreaPage extends GetView<AreaController> {
  const AreaPage({
    super.key,
    required this.villageId,
    required this.villageName,
  });

  final int villageId;
  final String villageName;

  // 主视图
  Widget _buildView() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 60),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DrawerWidget(villageId: villageId),
          const VerticalDivider(),
          BuildWidget(villageId: villageId),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Get.put(AreaController()).currentArea = 0;
    Get.put(AreaController()).getAreas(villageId);
    return GetBuilder<AreaController>(
      init: AreaController(),
      id: "area",
      builder: (_) {
        return ScaffoldWidget(
          body: _buildView(),
          strTitle: villageName,
          // floatButton: [FloatButton(id: villageId)],
        );
      },
    );
  }
}
