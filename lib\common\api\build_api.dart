import 'package:dio/dio.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/model/village_build_model.dart';
import '../model/village_area_model/village_building.dart';
import '../util/https.dart';

class BuildApi {
  /// 获取楼房列表
  static Future<List<VillageBuildModel>> getBuilds(int id,
      {required Function() fail}) async {
    List<VillageBuildModel> builds = [];

    Response results = await Https.get(
      Apis.building,
      queryParameters: {'village_area_id': id},
      fail: fail(),
    );

    for (var build in results.data['items']) {
      builds.add(VillageBuildModel.fromJson(build));
    }

    return builds;
  }

  // /// 创建楼房
  // static Future<VillageBuildModel> createBuild(
  //     {required int id, required String name}) async {
  //   Response result = await Https.post(Apis.area,
  //       data: {'village_area_id': id, 'name': name});

  //   return VillageBuildModel.fromJson(result.data);
  // }

  /// 批量创建楼房
  static Future<List<VillageBuilding>> createBuilds(
      {required List<Map<String, dynamic>> newBuilds}) async {
    List<VillageBuilding> builds = [];
    Response results = await Https.post(
      Apis.buildBatch,
      data: newBuilds,
      contentType: 'application/json',
      pageBack: true,
    );

    for (var build in results.data) {
      builds.add(VillageBuilding.fromJson(build));
    }

    return builds;
  }

  /// 获取楼房详情
  static Future<VillageBuildModel> getBuildDetail(int id) async {
    Response result = await Https.get('${Apis.building}$id');

    return VillageBuildModel.fromJson(result.data);
  }

  /// 修改楼房详情
  static Future<VillageBuildModel> patchBuildDetail(
      {required Map<String, dynamic> detail, required int id}) async {
    Response result = await Https.patch(
      '${Apis.building}/$id',
      data: detail,
      pageBack: true,
    );

    return VillageBuildModel.fromJson(result.data);
  }

  /// 删除楼房
  static deleteBuild(int id) async {
    await Https.delete(
      '${Apis.building}/$id',
      pageBack: true,
    );
  }
}
