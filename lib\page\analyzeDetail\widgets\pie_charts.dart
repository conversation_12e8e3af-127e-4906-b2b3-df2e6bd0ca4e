import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';
import 'package:police/page/analyzeDetail/controller.dart';
import '../../../common/model/analyze_details_model/alarms_statistic.dart';
import '../../../common/model/analyze_model.dart';
import '../../../common/widget/corner_card.dart';
import '../../../common/widget/pie_chart.dart';

class PieCharts extends StatelessWidget {
  const PieCharts({super.key, required this.controller, required this.details});

  final AnalyzedetailController controller;
  final AnalyzeModel details;

  @override
  Widget build(BuildContext context) {
    int total = 0;
    // 各类警情列表，排除警情数量为0的
    List<AlarmsStatistic> cases = controller.details.alarmsStatistics!
        .where((element) => element.alarmCount! > 0)
        .toList();
    // 计算总数
    for (var item in cases) {
      total += item.alarmCount!;
    }

    // 计算百分比
    for (var item in cases) {
      item.percent =
          double.parse((item.alarmCount! / total * 100).toStringAsFixed(2));
    }

    return CornerCard(
      child: Column(
        children: [
          Text(
            '${controller.getYearMonth(details)}各类警情占比',
            style: const TextStyle(fontSize: 25, color: Colors.white),
          ),
          total <= 0
              ? const SizedBox(width: double.infinity, height: 270)
              : PieChart(
                  [
                    charts.Series(
                      id: '警情占比',
                      data: cases,
                      domainFn: (data, _) =>
                          "${data.name} ${data.percent ?? 0}%",
                      measureFn: (data, _) => data.alarmCount ?? 0,
                    )
                  ],
                  showBehavior: true,
                )
        ],
      ),
    );
  }
}
