import 'person.dart';

class Organize {
  String? name;
  List<Person>? person;

  Organize({this.name, this.person});

  factory Organize.fromJson(Map<String, dynamic> json) => Organize(
        name: json['name'] as String?,
        person: (json['person'] as List<dynamic>?)
                ?.map((e) => Person.fromJson(e as Map<String, dynamic>))
                .toList() ??
            <Person>[],
      );

  Map<String, dynamic> toJson() => {
        'name': name,
        'person': person?.map((e) => e.toJson()).toList(),
      };
}
