import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controller.dart';

class NavButton extends StatelessWidget {
  const NavButton({super.key, required this.globalKey});
  final List<GlobalKey> globalKey;

  @override
  Widget build(BuildContext context) {
    List<String> texts = ['指挥长', '派出所', '介绍', '地图', '荣誉一', '荣誉二', '荣誉三', '荣誉墙'];

    return Padding(
      padding: const EdgeInsets.only(top: 70, right: 10),
      child: SizedBox(
        width: 60,
        child: ListView.separated(
          shrinkWrap: true,
          separatorBuilder: (_, __) => const SizedBox(height: 20),
          itemCount: texts.length,
          itemBuilder: (_, index) => InkWell(
            onTap: () =>
                Get.find<HomeController>().scrollToOffset(globalKey[index]),
            child: Text(
              texts[index],
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Color(0xd8cfe0ff), //,
                fontSize: 17,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
