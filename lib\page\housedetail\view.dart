import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/house_model.dart';
import 'package:police/common/widget/form_input.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/scaffold_widget.dart';
import 'package:police/page/housedetail/widgets/feedback.dart';
import 'package:police/page/housedetail/widgets/float_button.dart';
import 'package:police/page/housedetail/widgets/focus_people.dart';
import 'package:police/page/housedetail/widgets/house_info.dart';
import 'package:police/page/housedetail/widgets/safe_info.dart';
import 'index.dart';
import 'widgets/house_other_info.dart';
import 'widgets/people_info.dart';

class HousedetailPage extends GetView<HousedetailController> {
  const HousedetailPage({
    super.key,
    required this.detail,
    required this.unitId,
  });

  final HouseModel detail;
  final int unitId;

  // 主视图
  Widget _buildView() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(60),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FormInput(
              label: '入户地址',
              useMaxHeight: false,
              defaultText: detail.address ?? '',
              fontSize: 30,
              onChanged: (text) => detail.address = text,
            ),
            const SizedBox(height: 50),
            HouseInfo(detail: detail),
            const SizedBox(height: 50),
            PeopleInfo(houseId: detail.id),
            const SizedBox(height: 50),
            // OtherPeopleInfo(),
            // const SizedBox(height: 50),
            HouseOtherInfo(detail: detail),
            const SizedBox(height: 50),
            SafeInfo(detail: detail),
            const SizedBox(height: 50),
            FocusPeople(detail: detail),
            const SizedBox(height: 50),
            FeedBack(detail: detail),
            const SizedBox(height: 50),
            const Text(
              '民辅警日志(其他需要记录的内容)',
              style: TextStyle(fontSize: 40, color: Color(0xffffffff)),
            ),
            const SizedBox(height: 10),
            InputText(
              defaultText: detail.graphics ?? '',
              maxLines: null,
              minLines: 3,
              fontSize: 20,
              fontColor: const Color(0xffffffff),
              onChanged: (text) => detail.graphics = text,
            )
            // FormInput(label: 'label')
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (detail.id != null) {
      Get.put(HousedetailController()).house = detail;
      Get.put(HousedetailController()).getPeopleRela(detail.id!);
    } else {
      Get.put(HousedetailController()).code = 200;
      // Get.put(HousedetailController()).update([HousedetailIDS.peoples]);
    }
    return GetBuilder<HousedetailController>(
      init: HousedetailController(),
      id: HousedetailIDS.housedetail,
      builder: (_) {
        return ScaffoldWidget(
          body: _buildView(),
          floatButton: [
            FloatButton(
              controller: controller,
              houseId: controller.house.id,
              house: detail,
              unitId: unitId,
            )
          ],
          backResult: controller.house,
        );
      },
    );
  }
}
