import 'package:encrypt/encrypt.dart';
import 'package:police/common/util/shared.dart';

class CryptUtil {
  // static String _keyStr =
  //     "]9\xb8\x97!\x90.\x84r\xc6\xb48T>\xbb\xc0\x99\xc4\xf2\x19'?Z3\xaaV2,G\xbcj\x80";

  // static final _key = Key(Uint8List.fromList(_keyStr.codeUnits));
  static late Key _key;

  /// 解密
  static Future<String> decrypt(String data, String iv) async {
    _key = Key.fromBase16(await SharedUtil.getString('keyStr'));
    // _key = Key.fromLength(16);
    final encrypter = Encrypter(AES(_key, mode: AESMode.cbc));
    final decrypted =
        encrypter.decrypt(Encrypted.fromBase64(data), iv: IV.fromBase64(iv));
    return decrypted;
  }

  /// 加密
  static Future<String> encrypt(String data, String iv) async {
    _key = Key.fromBase16(await SharedUtil.getString('keyStr'));
    final encrypter = Encrypter(AES(_key, mode: AESMode.cbc));
    final encrypted = encrypter.encrypt(data, iv: IV.fromBase64(iv));
    return encrypted.base64;
  }
}
