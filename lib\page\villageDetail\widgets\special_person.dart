import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/village_model/person_interest_detail.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/float_square_button.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/page/peopledetail/index.dart';
import 'package:police/page/villageDetail/index.dart';

class SpecialPerson extends StatelessWidget {
  const SpecialPerson({
    super.key,
    required this.specialPerson,
    required this.specialPersonDetail,
  });

  final List<dynamic> specialPerson;
  final List<PersonInterestDetail> specialPersonDetail;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GetBuilder<VillagedetailController>(
          id: VillageDetailControllerIDS.specialPersonType,
          builder: (controller) {
            return Column(
              children: List.generate(specialPerson.length, (index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 40),
                  child: _buildSpecialPersonItem(context, index, controller),
                );
              }),
            );
          },
        ),
        const SizedBox(height: 40),
        NormolButton(
          lable: '新增关注人员组',
          width: 300,
          onTap: _addSpecialPersonGroup,
          color: const Color(0xca073773),
        ),
      ],
    );
  }

  /// 构建单个关注人员项
  Widget _buildSpecialPersonItem(
      context, int index, VillagedetailController controller) {
    final double screenHeight = MediaQuery.of(context).size.height;

    return Container(
      key: controller.peopleKeys[index],
      constraints: BoxConstraints(
        minHeight: screenHeight,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center, // 垂直居中
        children: [
          Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4)),
                  color: Color(0xca073773),
                ),
                padding:
                    const EdgeInsets.symmetric(vertical: 30, horizontal: 70),
                child: Column(
                  children: [
                    InputText(
                      defaultText: specialPerson[index],
                      textAlign: TextAlign.center,
                      canEdit: specialPerson[index] != '重点人员',
                      style: const TextStyle(color: Colors.white, fontSize: 28),
                      onChanged: (text) {
                        specialPerson[index] = text;
                        controller.isSave = false;
                        controller.update([VillageDetailControllerIDS.menu]);
                      },
                    ),
                    SizedBox(
                      height: specialPersonDetail[index].list!.isEmpty ? 0 : 20,
                    ),
                    Wrap(
                      spacing: 20,
                      runSpacing: 20,
                      children: List.generate(
                        specialPersonDetail[index].list!.length,
                        (i) {
                          return InkWell(
                            onTap: () => jump2page(PeopledetailPage(
                                specialPersonDetail[index].list![i])),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                ClipOval(
                                  child: ImageWidget.network(
                                    specialPersonDetail[index].list![i].photo ??
                                        '',
                                    width: 100,
                                    height: 100,
                                  ),
                                ),
                                const SizedBox(height: 14),
                                Text(
                                  specialPersonDetail[index].list![i].name ??
                                      '',
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    shadows: [
                                      Shadow(
                                        blurRadius: 10.0,
                                        color: Colors.black,
                                        offset: Offset(2.0, 2.0),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: specialPerson[index] != '重点人员',
                child: Align(
                  alignment: Alignment.topRight,
                  child: FloatSquareButton(
                    onTap: () {
                      _remove(index);
                      Get.find<VillagedetailController>()
                          .peopleKeys
                          .removeAt(index);
                      Get.find<VillagedetailController>().update([
                        VillageDetailControllerIDS.specialPersonType,
                        VillageDetailControllerIDS.menu
                      ]);
                    },
                    margin: const EdgeInsets.all(10),
                    color: const Color(0x883875a6),
                    icon: Icons.delete,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 添加新的关注人员组
  void _addSpecialPersonGroup() {
    specialPerson.add('');
    specialPersonDetail.add(PersonInterestDetail(name: '', list: []));
    Get.find<VillagedetailController>().peopleKeys.add(GlobalKey());
    Get.find<VillagedetailController>().update([
      VillageDetailControllerIDS.specialPersonType,
      VillageDetailControllerIDS.menu
    ]);
  }

  /// 删除关注人员组
  void _remove(int index) {
    specialPerson.removeAt(index);
    specialPersonDetail.removeAt(index);
    Get.find<VillagedetailController>()
        .update([VillageDetailControllerIDS.specialPersonType]);
  }
}
