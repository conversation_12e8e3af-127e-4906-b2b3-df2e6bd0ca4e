import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:police/common/model/house_model.dart';
import 'package:police/common/widget/form_input.dart';

import '../../../common/widget/check_box.dart';
import '../../../common/widget/radio.dart';
import '../controller.dart';

class FocusPeople extends StatelessWidget {
  const FocusPeople({super.key, required this.detail});

  final HouseModel detail;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HousedetailController>(
        id: HousedetailIDS.focusPeople,
        builder: (contro) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '关注人员类别',
                style: TextStyle(fontSize: 40, color: Color(0xffffffff)),
              ),
              const SizedBox(height: 10),
              Wrap(
                spacing: 20,
                runSpacing: 20,
                children: [
                  RadioWidget(
                    label: '境外停居留人员',
                    radios: const ['外国人', '华侨', '香港澳门台湾地区居民'],
                    valueGroup: detail.overseasResident ?? "",
                    onChanged: (val) {
                      detail.overseasResident = val;
                      contro.update([HousedetailIDS.focusPeople]);
                    },
                  ),
                  FormInput(
                    label: '填写居留证件相关信息',
                    defaultText: detail.overseasDocument ?? '',
                    maxLines: 999,
                    fontSize: 20,
                    textInputAction: TextInputAction.newline,
                    maxHeight: 200,
                    onChanged: (text) => detail.overseasDocument = text,
                  ),
                  CheckBoxWidegt(
                    label: '特殊人群',
                    chekcs: const [
                      '投资失败',
                      '生活失意',
                      '心理失衡',
                      '关系失和',
                      '精神失常',
                      '少年失管',
                      '低保户',
                      '低收入家庭',
                      '失独人员',
                      '残疾人员',
                      '长期失业',
                    ],
                    checked: detail.specialPopulation,
                    onChanged: (val, text) {
                      if (val) {
                        detail.specialPopulation.add(text);
                      } else {
                        detail.specialPopulation.remove(text);
                      }
                      contro.update([HousedetailIDS.focusPeople]);
                    },
                  ),
                  CheckBoxWidegt(
                    label: '困难群体',
                    chekcs: const [
                      '孤寡老人',
                      '事实无人抚养儿童',
                      '留守儿童',
                      '空巢老人',
                    ],
                    checked: detail.disadvantagedGroup,
                    onChanged: (val, text) {
                      if (val) {
                        detail.disadvantagedGroup.add(text);
                      } else {
                        detail.disadvantagedGroup.remove(text);
                      }
                      contro.update([HousedetailIDS.focusPeople]);
                    },
                  ),
                ],
              ),
            ],
          );
        });
  }
}
