import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/user_api.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/enums.dart';
import 'package:police/common/model/user_info_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/sort.dart';
import 'package:police/common/util/shared.dart';
import 'package:police/page/login/index.dart';

import '../home/<USER>';

class UserController extends GetxController {
  int code = 0;

  /// 用户权限
  Map<String, String> permissions = {
    'user': '普通用户',
    'advanced': '高级用户',
  };

  UserInfoModel userInfo = UserInfoModel();
  List<UserInfoModel> userList = [];

  /// 获取用户列表
  Future<List<UserInfoModel>> getUserList({bool isSir = false}) async {
    code = 0;
    update(['list']);
    userList = await UserApi.getUserList(
      isSir: isSir,
      fail: (result) {
        code = 404;
        update(['list']);
      },
    );

    userList.sort(
        (a, b) => a.isCommand == b.isCommand ? 0 : (a.isCommand ? -1 : 1));

    // 获取指挥人员
    Get.find<HomeController>().sir =
        userList.where((element) => element.isCommand).toList();

    code = 200;
    update(['list']);

    return userList;
  }

  /// 更改密码
  changPWD(BuildContext context, {required String password}) async {
    Dialogs.showLoadingDialog(context);
    await UserApi.changedPwd(password);
    showToast('修改成功');
  }

  /// 高级用户更改密码
  changePWDForAdmin(
    BuildContext context, {
    required int id,
    required String password,
  }) async {
    Dialogs.showLoadingDialog(context);
    await UserApi.changedPwdAdmin(password: password, id: id);
    showToast('修改成功');
  }

  /// 退出登录
  void logout(BuildContext context) {
    Dialogs.showConfirmDialog(
      context,
      title: '是否确认退出登录',
      onConfirm: () => sureLogout(),
    );
  }

  void sureLogout() async {
    await SharedUtil.setString('token', '');
    LoginController controller = Get.put(LoginController());
    controller.isLogin = false;
    // controller.userInfo = UserInfoModel();
    controller.token = '';
    userInfo = UserInfoModel();
    jump2page(const LoginPage(), jumpPageType: JumpPageType.offAll);
  }

  /// 更改用户头像
  updateAvatar(BuildContext context, Map<String, dynamic> userInfo) async {
    Dialogs.showLoadingDialog(context);
    await UserApi.updateUserInfo(userInfo);
    showToast('保存成功');
  }

  /// 冻结/解冻用户
  Future<UserInfoModel> freezeUser(
      BuildContext context, UserInfoModel info) async {
    Dialogs.showConfirmDialog(
      context,
      title: '是否${info.isActive ? '冻结' : '解冻'} ',
      subTitle: info.username,
      onConfirm: () async {
        Dialogs.showLoadingDialog(context);
        await UserApi.freezeUser(
          id: info.id!,
          isActive: !info.isActive,
          fail: (result) {
            // showToast('操作失败');
            return;
          },
        );
        info.isActive = !info.isActive;
        showToast('操作成功');
        update(['list']);
      },
    );
    return info;
  }
}
