import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/widget/form_input.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/page/user/controller.dart';

class PasswordManager extends StatelessWidget {
  const PasswordManager({super.key});

  @override
  Widget build(BuildContext context) {
    TextEditingController old = TextEditingController();
    TextEditingController new1 = TextEditingController();
    TextEditingController new2 = TextEditingController();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '更改密码',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xd8cfe0ff),
          ),
        ),
        const SizedBox(height: 10),
        // FormInput(
        //   label: '旧密码',
        //   controller: old,
        //   obscureText: true,
        //   maxLength: 16,
        // ),
        FormInput(
          controller: new1,
          label: '新密码',
          obscureText: true,
          maxLength: 16,
        ),
        FormInput(
          controller: new2,
          label: '确认新密码',
          obscureText: true,
          maxLength: 16,
        ),
        const SizedBox(height: 10),
        NormolButton(
          lable: '确定更改',
          width: 300,
          onTap: () {
            if (new1.text != new2.text) {
              showToast('两次输入的密码不一致');
              return;
            }
            if (new1.text.trim().isEmpty || new2.text.trim().isEmpty) {
              showToast('请不要留空');
              return;
            }
            Get.find<UserController>()
                .changPWD(context, password: new2.text.trim());
          },
        ),
      ],
    );
  }
}
