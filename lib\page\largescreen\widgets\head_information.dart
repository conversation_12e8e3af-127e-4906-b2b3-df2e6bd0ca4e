import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/page/largescreen/controller.dart';

import '../../foucs_peoples/view.dart';
import '../../main/controller.dart';
import '../../peoples/controller.dart';

class HeadInformation extends StatelessWidget {
  const HeadInformation({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    const TextStyle style = TextStyle(color: Color(0xD8FFFFFF));
    return Container(
      padding: const EdgeInsets.all(12),
      child: GetBuilder<LargescreenController>(
          id: LargescernnIDS.peoples,
          builder: (controller) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "今日案件与警情",
                      style: TextStyle(color: Color(0xffb5b7c0)),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                            controller.bigscreenData.todayAlarmCount!
                                .toString(),
                            style: const TextStyle(
                                color: Color(0xffffffff), fontSize: 38)),
                        const Text(" 起",
                            style: TextStyle(
                              color: Color(0xffffffff),
                              fontSize: 14,
                              height: 2.5,
                            ))
                      ],
                    )
                  ],
                ),
                InkWell(
                  onTap: () {
                    // Get.find<MainController>().tabController.animateTo(1);
                    // Get.put(PeoplesController()).sortFoucs = true;
                    // Get.put(PeoplesController())
                    //     .update([PeoplesControllerIDS.category]);
                    // Get.put(PeoplesController()).getPeopleList(page: 1);
                    jump2page(const FoucsPeoplesPage());
                  },
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "重点人员",
                        style: TextStyle(color: Color(0xffb5b7c0)),
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                              controller.bigscreenData.keyPersonnelCount
                                  .toString(),
                              style: const TextStyle(
                                  color: Color(0xffffffff), fontSize: 38)),
                          const Text(" 人",
                              style: TextStyle(
                                color: Color(0xffffffff),
                                fontSize: 14,
                                height: 2.5,
                              ))
                        ],
                      )
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "警情发生区",
                      style: TextStyle(color: Color(0xffb5b7c0)),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(controller.bigscreenData.villageCount!.toString(),
                            style: const TextStyle(
                                color: Color(0xffffffff), fontSize: 38)),
                        const Text(" 个",
                            style: TextStyle(
                              color: Color(0xffffffff),
                              fontSize: 14,
                              height: 2.5,
                            ))
                      ],
                    )
                  ],
                ),
              ],
            );
          }),
    );
  }
}
