import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class SlideAnimationWidget extends StatefulWidget {
  const SlideAnimationWidget({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    // required this.width,
    required this.actions,
  });

  final Widget child;
  final List<Widget> actions;
  final Duration duration;
  // final double width;

  @override
  State<SlideAnimationWidget> createState() => _SlideAnimationWidgetState();
}

class _SlideAnimationWidgetState extends State<SlideAnimationWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);

    _animation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (event) => _controller.forward(),
      onExit: (event) => _controller.reverse(),
      child: Column(
        children: [
          widget.child,
          SizeTransition(
            sizeFactor: _animation,
            child: Row(
              children: widget.actions.map((e) {
                return Expanded(child: e);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
