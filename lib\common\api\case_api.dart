import 'package:dio/dio.dart';
import 'package:police/common/model/case_model.dart';
import 'package:get/get.dart' hide Response;
import 'package:police/page/cases/controller.dart';
import '../util/https.dart';
import 'apis.dart';

class CaseApi {
  /// 获取案件列表
  static Future<List<CaseModel>> getCaseList({
    bool pageBack = false,
    Function(Response)? fail,
    int page = 1,
    String year = '',
    String month = '',
    String day = '',
    String? caseType,
  }) async {
    final List<CaseModel> caseList = [];
    Response results = await Https.get(
      Apis.caseApi,
      queryParameters: {
        'page': page,
        'year': year,
        'month': month,
        'day': day,
        'size': 20,
        'types': caseType,
      },
      pageBack: pageBack,
      fail: fail,
    );

    for (var caseItem in results.data['items']) {
      caseList.add(CaseModel.fromJson(caseItem));
    }

    Get.find<CasesController>().maxpages = results.data['pages'];

    return caseList;
  }

  /// 新增案件
  static Future<CaseModel> createCase(Map<String, dynamic> detail) async {
    Response result = await Https.post(
      Apis.caseApi,
      contentType: 'application/json',
      data: detail,
      pageBack: true,
    );

    return CaseModel.fromJson(result.data);
  }

  /// 获取案件详情
  static Future<CaseModel> getCaseDetail(int id,
      {bool pageBack = false}) async {
    Response result =
        await Https.get('${Apis.caseApi}/$id', pageBack: pageBack);

    return CaseModel.fromJson(result.data);
  }

  /// 修改案件详情
  static Future<CaseModel> patchCaseDetail(int id, Map<String, dynamic> data,
      {bool pageBack = false}) async {
    Response result = await Https.patch(
      '${Apis.caseApi}/$id',
      data: data,
      pageBack: pageBack,
    );

    return CaseModel.fromJson(result.data);
  }

  /// 删除案件
  static Future deleteCase(int id, {bool pageBack = false}) async {
    await Https.delete('${Apis.caseApi}/$id', pageBack: pageBack);
  }
}
