import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:police/common/model/people_detail_model.dart';
import 'package:police/common/widget/radio.dart';
import 'package:police/page/peopledetail/controller.dart';

class PeopleOtherInfo extends StatelessWidget {
  const PeopleOtherInfo(
      {super.key, required this.controller, required this.detail});

  final PeopledetailController controller;
  final PeopleDetailModel detail;

  @override
  Widget build(BuildContext context) {
    List<String> beliefs = const ['佛教', '基督', '伊斯兰', '无', '其他'];
    return GetBuilder<PeopledetailController>(
        id: PeopledetailControllerIDS.peopleOtherInfo,
        builder: (controller) {
          return Wrap(
            spacing: 30,
            runSpacing: 30,
            children: [
              Visibility(
                visible: !detail.isOrganize!,
                child: GetBuilder<PeopledetailController>(
                    id: PeopledetailControllerIDS.peopleType,
                    builder: (context) {
                      return RadioWidget(
                        label: '重点人员',
                        fontSize: 17,
                        onChanged: (val) {
                          detail.isKeyPersonnel = val == '是' ? true : false;
                          // _update();
                          controller.update([
                            PeopledetailControllerIDS.peopleType,
                            PeopledetailControllerIDS.foucsPeopleInfo,
                            'detail',
                          ]);
                        },
                        valueGroup: detail.isKeyPersonnel ? '是' : '否',
                        radios: const ['是', '否'],
                      );
                    }),
              ),
              RadioWidget(
                label: '人户分离',
                // margin: const EdgeInsets.all(10),
                onChanged: (val) {
                  detail.residenceSeparation = !detail.residenceSeparation;
                  _update();
                },
                valueGroup: detail.residenceSeparation ? '是' : '否',
                radios: const ['是', '否'],
              ),
              RadioWidget(
                label: '政治面貌',
                // margin: const EdgeInsets.all(10),
                onChanged: (val) {
                  detail.politicalStatus = val;
                  _update();
                },
                valueGroup: detail.politicalStatus!,
                radios: const ['党员', '团员', '其他党派', '群众'],
              ),
              // RadioWidget(
              //   label: '反诈 APP 是否下载',
              //   onChanged: (val) {},
              //   valueGroup: '0',
              //   radios: const ['是', '否'],
              //   // margin: const EdgeInsets.all(10),
              // ),
              RadioWidget(
                label: '婚姻状况',
                onChanged: (val) {
                  detail.maritalStatus = val;
                  _update();
                },
                valueGroup: detail.maritalStatus!,
                radios: const ['未婚', '已婚', '离异', '丧偶'],
                // margin: const EdgeInsets.all(10),
              ),
              RadioWidget(
                label: '兵役状况',
                onChanged: (val) {
                  detail.veteranStatus = val;
                  _update();
                },
                valueGroup: detail.veteranStatus!,
                radios: const ['未服', '退出', '预备', '未知'],
                // margin: const EdgeInsets.all(10),
              ),
              RadioWidget(
                label: '宗教信仰',
                onChanged: (val) {
                  detail.belief = val;
                  _update();
                },
                valueGroup:
                    !beliefs.contains(detail.belief) ? '其他' : detail.belief!,
                radios: beliefs,
                otherText:
                    !beliefs.contains(detail.belief) ? detail.belief! : '',
                canEdit: (value) =>
                    value == detail.belief || !beliefs.contains(detail.belief),
                onChangedOther: (text, index) {
                  detail.belief = text.trim();
                  _update();
                },
                // margin: const EdgeInsets.all(10),
              ),
              RadioWidget(
                label: '群防群治人员',
                // margin: const EdgeInsets.all(10),
                onChanged: (val) {
                  detail.isOrganize = val == '是' ? true : false;
                  controller.update([PeopledetailControllerIDS.peopleInfo]);
                  _update();
                },
                valueGroup: detail.isOrganize! ? '是' : '否',
                radios: const ['是', '否'],
              ),
            ],
          );
        });
  }

  void _update() {
    // controller.isSave = false;
    controller.update([PeopledetailControllerIDS.peopleOtherInfo]);
  }
}
