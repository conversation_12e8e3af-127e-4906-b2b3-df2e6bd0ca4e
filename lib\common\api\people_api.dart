import 'package:dio/dio.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart' as getx;
import 'package:police/common/api/apis.dart';
import 'package:police/common/model/people_detail_model.dart';
import 'package:police/common/util/https.dart';
import 'package:police/page/peoples/index.dart';

class PeopleApi {
  /// 获取人员列表
  static Future<List<PeopleDetailModel>> getPeopleList({
    bool pageBack = false,
    Map<String, dynamic>? parameters,
    Function(Response)? fail,
    bool fromFoucs = false,
  }) async {
    final List<PeopleDetailModel> peopleList = [];

    Response results = await Https.get(Apis.person,
        pageBack: pageBack, queryParameters: parameters, fail: fail);
    for (var people in results.data['items']) {
      peopleList.add(PeopleDetailModel.fromJson(people));
    }

    // 修改这里的逻辑，确保使用同一个controller实例
    final controller = getx.Get.find<PeoplesController>();
    if (fromFoucs) {
      controller.fromFoucsMaxpage = results.data['pages'];
    } else {
      controller.maxpage = results.data['pages'];
    }

    return peopleList;
  }

  /// 新增人员
  static Future<PeopleDetailModel> createPeople(Map<String, dynamic> detail,
      {bool pageBack = false}) async {
    Response result = await Https.post(
      Apis.person,
      contentType: 'application/json',
      data: detail,
      pageBack: pageBack,
    );

    return PeopleDetailModel.fromJson(result.data);
  }

  /// 获取人员详情
  static Future<PeopleDetailModel> getPeopleDetail(int id,
      {bool pageBack = false}) async {
    Response result = await Https.get('${Apis.person}/$id', pageBack: pageBack);

    return PeopleDetailModel.fromJson(result.data);
  }

  /// 获取重点人员信息
  static Future<Map<String, dynamic>> getFocusPeopleInfo(
      {Function(Response)? fail}) async {
    Response result = await Https.get(
      Apis.focusPeople,
      fail: (response) => fail?.call(response),
    );
    return result.data;
  }

  /// 修改人员详情
  static Future<PeopleDetailModel> patchPeopleDetail(
      int id, Map<String, dynamic> data,
      {bool pageBack = false}) async {
    Response result = await Https.patch(
      '${Apis.person}/$id',
      data: data,
      pageBack: pageBack,
    );

    return PeopleDetailModel.fromJson(result.data);
  }

  /// 删除人员
  static Future deletePeople(int id) async {
    await Https.delete(
      '${Apis.person}/$id',
      pageBack: true,
      // fail: (p0) => showToast('删除失败')
      fail: (p0) {},
    );
  }
}
