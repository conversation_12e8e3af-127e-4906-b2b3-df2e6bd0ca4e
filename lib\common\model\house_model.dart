class HouseModel {
  int? id;
  int? villageUnitId;
  String? address;
  String? propertyOwner;
  String? propertyTelephone;
  String? propertyIdNumber;
  String housingPurposes;
  bool residencePermit;
  int? numberResidents;
  String? graphics;
  bool isAppDownload;
  String? dogSize;
  bool dogDocuments;
  List<dynamic> vehicle;
  bool domesticViolence;
  bool cohabitantIssues;
  bool needsCounseling;
  String? caseStatus;
  String? overseasResident;
  String? overseasDocument;
  List<dynamic> specialPopulation;
  List<dynamic> disadvantagedGroup;
  List<dynamic> recentAlerts;
  List<dynamic> feedback;
  String? feedbackSummary;
  bool securityRisk;
  List<dynamic> securityConflicts;
  List<dynamic> securityPublicSafety;
  List<dynamic> securityDisposalStatus;
  DateTime? createTime;
  DateTime? updateTime;
  String? vehicleAdditional;
  String? recentAlertsAdditional;
  String? feedbackAdditional;
  String? securityConflictsAdditional;
  String? securityPublicSafetyAdditional;
  String? securityDisposalStatusAdditional;

  HouseModel(
      {this.id,
      this.villageUnitId,
      this.address,
      this.propertyOwner,
      this.propertyTelephone,
      this.propertyIdNumber,
      this.housingPurposes = '自住',
      this.residencePermit = true,
      this.numberResidents,
      this.graphics,
      this.isAppDownload = false,
      this.dogSize,
      this.dogDocuments = false,
      List<dynamic>? vehicle,
      this.domesticViolence = false,
      this.cohabitantIssues = false,
      this.needsCounseling = false,
      this.caseStatus,
      this.overseasResident,
      this.overseasDocument,
      List<dynamic>? specialPopulation,
      List<dynamic>? disadvantagedGroup,
      List<dynamic>? recentAlerts,
      List<dynamic>? feedback,
      this.feedbackSummary,
      this.securityRisk = false,
      List<dynamic>? securityConflicts,
      List<dynamic>? securityPublicSafety,
      List<dynamic>? securityDisposalStatus,
      this.createTime,
      this.updateTime,
      this.vehicleAdditional,
      this.securityPublicSafetyAdditional,
      this.securityDisposalStatusAdditional,
      this.securityConflictsAdditional,
      this.recentAlertsAdditional,
      this.feedbackAdditional})
      : vehicle = vehicle ?? [],
        specialPopulation = specialPopulation ?? [],
        disadvantagedGroup = disadvantagedGroup ?? [],
        recentAlerts = recentAlerts ?? [],
        feedback = feedback ?? [],
        securityConflicts = securityConflicts ?? [],
        securityPublicSafety = securityPublicSafety ?? [],
        securityDisposalStatus = securityDisposalStatus ?? [];

  factory HouseModel.fromJson(Map<String, dynamic> json) => HouseModel(
        id: json['id'] as int?,
        villageUnitId: json['village_unit_id'] as int?,
        address: json['address'] as String?,
        propertyOwner: json['property_owner'] as String?,
        propertyTelephone: json['property_telephone'] as String?,
        propertyIdNumber: json['property_id_number'] as String?,
        housingPurposes: json['housing_purposes'] as String,
        residencePermit: json['residence_permit'] as bool,
        numberResidents: json['number_residents'] as int?,
        graphics: json['graphics'] as String?,
        isAppDownload: json['is_app_download'] as bool,
        dogSize: json['dog_size'] as String?,
        dogDocuments: json['dog_documents'] as bool,
        vehicle: json['vehicle'] as List<dynamic>?,
        domesticViolence: json['domestic_violence'] as bool,
        cohabitantIssues: json['cohabitant_issues'] as bool,
        needsCounseling: json['needs_counseling'] as bool,
        caseStatus: json['case_status'] as String?,
        overseasResident: json['overseas_resident'] as String?,
        overseasDocument: json['overseas_document'] as String?,
        specialPopulation: json['special_population'] as List<dynamic>?,
        disadvantagedGroup: json['disadvantaged_group'] as List<dynamic>?,
        recentAlerts: json['recent_alerts'] as List<dynamic>?,
        feedback: json['feedback'] as List<dynamic>?,
        feedbackSummary: json['feedback_summary'] as String?,
        securityRisk: json['security_risk'] as bool,
        securityConflicts: json['security_conflicts'] as List<dynamic>?,
        securityPublicSafety: json['security_public_safety'] as List<dynamic>?,
        securityDisposalStatus:
            json['security_disposal_status'] as List<dynamic>?,
        createTime: json['create_time'] == null
            ? null
            : DateTime.parse(json['create_time'] as String),
        updateTime: json['update_time'] == null
            ? null
            : DateTime.parse(json['update_time'] as String),
        vehicleAdditional: json['vehicle_additional'] as String?,
        securityPublicSafetyAdditional:
            json['security_public_safety_additional'] as String?,
        securityDisposalStatusAdditional:
            json['security_disposal_status_additional'] as String?,
        securityConflictsAdditional:
            json['security_conflicts_additional'] as String?,
        recentAlertsAdditional: json['recent_alerts_additional'] as String?,
        feedbackAdditional: json['feedback_additional'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'village_unit_id': villageUnitId,
        'address': address,
        'property_owner': propertyOwner,
        'property_telephone': propertyTelephone,
        'property_id_number': propertyIdNumber,
        'housing_purposes': housingPurposes,
        'residence_permit': residencePermit,
        'number_residents': numberResidents,
        'graphics': graphics,
        'is_app_download': isAppDownload,
        'dog_size': dogSize,
        'dog_documents': dogDocuments,
        'vehicle': vehicle,
        'domestic_violence': domesticViolence,
        'cohabitant_issues': cohabitantIssues,
        'needs_counseling': needsCounseling,
        'case_status': caseStatus,
        'overseas_resident': overseasResident,
        'overseas_document': overseasDocument,
        'special_population': specialPopulation,
        'disadvantaged_group': disadvantagedGroup,
        'recent_alerts': recentAlerts,
        'feedback': feedback,
        'feedback_summary': feedbackSummary,
        'security_risk': securityRisk,
        'security_conflicts': securityConflicts,
        'security_public_safety': securityPublicSafety,
        'security_disposal_status': securityDisposalStatus,
        'create_time': createTime?.toIso8601String(),
        'update_time': updateTime?.toIso8601String(),
        'vehicle_additional': vehicleAdditional,
        'security_public_safety_additional': securityPublicSafetyAdditional,
        'security_disposal_status_additional': securityDisposalStatusAdditional,
        'security_conflicts_additional': securityConflictsAdditional,
        'recent_alerts_additional': recentAlertsAdditional,
        'feedback_additional': feedbackAdditional,
      };

  HouseModel copyWith({
    int? id,
    int? villageUnitId,
    String? address,
    String? propertyOwner,
    String? propertyTelephone,
    String? propertyIdNumber,
    String housingPurposes = '自住',
    bool residencePermit = true,
    int? numberResidents,
    String? graphics,
    bool isAppDownload = false,
    String? dogSize,
    bool dogDocuments = false,
    List<dynamic>? vehicle,
    bool domesticViolence = false,
    bool cohabitantIssues = false,
    bool needsCounseling = false,
    String? caseStatus,
    String? overseasResident,
    String? overseasDocument,
    List<dynamic>? specialPopulation,
    List<dynamic>? disadvantagedGroup,
    List<dynamic>? recentAlerts,
    List<dynamic>? feedback,
    String? feedbackSummary,
    bool securityRisk = false,
    List<dynamic>? securityConflicts,
    List<dynamic>? securityPublicSafety,
    List<dynamic>? securityDisposalStatus,
    DateTime? createTime,
    DateTime? updateTime,
    String? vehicleAdditional,
    String? securityPublicSafetyAdditional,
    String? securityDisposalStatusAdditional,
    String? securityConflictsAdditional,
    String? recentAlertsAdditional,
    String? feedbackAdditional,
  }) {
    return HouseModel(
      id: id ?? this.id,
      villageUnitId: villageUnitId ?? this.villageUnitId,
      address: address ?? this.address,
      propertyOwner: propertyOwner ?? this.propertyOwner,
      propertyTelephone: propertyTelephone ?? this.propertyTelephone,
      propertyIdNumber: propertyIdNumber ?? this.propertyIdNumber,
      housingPurposes: housingPurposes,
      residencePermit: residencePermit,
      numberResidents: numberResidents ?? this.numberResidents,
      graphics: graphics ?? this.graphics,
      isAppDownload: isAppDownload,
      dogSize: dogSize ?? this.dogSize,
      dogDocuments: dogDocuments,
      vehicle: vehicle,
      domesticViolence: domesticViolence,
      cohabitantIssues: cohabitantIssues,
      needsCounseling: needsCounseling,
      caseStatus: caseStatus ?? this.caseStatus,
      overseasResident: overseasResident ?? this.overseasResident,
      overseasDocument: overseasDocument ?? this.overseasDocument,
      specialPopulation: specialPopulation,
      disadvantagedGroup: disadvantagedGroup,
      recentAlerts: recentAlerts,
      feedback: feedback,
      feedbackSummary: feedbackSummary ?? this.feedbackSummary,
      securityRisk: securityRisk,
      securityConflicts: securityConflicts,
      securityPublicSafety: securityPublicSafety,
      securityDisposalStatus: securityDisposalStatus,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      vehicleAdditional: vehicleAdditional ?? this.vehicleAdditional,
      securityPublicSafetyAdditional:
          securityPublicSafetyAdditional ?? this.securityPublicSafetyAdditional,
      securityDisposalStatusAdditional: securityDisposalStatusAdditional ??
          this.securityDisposalStatusAdditional,
      securityConflictsAdditional:
          securityConflictsAdditional ?? this.securityConflictsAdditional,
      recentAlertsAdditional:
          recentAlertsAdditional ?? this.recentAlertsAdditional,
      feedbackAdditional: feedbackAdditional ?? this.feedbackAdditional,
    );
  }
}
