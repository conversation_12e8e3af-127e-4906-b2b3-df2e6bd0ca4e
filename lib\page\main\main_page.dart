import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/page/analyze/index.dart';
import 'package:police/page/cases/index.dart';
import 'package:police/page/home/<USER>';
import 'package:police/page/largescreen/index.dart';
import 'package:police/page/main/controller.dart';
import 'package:police/page/main/widgets/tab_bar.dart';
import 'package:police/page/peoples/index.dart';
import 'package:police/page/village/index.dart';

class MainPage extends StatelessWidget {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    MainController controller = Get.find();
    // 预加载图片
    precacheImage(const AssetImage('assets/pic/1121.png'), context);

    return Stack(
      children: [
        Image.asset(
          'assets/gif/bg.gif',
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover,
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          body: Stack(
            children: [
              Tab<PERSON><PERSON><PERSON>iew(
                controller: controller.tabController,
                physics: const NeverScrollableScrollPhysics(),
                children: const [
                  HomePage(),
                  PeoplesPage(),
                  // FactoryPage(),
                  VillagePage(),
                  CasesPage(),
                  LargescreenPage(),
                ],
              ),
              TabBarWidget(controller: controller),
            ],
          ),
        ),
      ],
    );
  }
}
