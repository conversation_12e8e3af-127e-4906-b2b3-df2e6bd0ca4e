import 'package:police/common/model/people_detail_model.dart';

class PersonInterestDetail {
  String? name;
  List<PeopleDetailModel>? list;

  PersonInterestDetail({this.name, this.list});

  factory PersonInterestDetail.fromJson(Map<String, dynamic> json) {
    return PersonInterestDetail(
      name: json['name'] as String?,
      list: json['list'] == null
          ? null
          : (json['list'] as List<dynamic>)
              .map((e) => PeopleDetailModel.fromJson(e as Map<String, dynamic>))
              .toList(),

      // list: (json['list'] as List<Map<String, dynamic>>?)
      //     ?.map((e) => (e as List<dynamic>)
      //         .map((e) => List.fromJson(e as Map<String, dynamic>))
      //         .toList())
      //     .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'list': list?.map((e) => e.toJson()).toList(),

        // 'list': list?.map((e) => e.map((e) => e.toJson()).toList()).toList(),
      };
}
