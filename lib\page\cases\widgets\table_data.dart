import 'dart:convert';

import 'package:fleather/fleather.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/util/date_format.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/rich_editor.dart';
import 'package:police/common/widget/right_click_menu.dart';
import 'package:police/page/cases/controller.dart';

import '../../../common/model/case_model.dart';
import '../../../common/routes/jump_page.dart';
import '../../../common/widget/corner_card.dart';
import '../../../common/widget/normol_button.dart';
import '../../../common/widget/scale_animated_widget.dart';

class TableData extends StatelessWidget {
  const TableData({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CasesController>(
      id: CaseControllerIDS.caseItem,
      builder: (controller) {
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: controller.cases.length,
          itemBuilder: (_, index) {
            return RightClickMenu(
              items: [
                PopupMenuItem(
                  value: '删除',
                  child: const Text('删除'),
                  onTap: () {
                    controller.deleteCase(
                      context,
                      index,
                      id: controller.cases[index].id,
                      name: controller.cases[index].title,
                    );
                  },
                ),
              ],
              child: Row(
                children: List.generate(controller.tabHeader.length, (i) {
                  return Expanded(
                    flex: i == 2 ? 3 : 2,
                    child: Container(
                      height: 50,
                      color: index % 2 == 1
                          ? const Color(0xd3021a37)
                          : const Color(0xd804254c),
                      alignment: Alignment.center,
                      margin: const EdgeInsets.all(.5),
                      child: InkWell(
                        onTapUp: (TapUpDetails details) {
                          if (i == 1 || i == 2) {
                            _showMenu(context, details.globalPosition, index,
                                controller, i);
                          } else if (i == 3) {
                            _showSelectedVillageMenu(
                                context, details, controller, index);
                          } else if (i == 5) {
                            Dialogs.showEditTextDialog(
                              context,
                              controller: TextEditingController(),
                              title: '详情',
                              defaultText: controller.cases[index].graphics,
                              onConfirm: (text) {
                                controller.cases[index].graphics = text;
                                controller.save(context);
                              },
                            );
                          } else if (i == 6) {
                            print(controller.cases[index].toJson());
                            showDialog(
                                context: context,
                                builder: (context) {
                                  return UnconstrainedBox(
                                    child: ScaleAnimatedWidget(
                                      begin: 0,
                                      end: 1,
                                      duration:
                                          const Duration(milliseconds: 300),
                                      userMouse: false,
                                      child: CornerCard(
                                        width: Get.width * .8,
                                        height: Get.height * .8,
                                        child: Padding(
                                          padding: const EdgeInsets.all(30),
                                          child: Column(
                                            children: [
                                              Expanded(
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child: RichEditor(
                                                        label: '警情处置反馈',
                                                        document: controller
                                                            .cases[index]
                                                            .policeFeedBack,
                                                        onDocumentChange:
                                                            (document) {
                                                          List<
                                                                  Map<String,
                                                                      dynamic>>
                                                              data = [];
                                                          for (var element
                                                              in document) {
                                                            data.add(element
                                                                .toJson());
                                                          }
                                                          controller
                                                                  .cases[index]
                                                                  .policeFeedBack =
                                                              data;
                                                        },
                                                      ),
                                                    ),
                                                    const VerticalDivider(),
                                                    Expanded(
                                                      child: RichEditor(
                                                        label: '指挥长盯办意见建议',
                                                        document: controller
                                                            .cases[index]
                                                            .opinions,
                                                        onDocumentChange:
                                                            (document) {
                                                          List<
                                                                  Map<String,
                                                                      dynamic>>
                                                              data = [];
                                                          for (var element
                                                              in document) {
                                                            data.add(element
                                                                .toJson());
                                                          }
                                                          controller
                                                              .cases[index]
                                                              .opinions = data;
                                                        },
                                                      ),
                                                    ),
                                                    const VerticalDivider(),
                                                    Expanded(
                                                      child: RichEditor(
                                                        label: '后续工作开展情况',
                                                        document: controller
                                                            .cases[index]
                                                            .finalFeedback,
                                                        onDocumentChange:
                                                            (document) {
                                                          List<
                                                                  Map<String,
                                                                      dynamic>>
                                                              data = [];
                                                          for (var element
                                                              in document) {
                                                            data.add(element
                                                                .toJson());
                                                          }
                                                          controller
                                                                  .cases[index]
                                                                  .finalFeedback =
                                                              data;
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                });
                          }
                        },
                        child: Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: i == 2 ? 40 : 0),
                          child: InputText(
                            textAlign: TextAlign.center,
                            rightClickMenu: false,
                            canEdit: i == 0 ||
                                i == 4 ||
                                controller.cases[index].sort == '' && i == 2,
                            maxLength: i == 4 ? 17 : null,
                            defaultText: _setDefaultText(i, controller, index),
                            style: const TextStyle(
                                color: Color(0xf0e5edff), fontSize: 16),
                            onChanged: (text) =>
                                _onChanged(text, controller, index, i),
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            );
          },
        );
      },
    );
  }

  /// 展示村庄列表
  void _showSelectedVillageMenu(BuildContext context, TapUpDetails details,
      CasesController controller, int index) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        details.globalPosition.dx,
        details.globalPosition.dy,
        overlay.size.width - details.globalPosition.dx,
        overlay.size.height - details.globalPosition.dy,
      ),
      items: List.generate(controller.villages.length, (index) {
        return PopupMenuItem(
          value: controller.villages[index],
          child: Text(
            controller.villages[index].name ?? '',
          ),
        );
      }),
    ).then((value) {
      if (value != null) {
        controller.cases[index].villageId = value.id;
        // controller.cases[index].graphics = value.name ?? '';
        controller.update([CaseControllerIDS.caseItem]);
      }
    });
  }

  String _setDefaultText(int i, CasesController controller, int index) {
    var caseItem = controller.cases[index];
    switch (i) {
      case 0:
        return caseItem.title ?? '';
      case 1:
        return caseItem.types ?? '';
      case 2:
        return caseItem.sort ?? '';
      case 3:
        // return caseItem.id == null ? '其他' : caseItem.graphics ?? '';
        // return _getVillageName(controller, caseItem);
        return controller.villageNameMap[caseItem.villageId] ?? '';
      case 5:
        return '详情';
      case 6:
        return '详情';
      default:
        // 如果 occurrenceTime 不为空则格式化日期，否则返回空字符串
        return caseItem.occurrenceTime == null
            ? ''
            : dateFormat(caseItem.occurrenceTime!);
    }
  }

  void _showMenu(BuildContext context, Offset globalPosition, int index,
      CasesController controller, int columnIndex) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    // 如果 columnIndex 是 2 且类型为空，则显示提示信息
    if (columnIndex == 2 && controller.cases[index].types == null) {
      showToast('请先选择类型');
      return;
    }

    // 生成菜单项列表
    List<PopupMenuEntry<String>> menuItems =
        _generateMenuItems(controller, index, columnIndex);

    // 显示菜单
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        globalPosition.dx,
        globalPosition.dy,
        overlay.size.width - globalPosition.dx,
        overlay.size.height - globalPosition.dy,
      ),
      items: menuItems,
    ).then((selectedValue) {
      // 处理菜单项的选择
      if (selectedValue != null) {
        _handleMenuSelection(controller, index, columnIndex, selectedValue);
      }
    });
  }

// 生成菜单项
  List<PopupMenuEntry<String>> _generateMenuItems(
      CasesController controller, int index, int columnIndex) {
    if (columnIndex == 1) {
      // 生成案件类型菜单项，显式转换为 List<String>
      List<String> keys = List<String>.from(controller.caseType.keys);
      return keys
          .map((key) => PopupMenuItem<String>(
                value: key,
                child: Text(key),
              ))
          .toList();
    } else {
      // 生成案件排序菜单项
      List<String> sorts = List<String>.from(
          controller.caseType[controller.cases[index].types] ?? []);
      return sorts
          .map((sort) => PopupMenuItem<String>(
                value: sort,
                child: Text(sort),
              ))
          .toList();
    }
  }

// 处理菜单项的选择
  void _handleMenuSelection(CasesController controller, int index,
      int columnIndex, String selectedValue) {
    var caseItem = controller.cases[index];
    switch (columnIndex) {
      case 1:
        // 处理案件类型选择
        if (caseItem.types != selectedValue) {
          caseItem.sort = ' '; // 如果选择的类型不同，清空排序
          controller.update([CaseControllerIDS.caseItem]);
        }
        caseItem.types = selectedValue;
        break;
      case 2:
        // 处理案件分类选择
        caseItem.sort = selectedValue == '其他' ? '' : selectedValue;
        break;
    }
    controller.update([CaseControllerIDS.caseItem]);
  }

  void _onChanged(String input, CasesController controller, int index, int i) {
    CaseModel caseItem = controller.cases[index];

    switch (i) {
      case 0:
        caseItem.title = input.trim();
        break;
      case 1:
        caseItem.types = input.trim();
        break;
      case 2:
        caseItem.sort = input.trim();

        break;
      // case 3:
      //   // caseItem.villageId = input;
      //   break;
      case 4:
        if (input.length == 12) {
          DateTime dateTime = DateTime.parse(
              '${input.substring(0, 4)}-${input.substring(4, 6)}-${input.substring(6, 8)} ${input.substring(8, 10)}:${input.substring(10, 12)}');
          controller.cases[index].occurrenceTime = dateTime;
          controller.update([CaseControllerIDS.caseItem]);
        } else if (input.length > 16) {
          try {
            String dateStr =
                '${input.replaceAll(RegExp(r'\s+'), ' ').trim()}:00';
            controller.cases[index].occurrenceTime = DateTime.parse(dateStr);
          } catch (e) {
            showToast('转换日期失败，请重新输入');
            controller.update([CaseControllerIDS.caseItem]);
          }
        }
        break;
    }
  }
}
