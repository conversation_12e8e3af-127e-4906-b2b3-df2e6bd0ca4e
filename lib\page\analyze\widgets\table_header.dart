import 'package:flutter/material.dart';

class AnalyzeTableHeader extends StatelessWidget {
  const AnalyzeTableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    List<String> header = ['日期', '研判详情'];

    return Row(
      children: header.map((e) {
        return Expanded(
            child: Container(
          color: const Color(0xd3041e3e),
          alignment: Alignment.center,
          margin: const EdgeInsets.all(.5),
          height: 70,
          child: Text(
            e,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.bold,
              color: Color(0xf0e5edff),
            ),
          ),
        ));
      }).toList(),
    );
  }
}
