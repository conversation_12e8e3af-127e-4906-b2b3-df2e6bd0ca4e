import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/drop_down.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/page/foucs_peoples/index.dart';
import 'package:police/page/peoples/controller.dart';

class Category extends StatelessWidget {
  const Category({super.key, this.fromFoucs = false});

  final bool fromFoucs;

  @override
  Widget build(BuildContext context) {
    PeoplesController controller = Get.find<PeoplesController>();
    TextEditingController searchContro = TextEditingController();

    return Padding(
      padding: const EdgeInsets.only(top: 100, bottom: 60, left: 50, right: 50),
      child: Container(
        height: 80,
        padding: const EdgeInsets.all(10),
        alignment: Alignment.center,
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                image: const DecorationImage(
                    image: AssetImage('assets/pic/234-2.png'),
                    fit: BoxFit.fill),
                borderRadius: BorderRadius.circular(10),
              ),
              width: 400,
              child: Row(
                children: [
                  GetBuilder<PeoplesController>(
                      id: PeoplesControllerIDS.category,
                      builder: (contro) {
                        return DropdownWidget(
                          hint: '',
                          selectedValue: fromFoucs
                              ? contro.fromFoucsSearchBy
                              : contro.searchBy,
                          items: contro.searchCategory,
                          onChanged: (val) {
                            fromFoucs
                                ? contro.fromFoucsSearchBy = val
                                : contro.searchBy = val;
                            contro.update([PeoplesControllerIDS.category]);
                            if (searchContro.text.trim().isNotEmpty) {
                              controller.getPeopleList(
                                page: 1,
                                name: searchContro.text.trim(),
                                fromFoucs: fromFoucs,
                                clear: !fromFoucs,
                              );
                            }
                          },
                        );
                      }),
                  Expanded(
                    child: InputText(
                      controller: searchContro,
                      fillColor: Colors.transparent,
                      fontColor: const Color(0xf0e5edff),
                      onChanged: (text) {
                        if (text.trim().isEmpty) {
                          controller.getPeopleList(
                            page: fromFoucs
                                ? controller.fromFoucsCurrentPage
                                : controller.currentPage,
                            fromFoucs: fromFoucs,
                            clear: !fromFoucs,
                          );
                        }
                      },
                      onEditingComplete: (text) {
                        if (text.isNotEmpty) {
                          controller.getPeopleList(
                            page: 1,
                            name: searchContro.text.trim(),
                            fromFoucs: fromFoucs,
                            clear: !fromFoucs,
                          );
                        } else {
                          showToast('请不要留空');
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 10),
            Row(
              children: [
                // DropdownWidget(
                //   hint: '关注人员',
                //   selectedValue: contro.sortFollow,
                //   items: contro.isFocus,
                //   onChanged: (val) {
                //     contro.sortFollow = val;
                //     contro.update([PeoplesControllerIDS.category]);
                //   },
                // ),
                // GetBuilder<PeoplesController>(
                //     id: PeoplesControllerIDS.category,
                //     builder: (contro) {
                //       return DropdownWidget(
                //         hint: '重点人员',
                //         selectedValue: contro.sortFoucs,
                //         items: contro.isFocus,
                //         onChanged: (val) async {
                //           contro.sortFoucs = val;
                //           contro.update([PeoplesControllerIDS.category]);
                //           await contro.getPeopleList(page: 1);
                //         },
                //       );
                //     }),
                fromFoucs
                    ? Container()
                    : InkWell(
                        onTap: () => jump2page(const FoucsPeoplesPage()),
                        child: Container(
                          decoration: const BoxDecoration(
                              image: DecorationImage(
                            image: AssetImage('assets/pic/234-2.png'),
                            fit: BoxFit.fill,
                          )),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 15, vertical: 11),
                          child: const Text(
                            '重点人员',
                            style: TextStyle(
                              color: Color(0xf0e5edff),
                              fontSize: 16.5,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      )
              ],
            ),
            // DropdownWidget(
            //   hint: '标签',
            //   selectedValue: null,
            //   items: contro.tags,
            //   onChanged: (val) => contro.addTag(val),
            // ),
          ],
        ),
      ),
    );
  }

  // Expanded _buildSortTag(PeoplesController contro) {
  //   return Expanded(
  //       child: ListView.separated(
  //     scrollDirection: Axis.horizontal,
  //     itemCount: contro.selectedTag.length,
  //     separatorBuilder: (context, index) => const SizedBox(width: 10),
  //     itemBuilder: (_, index) {
  //       return Container(
  //         margin: const EdgeInsets.symmetric(vertical: 5),
  //         decoration: const BoxDecoration(
  //             image: DecorationImage(
  //           image: AssetImage('assets/pic/234-2.png'),
  //           fit: BoxFit.fill,
  //         )),
  //         padding: const EdgeInsets.symmetric(horizontal: 10),
  //         alignment: Alignment.center,
  //         child: Row(
  //           children: [
  //             Text(
  //               contro.selectedTag[index],
  //               style: const TextStyle(color: Color(0xf0e5edff)),
  //             ),
  //             IconButton(
  //               icon: const Icon(Icons.close, color: Colors.blue),
  //               onPressed: () => contro.removeTag(index),
  //             )
  //           ],
  //         ),
  //       );
  //     },
  //   ));
  // }
}
