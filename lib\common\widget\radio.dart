import 'package:flutter/material.dart';

class RadioWidget extends StatelessWidget {
  final String label;
  final String valueGroup;
  final List<String> radios;
  final Function(String) onChanged;
  final Axis scrollDirection;
  final ScrollPhysics physics;
  final List<int>? disEnable;
  final EdgeInsets? margin;
  final String otherText;
  final Function(String, int)? onChangedOther;
  final bool Function(String)? canEdit;
  final double fontSize;
  final bool enable;

  const RadioWidget({
    super.key,
    this.label = '',
    required this.onChanged,
    required this.valueGroup,
    required this.radios,
    this.scrollDirection = Axis.horizontal,
    this.physics = const NeverScrollableScrollPhysics(),
    this.disEnable,
    this.margin,
    this.otherText = '',
    this.onChangedOther,
    this.canEdit,
    this.fontSize = 20,
    this.enable = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(4)),
      // padding: const EdgeInsets.all(7),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: label != ''
            ? MainAxisAlignment.spaceAround
            : MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          label != ''
              ? Text(
                  label,
                  style: TextStyle(fontSize: fontSize, color: Colors.white),
                )
              : Container(width: 0),
          const SizedBox(height: 10),
          Wrap(
            // mainAxisSize: MainAxisSize.min,
            children: List.generate(radios.length, (index) {
              String value = radios[index];
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Radio<String>(
                      value: value,
                      groupValue: valueGroup,
                      activeColor: Colors.blue[800],
                      onChanged: enable
                          ? (val) {
                              value = val!;
                              onChanged(val);
                            }
                          : null,
                    ),
                    radios[index] == '其他'
                        ? Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                radios[index],
                                style: const TextStyle(
                                    fontSize: 17, color: Colors.white),
                              ),
                              const SizedBox(width: 8),
                              SizedBox(
                                width: 200,
                                child: TextField(
                                  controller: TextEditingController.fromValue(
                                      TextEditingValue(text: otherText)),
                                  enabled: canEdit?.call(radios[index]),
                                  decoration: const InputDecoration(
                                    border: InputBorder.none,
                                    isCollapsed: true,
                                  ),
                                  style: const TextStyle(color: Colors.white),
                                  onChanged: (value) =>
                                      onChangedOther?.call(value.trim(), index),
                                ),
                              ),
                            ],
                          )
                        : Text(
                            radios[index],
                            style: const TextStyle(
                                fontSize: 17, color: Colors.white),
                          ),
                  ],
                ),
              );
            }),
          )
        ],
      ),
    );
  }

  // bool _isEnabled(int index, List<int>? disEnable) {
  //   bool enabled = true;
  //   if (disEnable != null) {
  //     for (var i in disEnable) {
  //       if (i == index) {
  //         enabled = false;
  //       } else {
  //         enabled = true;
  //       }
  //     }
  //   }
  //   return enabled;
  // }
}
