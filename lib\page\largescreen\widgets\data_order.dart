import 'dart:math';

import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/big_screen_model/alarm_sort.dart';
import 'package:police/page/largescreen/controller.dart';
import '../../../common/widget/corner_card.dart';

class DataOrder extends StatelessWidget {
  const DataOrder({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: CornerCard(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
          child: Column(
            children: [
              const Text("排序",
                  style: TextStyle(fontSize: 16, color: Color(0xe7ffffff))),
              Row(
                children: [
                  Expanded(
                    child: SizedBox(
                      height: 360, // Adjusted height for better visualization
                      child: GetBuilder<LargescreenController>(
                        id: LargescernnIDS.sort,
                        builder: (context) {
                          List<OrdinalCases> datas = [];
                          List<AlarmSort> sorts =
                              Get.find<LargescreenController>()
                                  .bigscreenData
                                  .alarmSort!;

                          // 对排序列表按 alarmCount 降序排序
                          sorts.sort(
                              (a, b) => b.alarmCount!.compareTo(a.alarmCount!));

                          // 定义一个颜色列表
                          final List<String> colors = [
                            '#FF0000', // 珊瑚色 (红色警示)
                            '#f77f00', // 橙色
                            '#e9c46a', // 黄色
                            '#f4a261', // 沙色
                            '#2a9d8f', // 深青色
                            '#6a4c93', // 紫色
                            '#264653', // 深蓝色
                          ];

                          for (var i = 0; i < sorts.length; i++) {
                            datas.add(OrdinalCases(
                                sorts[i].name ?? '',
                                sorts[i].alarmCount!,
                                colors[i % colors.length]));
                          }

                          return SizedBox(
                            height: 10,
                            child:
                                HorizontalBarChart.withSampleData(datas: datas),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HorizontalBarChart extends StatelessWidget {
  final List<charts.Series<OrdinalCases, String>> seriesList;
  final bool animate;

  const HorizontalBarChart(this.seriesList, {super.key, this.animate = true});

  factory HorizontalBarChart.withSampleData(
      {required List<OrdinalCases> datas}) {
    return HorizontalBarChart(_createSampleData(datas: datas));
  }

  @override
  Widget build(BuildContext context) {
    return charts.BarChart(
      seriesList,
      animate: animate,
      vertical: false,
      barRendererDecorator: charts.BarLabelDecorator<String>(
        insideLabelStyleSpec:
            charts.TextStyleSpec(color: charts.Color.fromHex(code: '#ffffff')),
        // Labels inside the bar
        outsideLabelStyleSpec:
            charts.TextStyleSpec(color: charts.Color.fromHex(code: '#ffffff')),
      ),
      domainAxis: charts.OrdinalAxisSpec(
        renderSpec: charts.SmallTickRendererSpec(
          labelStyle: charts.TextStyleSpec(
              color: charts.Color.fromHex(code: '#4994ec')), // Axis labels
          lineStyle: charts.LineStyleSpec(
              color: charts.Color.fromHex(code: '#4994ec')), // Axis lines
        ),
      ),
      primaryMeasureAxis: charts.NumericAxisSpec(
        renderSpec: charts.GridlineRendererSpec(
          labelStyle: charts.TextStyleSpec(
              color: charts.Color.fromHex(code: '#4994ec')), // Axis labels
          lineStyle: charts.LineStyleSpec(
              color: charts.Color.fromHex(code: '#4994ec')), // Gridline
        ),
      ),
    );
  }

  static List<charts.Series<OrdinalCases, String>> _createSampleData(
      {required List<OrdinalCases> datas}) {
    return [
      charts.Series<OrdinalCases, String>(
        id: '案件统计',
        domainFn: (OrdinalCases cases, _) => cases.caseType,
        measureFn: (OrdinalCases cases, _) => cases.count,
        data: datas,
        labelAccessorFn: (OrdinalCases cases, _) => '${cases.count}',
        colorFn: (cases, __) =>
            charts.Color.fromHex(code: cases.color), // Bars color
      ),
    ];
  }
}

class OrdinalCases {
  final String caseType;
  final int count;
  final String color;

  OrdinalCases(this.caseType, this.count, this.color);
}
