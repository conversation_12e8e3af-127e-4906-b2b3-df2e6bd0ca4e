import 'package:get/get.dart';
import 'package:police/common/api/house_api.dart';
import 'package:police/common/model/house_model.dart';

class HouseControllerIDS {
  static String houses = 'houses';
  static String housesItem = 'housesItem';
  static String turnpage = 'turnpage';
}

class HouseController extends GetxController {
  int code = 0;
  int currentPage = 1;
  int maxpages = 1;

  List<String> tableHeaders = ['屋主', '联系电话', '房屋用途', '实住人口', '地址'];

  /// 房屋列表
  List<HouseModel> houses = [];

  /// 获取房屋列表
  getHouses(int unitId, {int page = 1}) async {
    code = 0;
    update([HouseControllerIDS.houses]);

    houses = await HouseApi.getHouses(
      unitId,
      page: page,
      fail: (p0) {
        code = 404;
        update([HouseControllerIDS.houses]);
      },
    );

    code = 200;
    update([HouseControllerIDS.houses]);
  }

  /// 翻页
  Future<void> turnPage(int index, int total, {required int unitId}) async {
    if (index == 0 && currentPage > 1) {
      currentPage -= 1;
    } else if (index == total) {
      currentPage = maxpages;
    } else if (index > 0 && index < total) {
      currentPage = index;
    }
    await getHouses(unitId, page: currentPage);
  }
}
