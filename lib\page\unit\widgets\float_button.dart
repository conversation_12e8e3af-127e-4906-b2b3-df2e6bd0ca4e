import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:police/page/unit/index.dart';

import '../../../common/widget/float_square_button.dart';

class FloatButton extends StatelessWidget {
  const FloatButton(
      {super.key,
      required this.controller,
      required this.buildId,
      required this.buildName});

  final UnitController controller;
  final String buildName;
  final int buildId;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.all(36),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FloatSquareButton(
              onTap: () =>
                  controller.deleteBuild(context, id: buildId, name: buildName),
              icon: Icons.delete,
              color: Colors.red,
            ),
            const SizedBox(height: 10),
            FloatSquareButton(
                onTap: () => controller.getUnits(buildId), icon: Icons.refresh),
            const SizedBox(height: 10),
            FloatSquareButton(
              onTap: () => controller.save(context),
              icon: FontAwesomeIcons.floppyDisk,
            ),
            const SizedBox(height: 10),
            FloatSquareButton(
                onTap: () => controller.addUnit(buildId), icon: Icons.add),
          ],
        ),
      ),
    );
  }
}
