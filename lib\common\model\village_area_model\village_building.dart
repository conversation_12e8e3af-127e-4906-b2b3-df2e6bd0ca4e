class VillageBuilding {
  int? villageAreaId;
  String? name;
  int? id;
  DateTime? createTime;

  VillageBuilding({
    this.villageAreaId,
    this.name,
    this.id,
    this.createTime,
  });

  factory VillageBuilding.fromJson(Map<String, dynamic> json) {
    return VillageBuilding(
      villageAreaId: json['village_area_id'] as int?,
      name: json['name'] as String?,
      id: json['id'] as int?,
      createTime: json['create_time'] == null
          ? null
          : DateTime.parse(json['create_time'] as String),
    );
  }

  Map<String, dynamic> toJson() => {
        'village_area_id': villageAreaId,
        'name': name,
        'id': id,
        'create_time': createTime?.toIso8601String(),
      };
}
