import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/model/user_info_model.dart';
import 'package:police/common/widget/form_text.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/common/widget/image_upload.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/page/home/<USER>';
import 'package:police/page/user/controller.dart';

import '../../../common/widget/form_input.dart';

class UserInfo extends StatelessWidget {
  const UserInfo({super.key});

  @override
  Widget build(BuildContext context) {
    UserController controller = Get.find<UserController>();
    UserInfoModel userInfo = controller.userInfo;

    return Column(
      children: [
        SizedBox(
          height: 130 * 4 / 3,
          child: Row(
            children: [
              // ImageWidget.network(
              //   '${Apis.baseUrl}/assets/${userInfo.avatar}',
              //   width: 130,
              //   height: 130 * 4 / 3,
              //   borderRadius: BorderRadius.circular(10),
              // ),
              ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: ImageUploadWidget(
                  photoUrl: userInfo.avatar ?? '',
                  width: 130,
                  height: 130 * 4 / 3,
                  onUploaded: (url) async {
                    userInfo.avatar = url;
                    await controller
                        .updateAvatar(context, {"avatar": userInfo.avatar});
                  },
                ),
              ),
              const SizedBox(width: 60),
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 200,
                    child: FormInput(
                      label: '姓名',
                      defaultText: userInfo.name ?? '',
                      margin: EdgeInsets.zero,
                      color: const Color(0xd8cfe0ff),
                      onChanged: (text) => userInfo.name = text,
                    ),
                  ),
                  SizedBox(
                    width: 200,
                    child: FormInput(
                      label: '警号',
                      margin: EdgeInsets.zero,
                      defaultText: userInfo.policeNumber ?? '',
                      color: const Color(0xd8cfe0ff),
                      onChanged: (text) => userInfo.policeNumber = text,
                    ),
                  ),
                  SizedBox(
                    width: 200,
                    child: FormInput(
                      label: '职位',
                      margin: EdgeInsets.zero,
                      defaultText: userInfo.commandPost ?? '',
                      color: const Color(0xd8cfe0ff),
                      onChanged: (text) => userInfo.commandPost = text,
                    ),
                  ),
                  SizedBox(
                    width: 200,
                    child: FormInput(
                      label: '电话',
                      margin: EdgeInsets.zero,
                      defaultText: userInfo.telephone ?? '',
                      color: const Color(0xd8cfe0ff),
                      onChanged: (text) => userInfo.telephone = text,
                    ),
                  ),
                  SizedBox(
                    width: 200,
                    child: FormInput(
                      label: '用户级别',
                      margin: EdgeInsets.zero,
                      defaultText:
                          controller.permissions[userInfo.permissions] ?? '',
                      color: const Color(0xd8cfe0ff),
                      enabled: false,
                    ),
                  ),

                  // FormText(
                  //   labelText: '帐号状态',
                  //   valueText: userInfo.isActive ? '正常' : '冻结',
                  //   style: TextStyle(
                  //     fontSize: 17,
                  //     color: userInfo.isActive
                  //         ? const Color(0xd8cfe0ff)
                  //         : Colors.red,
                  //   ),
                  // ),
                ],
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(0, 20, 30, 0),
          child: NormolButton(
            lable: '更改用户信息',
            onTap: () async {
              await controller.updateAvatar(context, {
                "police_number": userInfo.policeNumber,
                "name": userInfo.name,
                "telephone": userInfo.telephone,
                "command_post": userInfo.commandPost,
              });

              int index = Get.find<HomeController>()
                  .sir
                  .indexWhere((item) => item.id == userInfo.id);

              if (index != -1) {
                Get.find<HomeController>().sir[index] = userInfo;
                Get.find<HomeController>().update(['sir']);
              }
            },
          ),
        ),
      ],
    );
  }
}
