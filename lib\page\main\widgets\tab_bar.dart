import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/material.dart';
import 'package:police/common/widget/drag_window.dart';
import 'package:police/common/widget/window_manager.dart';
import 'package:police/page/main/controller.dart';
import 'package:police/page/main/widgets/user_info.dart';

class TabBarWidget extends StatelessWidget {
  const TabBarWidget({super.key, required this.controller});

  final MainController controller;

  @override
  Widget build(BuildContext context) {
    // 预加载图片
    precacheImage(const AssetImage('assets/pic/tabbar_indicator.png'), context);

    return DragWindowWidget(
      child: Container(
        color: Colors.transparent,
        padding: const EdgeInsets.fromLTRB(40, 10, 0, 10),
        height: 60,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: SvgPicture.asset(
                'assets/svg/police_logo.svg',
                width: 30,
                height: 30,
              ),
            ),
            Center(
              child: TabBar(
                controller: controller.tabController,
                isScrollable: true,
                tabAlignment: TabAlignment.center,
                dividerColor: Colors.transparent,
                indicator: const BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage('assets/pic/tabbar_indicator.png')),
                ),
                splashFactory: NoSplash.splashFactory, // 隐藏点击水波纹
                // overlayColor: WidgetStateColor.resolveWith(
                //     (states) => Colors.transparent), // 点击高亮颜色
                overlayColor: MaterialStateColor.resolveWith(
                    (states) => Colors.transparent),
                indicatorSize: TabBarIndicatorSize.tab,
                labelPadding:
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                labelStyle: const TextStyle(
                    fontWeight: FontWeight.normal, fontSize: 20),
                unselectedLabelStyle: const TextStyle(
                    fontWeight: FontWeight.normal, fontSize: 20),
                labelColor: const Color(0xd8cfe0ff), // 设置选中的文本颜色
                unselectedLabelColor: const Color(0xd8cfe0ff), // 设置未选中的文本颜色
                tabs: controller.tabs.map((e) => Text(e)).toList(),
              ),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  UserInfo(controller: controller),
                  const VerticalDivider(indent: 7, endIndent: 7),
                  const WindowManager(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
