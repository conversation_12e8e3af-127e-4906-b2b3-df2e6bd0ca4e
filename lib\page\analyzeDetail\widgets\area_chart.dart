import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/analyze_model.dart';
import 'package:police/page/analyzeDetail/controller.dart';
import 'dart:math';

import '../../../common/model/analyze_details_model/village_list.dart';
import '../../../common/widget/animation_bar_chart.dart';
import '../../../common/widget/corner_card.dart';
import '../../analyze/controller.dart';

class AreaChart extends StatelessWidget {
  const AreaChart({super.key, required this.details});

  final AnalyzeModel details;

  @override
  Widget build(BuildContext context) {
    AnalyzedetailController controller = Get.find<AnalyzedetailController>();
    List<SortChartModel> datas = [];
    final random = Random();

    for (VillageList area in controller.details.villageList ?? []) {
      // 生成随机颜色
      final color = Color.fromRGBO(
        64 + random.nextInt(128),
        64 + random.nextInt(128),
        64 + random.nextInt(128),
        1,
      ).value;
      datas.add(SortChartModel(area.name ?? '', area.alarmCount ?? 0, color));
    }
    return CornerCard(
      child: Column(
        children: [
          Text(
            '${controller.getYearMonth(details)}警情发生区分布图',
            style: const TextStyle(fontSize: 25, color: Colors.white),
          ),
          AnimatedBarChart(
            datas: datas,
            hideZeroValues: true,
            labelStyle: const TextStyle(color: Colors.white),
            maxBarSpacing: 5,
          ),
        ],
      ),
    );
  }
}
