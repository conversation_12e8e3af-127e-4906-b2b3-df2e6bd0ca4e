import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:police/common/widget/image.dart';

import '../api/apis.dart';
import '../api/uplaod_image_api.dart';
import '../util/image_picker.dart';
import 'lottie_animation.dart';

class ImageUploadWidget extends StatefulWidget {
  const ImageUploadWidget({
    super.key,
    required this.photoUrl,
    required this.onUploaded,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.alignment = Alignment.center,
  });

  final String photoUrl;
  final Function(String url) onUploaded;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Alignment alignment;

  @override
  State<ImageUploadWidget> createState() => _DDDDState();
}

class _DDDDState extends State<ImageUploadWidget> {
  String photoPath = '';
  String photoUrl = '';
  double uploadProgress = 0.0;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: InkWell(
        onTap: () async {
          XFile? image = await imagePicker();
          if (image != null) {
            setState(() {
              photoUrl = '';
              uploadProgress = 0.0;
              photoPath = image.path;
            });
            final result = await uploadImage(
              photoPath,
              onSendProgress: (now, total) {
                setState(() {
                  uploadProgress = now / total;
                });
              },
            );
            setState(() {
              photoUrl = result['files'][0];
              photoPath == '';
              widget.onUploaded(photoUrl);
            });
          }
        },
        child: widget.photoUrl != '' && photoPath == ''
            ? ImageWidget.network(
                widget.photoUrl,
                fit: widget.fit,
                alignment: widget.alignment,
              )
            : photoUrl != ''
                ? ImageWidget.network(photoUrl, fit: widget.fit)
                : photoPath != ''
                    ? Stack(
                        fit: StackFit.expand,
                        children: [
                          ImageWidget.file(
                            photoPath,
                            width: double.infinity,
                            height: double.infinity,
                            fit: widget.fit,
                            alignment: widget.alignment,
                          ),
                          Visibility(
                            visible: uploadProgress < 1.0,
                            child: Container(
                              color: Colors.black.withOpacity(.7),
                              child: LottieAnimation(
                                'hori_progress.json',
                                progress: uploadProgress,
                              ),
                            ),
                          )
                        ],
                      )
                    : Container(
                        color: Colors.grey.withOpacity(.3),
                        child: const Icon(Icons.photo),
                      ),
      ),
    );
  }
}
