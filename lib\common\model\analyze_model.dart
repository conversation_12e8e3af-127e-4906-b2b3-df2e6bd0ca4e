class AnalyzeModel {
  int? id;
  String? types;
  String? yearMonthDay;
  String? graphics;

  AnalyzeModel({
    this.id,
    this.types,
    this.yearMonthDay,
    this.graphics,
  });

  factory AnalyzeModel.fromJson(Map<String, dynamic> json) => AnalyzeModel(
        id: json['id'] as int?,
        types: json['types'] as String?,
        yearMonthDay: json['year_month_day'] as String?,
        graphics: json['graphics'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'types': types,
        'year_month_day': yearMonthDay,
        'graphics': graphics,
      };
}
