class UserInfoModel {
  int? id;
  String? username;
  String? avatar;
  bool isActive;
  String permissions;
  DateTime? createTime;
  DateTime? loginTime;
  String? policeNumber;
  String? name;
  String? telephone;
  String? commandPost;
  bool isCommand;

  UserInfoModel({
    this.id,
    this.username,
    this.avatar,
    this.isActive = true,
    this.permissions = 'user',
    this.createTime,
    this.loginTime,
    this.policeNumber,
    this.name,
    this.telephone,
    this.commandPost,
    this.isCommand = false,
  });

  factory UserInfoModel.fromJson(Map<String, dynamic> json) => UserInfoModel(
        id: json['id'] as int?,
        username: json['username'] as String?,
        avatar: json['avatar'] as String?,
        isActive: json['is_active'] as bool,
        permissions: json['permissions'] as String,
        createTime: json['create_time'] == null
            ? null
            : DateTime.parse(json['create_time'] as String),
        loginTime: json['login_time'] == null
            ? null
            : DateTime.parse(json['login_time'] as String),
        policeNumber: json['police_number'] as String?,
        name: json['name'] as String?,
        telephone: json['telephone'] as String?,
        commandPost: json['command_post'] as String?,
        isCommand: json['is_command'] as bool,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'username': username,
        'avatar': avatar,
        'is_active': isActive,
        'permissions': permissions,
        'create_time': createTime?.toIso8601String(),
        'login_time': loginTime?.toIso8601String(),
        'police_number': policeNumber,
        'name': name,
        'telephone': telephone,
        'command_post': commandPost,
        'is_command': isCommand,
      };

  UserInfoModel copyWith({
    int? id,
    String? username,
    String? avatar,
    bool? isActive,
    String? permissions,
    DateTime? createTime,
    DateTime? loginTime,
    String? policeNumber,
    String? name,
    String? telephone,
    String? commandPost,
    bool? isCommand,
  }) {
    return UserInfoModel(
      id: id ?? this.id,
      username: username ?? this.username,
      avatar: avatar ?? this.avatar,
      isActive: isActive ?? this.isActive,
      permissions: permissions ?? this.permissions,
      createTime: createTime ?? this.createTime,
      loginTime: loginTime ?? this.loginTime,
      policeNumber: policeNumber ?? this.policeNumber,
      name: name ?? this.name,
      telephone: telephone ?? this.telephone,
      commandPost: commandPost ?? this.commandPost,
      isCommand: isCommand ?? this.isCommand,
    );
  }
}
