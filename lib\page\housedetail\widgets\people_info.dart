import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/model/people_detail_model.dart';
import 'package:police/common/model/people_rela_model/people_rela_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/corner_card.dart';
import 'package:police/common/widget/empty_widget.dart';
import 'package:police/common/widget/float_square_button.dart';
import 'package:police/common/widget/form_input.dart';
import 'package:police/common/widget/form_text.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/common/widget/radio.dart';
import 'package:police/page/housedetail/controller.dart';
import 'package:police/page/peopledetail/index.dart';

class PeopleInfo extends StatelessWidget {
  const PeopleInfo({super.key, this.houseId});
  final int? houseId;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HousedetailController>(
      id: HousedetailIDS.peoples,
      builder: (controller) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '人员基本信息',
              style: TextStyle(fontSize: 40, color: Color(0xffffffff)),
            ),
            const SizedBox(height: 10),
            _buildContent(controller, context, houseId),
          ],
        );
      },
    );
  }

  /// 构建内容区域，根据状态显示加载、错误、空数据或列表
  Widget _buildContent(
      HousedetailController controller, BuildContext context, int? houseId) {
    late Widget content;
    if (controller.code == 0) {
      content = const LoadingWidget();
    } else if (controller.code == 404) {
      content = const HttpErrorWidget();
    } else if (controller.code == 200 && controller.peoples.isEmpty) {
      content = const EmptyWidget();
    } else {
      content = _buildPeopleList(controller, context);
    }

    return Column(
      children: [
        SizedBox(
          height: controller.peoples.isEmpty ? 200 : null,
          child: content,
        ),
        const SizedBox(height: 20),
        _buildActionButtons(controller, context, houseId),
      ],
    );
  }

  /// 构建人员信息列表
  Widget _buildPeopleList(
      HousedetailController controller, BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      separatorBuilder: (_, __) => const SizedBox(height: 20),
      itemCount: controller.peoples.length,
      itemBuilder: (context, index) {
        PeopleRelaModel people = controller.peoples[index];
        PeopleDetailModel person = people.person ?? PeopleDetailModel();
        return _buildPeopleCard(people, person, controller, context, index);
      },
    );
  }

  /// 构建单个人员信息卡片
  Widget _buildPeopleCard(PeopleRelaModel people, PeopleDetailModel person,
      HousedetailController controller, BuildContext context, int index) {
    return InkWell(
      onTap: () => jump2page(PeopledetailPage(person, fromHouse: true)),
      child: CornerCard(
        backgroundColor: const Color(0xca073773),
        padding: const EdgeInsets.all(20),
        child: Stack(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPersonImage(person),
                const SizedBox(width: 10),
                Expanded(child: _buildPersonDetails(people, person)),
              ],
            ),
            _buildDeleteButton(controller, context, index, people, person),
          ],
        ),
      ),
    );
  }

  /// 构建人员图片展示
  Widget _buildPersonImage(PeopleDetailModel person) {
    return ImageWidget.network(
      person.photo ?? '',
      width: 130,
      height: 130 * 4 / 3,
      borderRadius: BorderRadius.circular(10),
    );
  }

  /// 构建人员详细信息表单
  Widget _buildPersonDetails(PeopleRelaModel people, PeopleDetailModel person) {
    return Wrap(
      spacing: 40,
      runSpacing: 40,
      children: [
        FormInput(
          maxLength: 10,
          label: '成员关系',
          width: 200,
          fontSize: 20,
          defaultText: people.relationName ?? '',
          margin: EdgeInsets.zero,
          onChanged: (text) => people.relationName = text,
        ),
        FormText(
          labelText: '姓名',
          fontSize: 20,
          valueText: person.name ?? '',
        ),
        FormText(
          labelText: '身份证号',
          fontSize: 20,
          valueText: person.idNumber ?? '',
        ),
        FormText(
          labelText: '联系电话',
          fontSize: 20,
          valueText: person.telephone ?? '',
        ),
        _buildRadioGroup(
          '人户分离',
          ['是', '否'],
          person.residenceSeparation ? '是' : '否',
        ),
        FormText(
          labelText: '户籍地址',
          fontSize: 20,
          valueText: person.residenceAddress ?? '',
        ),
        _buildRadioGroup(
          '政治面貌',
          ['党员', '团员', '其他党派', '群众'],
          person.politicalStatus ?? '群众',
        ),
        _buildRadioGroup(
          '婚姻状况',
          ['未婚', '已婚', '离异', '丧偶'],
          person.maritalStatus ?? '未婚',
        ),
        FormText(
          labelText: '文化程度',
          fontSize: 20,
          valueText: person.education ?? '',
        ),
        _buildRadioGroup(
          '宗教信仰',
          ['佛教', '伊斯兰', '基督', '无', '其他'],
          person.belief ?? '其他',
        ),
        _buildRadioGroup(
          '兵役状况',
          ['未服', '退出', '预备', '未知'],
          person.veteranStatus ?? '未知',
        ),
        _buildRadioGroup(
          '职业',
          ['国家机关', '企事业', '民营个体', '自由', '离退休', '学生', '农民', '无业'],
          person.profession ?? '无业',
        ),
        FormText(
          labelText: '关注人员类别',
          fontSize: 20,
          valueText: person.personInterest ?? '',
        ),
      ],
    );
  }

  /// 构建带有提示的单选按钮组
  Widget _buildRadioGroup(
    String label,
    List<String> options,
    String selectedOption,
  ) {
    return RadioWidget(
      label: label,
      radios: options,
      onChanged: (val) => showToast('请前往人员详情修改'),
      valueGroup: selectedOption,
      canEdit: (val) => false,
    );
  }

  /// 构建删除按钮
  Widget _buildDeleteButton(
      HousedetailController controller,
      BuildContext context,
      int index,
      PeopleRelaModel people,
      PeopleDetailModel person) {
    return Align(
      alignment: Alignment.topRight,
      child: FloatSquareButton(
        onTap: () => controller.deletePeople(
          context,
          index: index,
          peopleId: people.id,
          name: person.name,
        ),
        icon: Icons.delete,
        color: Colors.blue.withOpacity(.3),
      ),
    );
  }

  /// 构建新增和保存按钮
  Widget _buildActionButtons(
      HousedetailController controller, BuildContext context, int? houseId) {
    return Column(
      children: [
        NormolButton(
          lable: '新增人员关系',
          onTap: () {
            if (controller.house.id != null) {
              controller.addPeople(context, houseId);
            } else {
              showToast('请先保存房屋');
            }
          },
        ),
        // const SizedBox(height: 10),
        // NormolButton(
        //   lable: '保存人员关系',
        //   onTap: () {
        //     if (controller.house.id != null) {
        //       controller.savePeople(context, controller.house.id!);
        //     } else {
        //       showToast('请先保存房屋');
        //     }
        //   },
        // ),
      ],
    );
  }
}
