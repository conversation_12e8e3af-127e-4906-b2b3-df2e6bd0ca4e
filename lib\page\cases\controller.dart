import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart' hide Response;
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/case_api.dart';
import 'package:police/common/api/village_api.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/model/case_model.dart';
import 'package:police/common/util/https.dart';

import '../../common/api/apis.dart';
import '../../common/model/dropdown_menu_item_model.dart';
import '../../common/model/village_model/village_model.dart';

class CaseControllerIDS {
  static String cases = 'cases';
  static String caseItem = 'case_item';
  static String header = 'header';
  static String turnpage = 'turnpage';
  static String animation = 'animation';
  static String category = 'category';
}

class CaseType {
  static String criminal = '刑事案件';
  static String security = '治安案件';
  static String dispute = '纠纷';
  static String help = '求助';
  static String invalid = '无效警情';
  static String other = '其他警情';
}

class CasesController extends GetxController {
  int maxpages = 1;
  int currentPage = 1;
  int code = 0;
  int sortForm = -1;
  int newCaseCount = 0;
  Offset offset = Offset.zero;
  List<VillageModel> villages = [];
  Map<int, String> villageNameMap = {};

  /// 年
  List<DropdownMenuItemModel> year = List.generate(
    11,
    (index) => DropdownMenuItemModel(
      text: index == 0 ? '年' : (DateTime.now().year + 1 - index).toString(),
      value: index == 0 ? '' : (DateTime.now().year + 1 - index).toString(),
    ),
  );

  /// 月
  List<DropdownMenuItemModel> month = List.generate(
    13,
    (index) => DropdownMenuItemModel(
      text: index == 0 ? '月' : index.toString(),
      value: index == 0 ? '' : index.toString(),
    ),
  );

  /// 日
  List<DropdownMenuItemModel> day = List.generate(
    32,
    (index) => DropdownMenuItemModel(
      text: index == 0 ? '日' : index.toString(),
      value: index == 0 ? '' : index.toString(),
    ),
  );

  /// 警情类型
  List<DropdownMenuItemModel> type = [
    DropdownMenuItemModel(
      text: '警情类型',
      value: '',
    ),
    DropdownMenuItemModel(
      text: '刑事案件',
      value: '刑事案件',
    ),
    DropdownMenuItemModel(
      text: '治安案件',
      value: '治安案件',
    ),
    DropdownMenuItemModel(
      text: '纠纷',
      value: '纠纷',
    ),
    DropdownMenuItemModel(
      text: '求助',
      value: '求助',
    ),
    DropdownMenuItemModel(
      text: '无效警情',
      value: '无效警情',
    ),
    DropdownMenuItemModel(
      text: '其他警情',
      value: '其他警情',
    ),
  ];

  String selectedYear = '';
  String selectedMonth = '';
  String selectedDay = '';
  String selectedType = '';

  /// 选中的案件
  int? selectedCase;

  ScrollController scrollController = ScrollController();

  Map<String, List<String>> caseType = {}; // 使用具体类型

  List<String> tabHeader = [
    '警情内容',
    '警情类型',
    '警情分类',
    '警情地点',
    '警情时间',
    '警情处置',
    '反馈/建议'
  ];

  /// 原始的案件列表用于对比修改
  List<CaseModel> oldCases = [];

  /// 案件列表
  List<CaseModel> cases = [];

  @override
  void onInit() {
    super.onInit();
    getCases();
    getCaseType();
  }

  /// 获取案件类型
  Future<void> getCaseType() async {
    // 读取 JSON 文件内容
    String jsonString = await rootBundle.loadString('assets/case_type_1.json');

    // 进行初步解码
    Map<String, dynamic> decodedJson = json.decode(jsonString);

    // 手动转换为 Map<String, List<String>>
    caseType = decodedJson.map<String, List<String>>(
      (key, value) {
        // 将动态类型的 List 转换为 List<String>
        List<String> stringList = List<String>.from(value);
        return MapEntry(key, stringList);
      },
    );
  }

  /// 获取案件列表
  Future<void> getCases({int page = 1}) async {
    code = 0;
    update([CaseControllerIDS.cases]);

    cases = await CaseApi.getCaseList(
      page: page,
      year: selectedYear,
      month: selectedMonth,
      day: selectedDay,
      caseType: selectedType,
      fail: (error) {
        code = 404;
        update([CaseControllerIDS.cases]);
      },
    );

    // 创建 new List from cases
    oldCases = cases.map((caseItem) => caseItem.copy()).toList();
    code = 200;
    newCaseCount = 0;
    update([CaseControllerIDS.cases]);
  }

  /// 翻页
  Future<void> turnPage(int index, int total) async {
    if (index == 0 && currentPage > 1) {
      currentPage -= 1;
    } else if (index == total) {
      currentPage = maxpages;
    } else if (index > 0 && index < total) {
      currentPage = index;
    }
    await getCases(page: currentPage);
  }

  /// 保存案件
  Future<void> save(BuildContext context) async {
    // 创建一个集合以记录需要保存的变化
    Set<CaseModel> changedCases = {};

    // 遍历当前的案件列表
    for (var caseItem in cases) {
      bool isModified = true;

      // 查找是否存在匹配的旧案件
      for (var old in oldCases) {
        if (old.title == caseItem.title &&
            old.types == caseItem.types &&
            old.sort == caseItem.sort &&
            old.graphics == caseItem.graphics &&
            old.occurrenceTime == caseItem.occurrenceTime &&
            old.villageId == caseItem.villageId &&
            old.finalFeedback == caseItem.finalFeedback &&
            old.opinions == caseItem.opinions &&
            old.policeFeedBack == caseItem.policeFeedBack) {
          isModified = false; // 如果 ID 匹配，说明案件没有被修改
          break;
        }
      }

      if (isModified) {
        changedCases.add(caseItem); // 将修改过的案件添加到集合中
      }
    }

    // 如果没有发现更改，则无需保存
    if (changedCases.isEmpty) {
      showToast('没有发现更改的案件。');
      return;
    }

    try {
      // 保存所有修改过的案件
      for (var caseItem in changedCases) {
        if (caseItem.villageId == null) {
          showToast('请选择警情地点');
          return;
        }

        if (caseItem.occurrenceTime == null) {
          showToast('警情时间格式不正确');
          return;
        }

        if (caseItem.id == null) {
          Dialogs.showLoadingDialog(context);
          final result = await CaseApi.createCase(caseItem.toJson());
          cases.remove(caseItem); // 移除旧的案件
          cases.insert(0, result); // 添加新的案件
        } else {
          Dialogs.showLoadingDialog(context);
          await CaseApi.patchCaseDetail(caseItem.id!, caseItem.toJson(),
              pageBack: true);
        }
      }

      oldCases = cases.map((caseItem) => caseItem.copy()).toList();
      // 更新状态
      update([CaseControllerIDS.caseItem]);
      showToast('保存成功');
      // await getCases();
    } catch (e) {
      // showToast('保存失败');
    }
  }

  /// 保持案件反馈
  Future<void> saveFeedback(BuildContext context,
      {required int id, required Map<String, dynamic> data}) async {
    Dialogs.showLoadingDialog(context);
    try {
      await CaseApi.patchCaseDetail(id, data, pageBack: true);
      showToast('保存成功');
    } catch (e) {}
  }

  /// 新增案件
  void addCase() {
    // 检查是否存在尚未保存的新案件
    bool hasUnsavedNewCase = cases.any((caseItem) => caseItem.id == null);

    if (hasUnsavedNewCase) {
      newCaseCount += 1;
      if (newCaseCount >= 1) {
        showToast('请逐条添加');
        return;
      }
    }

    cases.insert(0, CaseModel());
    update([CaseControllerIDS.caseItem, CaseControllerIDS.cases]);
  }

  /// 删除案件
  deleteCase(BuildContext context, int index, {int? id, String? name}) async {
    if (id != null) {
      Dialogs.showConfirmDialog(
        context,
        title: '是否确认删除',
        subTitle: ' $name',
        content: '删除后将无法恢复！',
        onConfirm: () async {
          Dialogs.showLoadingDialog(context);
          await CaseApi.deleteCase(id, pageBack: true);
          showToast('删除成功');
          cases.removeAt(index);
          update([CaseControllerIDS.caseItem, CaseControllerIDS.cases]);
        },
      );
    } else {
      cases.removeAt(index);
      update([CaseControllerIDS.caseItem, CaseControllerIDS.cases]);
    }
  }

  /// 获取所有村庄
  getAllVillage() async {
    villages.clear();
    code = 0;
    update([CaseControllerIDS.cases]);

    Response response = await Https.get(
      Apis.village,
      queryParameters: {'size': 100},
      fail: (results) {
        code = 404;
        update([CaseControllerIDS.cases]);
        return;
      },
    );

    for (var item in response.data['items']) {
      villages.add(VillageModel.fromJson(item));
      villageNameMap[item['id']] = item['name'];
    }

    getCases(page: currentPage);
  }
}
