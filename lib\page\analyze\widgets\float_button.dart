import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/analyze_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/page/analyze/index.dart';
import 'package:police/page/analyzeDetail/index.dart';

import '../../../common/widget/float_square_button.dart';

class AnalyzeFloatButton extends StatelessWidget {
  const AnalyzeFloatButton({super.key});

  @override
  Widget build(BuildContext context) {
    AnalyzeController controller = Get.find();

    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.all(36),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FloatSquareButton(
              onTap: () {
                controller.currentPage = 1;
                controller.getAnalyze();
              },
              icon: Icons.refresh,
            ),
            const SizedBox(height: 10),
            // FloatSquareButton(
            //   onTap: () => controller.save(context),
            //   icon: FontAwesomeIcons.floppyDisk,
            // ),
            // const SizedBox(height: 10),
            FloatSquareButton(
              onTap: () => jump2page(
                AnalyzedetailPage(AnalyzeModel(types: controller.currentType)),
                onBack: (result) {
                  if (result != null) {
                    if (result is AnalyzeModel) {
                      if (result.types == controller.currentType) {
                        controller.analyzeList.add(result);
                        controller.update(['data']);
                      }
                    }
                  }
                },
              ),
              icon: Icons.add,
            ),
          ],
        ),
      ),
    );
  }
}
