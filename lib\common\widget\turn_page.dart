import 'package:flutter/material.dart';

class TurnPage extends StatelessWidget {
  const TurnPage(
      {super.key,
      required this.itemCount,
      required this.currentIndex,
      required this.onTap});

  final int itemCount;
  final int currentIndex;
  final Function(int, int) onTap;

  List<int> _getPageNumbers() {
    List<int> pageNumbers = [];
    if (itemCount <= 5) {
      for (int i = 1; i <= itemCount; i++) {
        pageNumbers.add(i);
      }
    } else {
      pageNumbers.add(1); // Always show the first page number
      if (currentIndex <= 3) {
        for (int i = 2; i <= 5; i++) {
          pageNumbers.add(i);
        }
      } else if (currentIndex >= itemCount - 2) {
        for (int i = itemCount - 4; i <= itemCount; i++) {
          pageNumbers.add(i);
        }
      } else {
        for (int i = currentIndex - 1; i <= currentIndex + 1; i++) {
          pageNumbers.add(i);
        }
        pageNumbers
            .add(currentIndex + 2); // Add two more pages after the current
        if (currentIndex + 2 < itemCount) {
          pageNumbers
              .add(currentIndex + 3); // One more page after that, if possible
        }
      }
      if (!pageNumbers.contains(itemCount)) {
        pageNumbers.add(itemCount); // Always show the last page number
      }
    }
    return pageNumbers;
  }

  @override
  Widget build(BuildContext context) {
    List<int> pageNumbers = _getPageNumbers();
    return SizedBox(
      height: 50,
      child: ListView.separated(
        itemCount: pageNumbers.length + 2,
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        separatorBuilder: (context, index) => const SizedBox(width: 30),
        itemBuilder: (_, index) {
          if (index == 0) {
            return InkWell(
              onTap: currentIndex == 1
                  ? null
                  : () => onTap(currentIndex - 1, itemCount),
              child: Container(
                width: 50,
                height: 50,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  color: const Color(0xeccfe0ff),
                ),
                child: const Icon(Icons.navigate_before_rounded),
              ),
            );
          } else if (index == pageNumbers.length + 1) {
            return InkWell(
              onTap: currentIndex == itemCount
                  ? null
                  : () => onTap(currentIndex + 1, itemCount),
              child: Container(
                width: 50,
                height: 50,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  color: const Color(0xeccfe0ff),
                ),
                child: const Icon(Icons.navigate_next_rounded),
              ),
            );
          } else {
            int pageNumber = pageNumbers[index - 1];
            return InkWell(
              onTap: currentIndex == pageNumber
                  ? null
                  : () => onTap(pageNumber, itemCount),
              child: Container(
                width: 50,
                height: 50,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  color: currentIndex == pageNumber
                      ? const Color(0xEA0C64BF)
                      : const Color(0xeccfe0ff),
                ),
                child: Text(
                  pageNumber.toString(),
                  style: TextStyle(
                    color: currentIndex == pageNumber
                        ? const Color(0xeccfe0ff)
                        : Colors.black,
                  ),
                ),
              ),
            );
          }
        },
      ),
    );
  }
}
