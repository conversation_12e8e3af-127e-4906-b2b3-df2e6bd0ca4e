import 'package:dio/dio.dart';

class HttpResponseModel {
  int code;
  Response<Map<String, dynamic>> results;

  HttpResponseModel({required this.code, required this.results});

  factory HttpResponseModel.fromJson(Map<String, dynamic> json) {
    return HttpResponseModel(
      code: json['code'] as int,
      results: json['results'] as Response<Map<String, dynamic>>,
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'results': results,
      };
}
