import 'alarm_sort.dart';
import 'alarm_type.dart';
import 'latest_alarm.dart';
import 'village_list.dart';

class BigScreenModel {
  int villageCount;
  int keyPersonnelCount;
  int todayAlarmCount;
  List<LatestAlarm>? latestAlarms;
  List<VillageList>? villageList;
  List<AlarmType>? alarmTypes;
  List<AlarmSort>? alarmSort;

  BigScreenModel({
    this.villageCount = 0,
    this.keyPersonnelCount = 0,
    this.todayAlarmCount = 0,
    List<LatestAlarm>? latestAlarms,
    List<VillageList>? villageList,
    List<AlarmType>? alarmTypes,
    List<AlarmSort>? alarmSort,
  })  : latestAlarms = latestAlarms ?? <LatestAlarm>[],
        villageList = villageList ?? <VillageList>[],
        alarmTypes = alarmTypes ?? <AlarmType>[],
        alarmSort = alarmSort ?? <AlarmSort>[];

  factory BigScreenModel.fromJson(Map<String, dynamic> json) {
    return BigScreenModel(
      villageCount: json['village_count'] as int,
      keyPersonnelCount: json['key_personnel_count'] as int,
      todayAlarmCount: json['today_alarm_count'] as int,
      latestAlarms: (json['latest_alarms'] as List<dynamic>?)
              ?.map((e) => LatestAlarm.fromJson(e as Map<String, dynamic>))
              .toList() ??
          <LatestAlarm>[],
      villageList: (json['village_list'] as List<dynamic>?)
              ?.map((e) => VillageList.fromJson(e as Map<String, dynamic>))
              .toList() ??
          <VillageList>[],
      alarmTypes: (json['alarm_types'] as List<dynamic>?)
              ?.map((e) => AlarmType.fromJson(e as Map<String, dynamic>))
              .toList() ??
          <AlarmType>[],
      alarmSort: (json['alarm_sort'] as List<dynamic>?)
              ?.map((e) => AlarmSort.fromJson(e as Map<String, dynamic>))
              .toList() ??
          <AlarmSort>[],
    );
  }

  Map<String, dynamic> toJson() => {
        'village_count': villageCount,
        'key_personnel_count': keyPersonnelCount,
        'today_alarm_count': todayAlarmCount,
        'latest_alarms': latestAlarms?.map((e) => e.toJson()).toList(),
        'village_list': villageList?.map((e) => e.toJson()).toList(),
        'alarm_types': alarmTypes?.map((e) => e.toJson()).toList(),
        'alarm_sort': alarmSort?.map((e) => e.toJson()).toList(),
      };
}
