import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/user_info_model.dart';
import 'package:police/common/widget/form_text.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/page/home/<USER>';

class Person extends StatelessWidget {
  const Person({super.key});

  @override
  Widget build(BuildContext context) {
    HomeController controller = Get.find();
    return GetBuilder<HomeController>(
      id: 'sir',
      builder: (context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(controller.sir.length, (index) {
            return _buildPerson(controller.sir, index);
          }),
        );
      },
    );
  }

  Widget _buildPerson(List<UserInfoModel> sir, int index) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 40),
      // width: Get.width * .25,
      height: 430,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/pic/118.png'),
          fit: BoxFit.fill,
          // 确保将图片放在assets文件夹中，并在pubspec.yaml中声明
          // fit: BoxFit.contain, // 使用BoxFit.contain来完整显示图片
        ),
      ),
      child: Column(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: ImageWidget.network(
              sir[index].avatar ?? '',
              fit: BoxFit.cover,
              width: 130,
              height: 130 * 4 / 3,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            sir[index].name ?? '',
            style: const TextStyle(
                fontSize: 26,
                color: Color(0xd8cfe0ff),
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: 180,
            child: Column(
              children: [
                Text(
                  sir[index].commandPost ?? '',
                  style: const TextStyle(
                    color: Color(0xd8cfe0ff),
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 10),
                FormText(
                  labelText: '警号',
                  valueText: sir[index].policeNumber ?? '',
                  expandValue: true,
                  style: const TextStyle(
                    color: Color(0xd8cfe0ff),
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 10),
                FormText(
                  labelText: '电话',
                  valueText: sir[index].telephone ?? '',
                  expandValue: true,
                  style: const TextStyle(
                    color: Color(0xd8cfe0ff),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
