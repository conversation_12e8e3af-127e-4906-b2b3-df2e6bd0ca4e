import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/corner_card.dart';
import 'package:police/page/largescreen/controller.dart';

import '../../../common/model/big_screen_model/alarm_sort.dart';
import '../../../common/widget/animation_bar_chart.dart';

class SortChart extends StatelessWidget {
  const SortChart({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: CornerCard(
        child: GetBuilder<LargescreenController>(
          id: LargescernnIDS.sort,
          builder: (context) {
            List<SortChartModel> datas = [];
            List<AlarmSort> sorts =
                Get.find<LargescreenController>().bigscreenData.alarmSort ?? [];

            // 对排序列表按 alarmCount 降序排序
            sorts.sort((a, b) => b.alarmCount!.compareTo(a.alarmCount!));

            // 定义一个颜色列表
            final List<int> colors = [
              0xffFF0000, // 珊瑚色 (红色警示)
              0xfff77f00, // 橙色
              0xffe9c46a, // 黄色
              0xfff4a261, // 沙色
              0xff2a9d8f, // 深青色
              0xff6a4c93, // 紫色
              0xff264653, // 深蓝色
              0xffe63946, // 鲜红色
              0xfff1faee, // 温暖白色
              0xffa8dadc, // 浅蓝色
              0xff6a0dad, // 深紫色
              0xfff77f00, // 鲜橙色
              0xffd62839, // 鲜亮红色
              0xff00bfae, // 明亮青绿色
              0xffc72c48, // 鲜艳红色
              0xfff45d48, // 明亮橙红色
              0xff42a5f5, // 鲜艳蓝色
              0xff8e24aa, // 鲜亮紫色
              0xfffbc02d, // 明亮黄色
              0xff43a047, // 鲜艳绿色
              0xff00acc1, // 天蓝色
              0xff009688, // 青色
              0xff795548, // 棕色
              0xff607d8b, // 蓝灰色
              0xffc2185b, // 深玫瑰色
              0xffe040fb, // 亮紫色
              0xff8bc34a, // 亮绿
              0xffff9800, // 亮橙色
              0xffffc107, // 亮金色
              0xff9c27b0, // 紫水晶色
              0xff673ab7, // 深紫色
              0xff2196f3, // 蓝色
              0xffff5722, // 深橙色
              0xff4caf50, // 绿
              0xffffeb3b, // 黄色
              0xff3f51b5, // 靛蓝色
              0xff4e342e, // 深棕色
              0xff00695c, // 深青色
              0xffef5350, // 鲜红色
              0xffab47bc, // 浅紫色
              0xff66bb6a, // 浅绿色
              0xffffa726, // 橙黄色
              0xffd4e157, // 浅黄绿色
              0xff7e57c2, // 中紫色
              0xff26c6da, // 浅蓝色
              0xff5c6bc0, // 淡紫色
              0xff0097a7, // 青绿色
              0xffd32f2f, // 深红色
              0xff388e3c, // 深绿色
            ];

            for (var i = 0; i < sorts.length; i++) {
              datas.add(SortChartModel(sorts[i].name ?? '',
                  sorts[i].alarmCount!, colors[i % colors.length]));
            }

            return AnimatedBarChart(datas: datas);
          },
        ),
      ),
    );
  }
}
