import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:police/common/model/house_model.dart';
import 'package:police/common/model/people_detail_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/page/housedetail/index.dart';
import 'package:police/page/peopledetail/index.dart';

import '../../../common/widget/float_square_button.dart';

class FloatButton extends StatelessWidget {
  const FloatButton({
    super.key,
    required this.controller,
    this.houseId,
    required this.house,
    required this.unitId,
  });

  final HousedetailController controller;
  final HouseModel house;
  final int? houseId;
  final int unitId;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.all(36),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Visibility(
              visible: houseId != null,
              child: FloatSquareButton(
                onTap: () async => await controller
                    .deleteHouse(context, houseId!, name: house.propertyOwner),
                icon: Icons.delete,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 10),
            FloatSquareButton(
              onTap: () => controller.updateHouseDetail(
                context,
                houseId: houseId,
                detail: house,
                unitId: unitId,
              ),
              icon: FontAwesomeIcons.floppyDisk,
            ),
            const SizedBox(height: 10),
            FloatSquareButton(
              onTap: () => jump2page(
                  PeopledetailPage(PeopleDetailModel(), fromHouse: true)),
              icon: Icons.add,
            ),
          ],
        ),
      ),
    );
  }
}
