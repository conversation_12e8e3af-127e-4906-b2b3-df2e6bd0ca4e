import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class DragScrollableDatePicker extends StatefulWidget {
  const DragScrollableDatePicker({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _DragScrollableDatePickerState createState() =>
      _DragScrollableDatePickerState();
}

class _DragScrollableDatePickerState extends State<DragScrollableDatePicker> {
  DateTime _selectedDate = DateTime.now();
  double _startY = 0.0;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onPanStart: (details) {
          _startY = details.globalPosition.dy;
        },
        onPanUpdate: (details) {
          double deltaY = details.globalPosition.dy - _startY;
          _startY = details.globalPosition.dy;
          _updateDateTime(deltaY);
        },
        child: Container(
          color: Colors.transparent,
          child: CupertinoDatePicker(
            mode: CupertinoDatePickerMode.dateAndTime,
            onDateTimeChanged: (DateTime newDateTime) {
              setState(() {
                _selectedDate = newDateTime;
              });
            },
            initialDateTime: _selectedDate,
          ),
        ),
      ),
    );
  }

  void _updateDateTime(double deltaY) {
    final duration = Duration(milliseconds: (deltaY / 5).toInt());
    setState(() {
      _selectedDate = _selectedDate.add(duration);
    });
  }
}
