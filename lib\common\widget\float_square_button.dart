import 'package:flutter/material.dart';

class FloatSquareButton extends StatelessWidget {
  const FloatSquareButton({
    super.key,
    required this.onTap,
    required this.icon,
    this.color = Colors.blue,
    this.margin = EdgeInsets.zero,
    this.iconColor = Colors.white,
  });

  final Function() onTap;
  final IconData icon;
  final Color color;
  final Color iconColor;
  final EdgeInsets margin;

  // final Color color;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(10),
      onTap: onTap,
      child: Container(
        margin: margin,
        decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(10),
            boxShadow: const [
              BoxShadow(
                color: Colors.black26,
                offset: Offset(5, 5),
                blurRadius: 5,
              ),
            ]),
        padding: const EdgeInsets.all(10),
        child: Icon(icon, color: iconColor),
      ),
    );
  }
}
