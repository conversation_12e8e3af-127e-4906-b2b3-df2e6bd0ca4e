import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/build_api.dart';
import 'package:police/common/api/unit_api.dart';
import 'package:police/common/model/village_unit_model.dart';
import 'package:police/common/routes/jump_page.dart';

import '../../common/dialog.dart';

class UnitControllerIDS {
  static String unitItem = 'unitItem';
  static String turnpage = 'turnpage';
}

class UnitController extends GetxController {
  int code = 0;
  int currentPage = 1;
  int maxpages = 1;
  int newUnitCount = 0;

  List<VillageUnitModel> oldUnits = [];
  List<VillageUnitModel> units = [];

  List<String> tableHeader = ['名称', '一梯几户', '住户数量', '商铺数量'];

  /// 获取单元列表
  Future<void> getUnits(int buildId, {int page = 1}) async {
    code = 0;
    update([UnitControllerIDS.unitItem]);

    units = await UnitApi.getUnits(
      buildId,
      page: page,
      fail: (p0) {
        code = 404;
        update([UnitControllerIDS.unitItem]);
      },
    );

    code = 200;

    oldUnits = units.map((item) => item.copyWith()).toList();

    update([UnitControllerIDS.unitItem]);
  }

  /// 保存单元
  Future<void> save(BuildContext context) async {
    // 创建一个集合以记录需要保存的变化
    Set<VillageUnitModel> changedUnits = {};

    // 遍历当前的案件列表
    for (var unit in units) {
      bool isModified = true;

      // 查找是否存在匹配的旧案件
      for (var old in oldUnits) {
        if (old.name == unit.name &&
            old.residents == unit.residents &&
            old.residentsCount == unit.residentsCount &&
            old.merchants == unit.merchants) {
          isModified = false;
          break;
        }
      }

      if (isModified) {
        changedUnits.add(unit); // 将修改过的案件添加到集合中
      }
    }

    // 如果没有发现更改，则无需保存
    if (changedUnits.isEmpty) {
      showToast('没有发现更改的单元。');
      return;
    }

    try {
      // 保存所有修改过的案件
      for (var unit in changedUnits) {
        if (unit.id == null) {
          if (unit.name == null) {
            showToast('请输入单元名称');
            return;
          }
          Dialogs.showLoadingDialog(context);
          final result = await UnitApi.createUnit(detail: unit.toJson());
          units.remove(unit); // 移除旧的案件
          units.add(result); // 添加新的案件
        } else {
          Dialogs.showLoadingDialog(context);
          await UnitApi.patchUnitDetail(detail: unit.toJson(), id: unit.id!);
        }
      }

      oldUnits = units.map((unit) => unit.copyWith()).toList();
      // 更新状态
      update([UnitControllerIDS.unitItem]);
      showToast('保存成功');
      // await getCases();
    } catch (e) {}
  }

  /// 新增单元
  void addUnit(int buildId) {
    // 检查是否存在尚未保存的新单元
    bool hasUnsavedNewUnit = units.any((caseItem) => caseItem.id == null);

    if (hasUnsavedNewUnit) {
      newUnitCount += 1;
      if (newUnitCount >= 1) {
        showToast('请逐条添加');
        return;
      }
    }

    units.add(VillageUnitModel(
        villageBuildingId: buildId,
        residents: 0,
        residentsCount: 0,
        merchants: 0));
    update([UnitControllerIDS.unitItem]);
  }

  /// 翻页
  Future<void> turnPage(int index, int total, {required int buildId}) async {
    if (index == 0 && currentPage > 1) {
      currentPage -= 1;
    } else if (index == total) {
      currentPage = maxpages;
    } else if (index > 0 && index < total) {
      currentPage = index;
    }
    await getUnits(buildId, page: currentPage);
  }

  /// 删除单元
  deleteCase(BuildContext context, int index, {int? id, String? name}) async {
    if (id != null) {
      Dialogs.showConfirmDialog(
        context,
        title: '是否确认删除',
        subTitle: ' $name',
        content: '删除后将无法恢复！',
        onConfirm: () async {
          Dialogs.showLoadingDialog(context);
          await UnitApi.deleteUnit(id, pageBack: true);
          units.removeAt(index);
          update([UnitControllerIDS.unitItem]);
        },
      );
    } else {
      units.removeAt(index);
      update([UnitControllerIDS.unitItem]);
    }
  }

  /// 删除楼栋
  deleteBuild(BuildContext context, {required int id, String? name}) async {
    Dialogs.showConfirmDialog(
      context,
      title: '是否确认删除',
      subTitle: ' $name',
      content: '删除后将无法恢复！',
      onConfirm: () async {
        Dialogs.showLoadingDialog(context);
        await BuildApi.deleteBuild(id);
        pageback(result: 'remove');
      },
    );
  }
}
