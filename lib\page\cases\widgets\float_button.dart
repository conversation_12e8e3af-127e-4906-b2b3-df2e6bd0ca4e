import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:police/page/cases/controller.dart';

import '../../../common/widget/float_square_button.dart';

class FloatButton extends StatelessWidget {
  const FloatButton({super.key, required this.controller});

  final CasesController controller;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.all(36),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FloatSquareButton(
              onTap: () async {
                await controller.getAllVillage();
                // controller.getCases();
              },
              icon: Icons.refresh,
            ),
            const SizedBox(height: 10),
            FloatSquareButton(
              onTap: () => controller.save(context),
              icon: FontAwesomeIcons.floppyDisk,
            ),
            const SizedBox(height: 10),
            FloatSquareButton(
                onTap: () => controller.addCase(), icon: Icons.add),
          ],
        ),
      ),
    );
  }
}
