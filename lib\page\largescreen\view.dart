import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/keep_alive_widget.dart';
import 'package:police/page/largescreen/widgets/data_order.dart';
import 'package:police/page/largescreen/widgets/head_information.dart';
import 'package:police/page/largescreen/widgets/judgment_link.dart';
import 'package:police/page/largescreen/widgets/map_city.dart';
import 'package:police/page/largescreen/widgets/new_cases.dart';
import 'package:police/page/largescreen/widgets/personnel_data.dart';
import 'package:police/page/largescreen/widgets/police_information_case.dart';
import 'package:police/page/largescreen/widgets/sort_chart.dart';

import 'index.dart';

class LargescreenPage extends GetView<LargescreenController> {
  const LargescreenPage({super.key});

  // 主视图
  Widget _buildView() {
    return const Padding(
      padding: EdgeInsets.only(top: 70, right: 20, bottom: 20, left: 20),
      child: Row(
        children: [
          // 左边
          Expanded(flex: 1, child: PersonnelData()),
          SizedBox(width: 10),
          // 中间
          Expanded(
              flex: 2,
              child: Column(
                children: [
                  HeadInformation(),
                  MapCity(),
                  JudgmentLink(),
                  NewCases(),
                ],
              )),
          SizedBox(width: 10),
          // 右边
          Expanded(
            flex: 1,
            child: Column(
              children: [
                PoliceInformationCase(),
                SizedBox(height: 10),
                // DataOrder(),
                SortChart(),
                // CaseAndAlertInformation()
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LargescreenController>(
      init: LargescreenController(),
      id: "largescreen",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
