import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/page/area/controller.dart';
import 'package:police/page/area/widgets/float_button.dart';
import 'package:police/page/unit/index.dart';

class BuildWidget extends StatelessWidget {
  const BuildWidget({super.key, required this.villageId});

  final int villageId;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Stack(
        children: [
          GetBuilder<AreaController>(
              id: AreaControllerIDS.build,
              builder: (contro) {
                return Padding(
                  padding: const EdgeInsets.only(left: 0),
                  child: Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    children: List.generate(contro.builds.length, (index) {
                      return InkWell(
                        onTap: () {
                          if (contro.builds[index].id != null) {
                            jump2page(
                              UnitPage(
                                buildName: contro.builds[index].name ?? '未命名',
                                buildId: contro.builds[index].id!,
                              ),
                              onBack: (result) {
                                if (result != null) {
                                  if (result == 'remove') {
                                    contro.builds.removeAt(index);
                                    contro.update([AreaControllerIDS.build]);
                                  }
                                }
                              },
                            );
                          }
                        },
                        child: Container(
                          // padding: const EdgeInsets.all(20),
                          width: 150,
                          height: 150,
                          decoration: const BoxDecoration(
                              image: DecorationImage(
                            image: AssetImage('assets/pic/234.png'),
                            fit: BoxFit.fill,
                          )),
                          alignment: Alignment.center,
                          child: InputText(
                            textAlign: TextAlign.center,
                            hint: '请输入名称',
                            defaultText: contro.builds[index].name ?? '',
                            style: const TextStyle(
                                color: Colors.white, fontSize: 20),
                            onChanged: (text) {
                              contro.builds[index].name = text;
                              contro.areas[contro.currentArea]
                                  .villageBuilding![index].name = text;
                            },
                          ),
                        ),
                      );
                    }),
                  ),
                );
              }),
          Align(
            alignment: Alignment.bottomRight,
            child: FloatButton(villageId: villageId),
          ),
        ],
      ),
    );
  }
}
