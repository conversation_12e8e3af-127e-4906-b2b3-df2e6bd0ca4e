import 'package:get/get.dart';
import 'package:police/common/api/people_api.dart';
import 'package:police/common/model/people_detail_model.dart';

import '../../common/model/dropdown_menu_item_model.dart';

class PeoplesControllerIDS {
  static String peopleList = 'people_list';
  static String category = 'category';
  static String turnPage = 'turn_page';
}

class PeoplesController extends GetxController {
  List<PeopleDetailModel> peoples = [];
  List<PeopleDetailModel> fromFoucsPeoples = [];
  var focusPeople = <Map<String, dynamic>>[].obs;

  /// 人员总数
  // int total = 0;
  int code = 0;
  int fromFoucsCode = 0;

  /// 当前页数
  int currentPage = 1;
  int fromFoucsCurrentPage = 1;

  /// 最大页数
  int maxpage = 1;
  int fromFoucsMaxpage = 1;

  /// 当前人员类型（用于重点人员筛选）
  String personType = '';

  // TextEditingController searchContro = TextEditingController();
  String searchBy = 'search';
  String fromFoucsSearchBy = 'search';

  List<DropdownMenuItemModel> searchCategory = [
    DropdownMenuItemModel(text: '全部', value: 'search'),
    DropdownMenuItemModel(text: '姓名', value: 'name'),
    DropdownMenuItemModel(text: '证件号', value: 'id_number'),
    DropdownMenuItemModel(text: '电话', value: 'telephone'),
    DropdownMenuItemModel(text: '关注人员', value: 'person_interest'),
    DropdownMenuItemModel(text: '绰号', value: 'nickname'),
  ];

  /// 选中标签
  // List<String> selectedTag = [];

  /// 标签
  // List<DropdownMenuItemModel> tags = [
  //   DropdownMenuItemModel(text: '家庭暴力', value: '家庭暴力'),
  //   DropdownMenuItemModel(text: '抑郁精神障碍', value: '抑郁精神障碍'),
  //   DropdownMenuItemModel(text: '暴力倾向', value: '暴力倾向'),
  //   DropdownMenuItemModel(text: '需要心里疏导', value: '需要心里疏导'),
  // ];

  /// 筛选关注人员
  // int? sortFollow;

  /// 关注人员
  // List<DropdownMenuItemModel> isFollow = [
  //   DropdownMenuItemModel(text: '全部', value: -1),
  //   DropdownMenuItemModel(text: '是', value: 1),
  //   DropdownMenuItemModel(text: '否', value: 0),
  // ];

  /// 筛选重点人员
  bool? sortFoucs;

  /// 重点人员
  List<DropdownMenuItemModel> isFocus = [
    DropdownMenuItemModel(text: '重点人员', value: null),
    DropdownMenuItemModel(text: '是', value: true),
    DropdownMenuItemModel(text: '否', value: false),
  ];

  @override
  void onReady() {
    super.onReady();
    getPeopleList(page: 1);
  }

  /// 获取人员列表
  Future<List<PeopleDetailModel>> getPeopleList({
    required int page,
    String? name,
    bool clear = true,
    String? personType,
    bool fromFoucs = false,
  }) async {
    fromFoucs ? fromFoucsCode = 0 : code = 0;
    // 根据 fromFoucs 清理对应的数据
    if (clear) {
      fromFoucs ? fromFoucsPeoples.clear() : peoples.clear();
    }
    fromFoucs ? fromFoucsCurrentPage = page : currentPage = page;

    // 保存personType，以便在翻页时使用
    if (fromFoucs && personType != null && personType.isNotEmpty) {
      this.personType = personType;
    }

    update([PeoplesControllerIDS.peopleList, PeoplesControllerIDS.turnPage]);
    List<PeopleDetailModel> results = await PeopleApi.getPeopleList(
      fromFoucs: fromFoucs,
      parameters: {
        'page': page,
        'size': 30,
        if (personType != null && personType.isNotEmpty)
          'person_type': personType,
        if (name != null && name.isNotEmpty)
          fromFoucs ? fromFoucsSearchBy : searchBy: name,
        if (sortFoucs != null) 'is_key_personnel': sortFoucs,
      },
      fail: (_) {
        fromFoucs ? fromFoucsCode = 404 : code = 404;
        update(
            [PeoplesControllerIDS.peopleList, PeoplesControllerIDS.turnPage]);
      },
    );

    fromFoucs ? fromFoucsPeoples = results : peoples = results;
    fromFoucs ? fromFoucsCode = 200 : code = 200;
    update([PeoplesControllerIDS.peopleList, PeoplesControllerIDS.turnPage]);
    return fromFoucs ? fromFoucsPeoples : peoples;
  }

  /// 翻页
  Future turnPage(int index, int total, {bool fromFoucs = false}) async {
    if (index == 0 && _pageMax1(fromFoucs)) {
      fromFoucs ? fromFoucsCurrentPage -= 1 : currentPage -= 1;
    } else if (index == total) {
      fromFoucs
          ? fromFoucsCurrentPage = fromFoucsMaxpage
          : currentPage = maxpage;
    } else if (index > 0 && index < total) {
      fromFoucs ? fromFoucsCurrentPage = index : currentPage = index;
    }
    await getPeopleList(
      page: fromFoucs ? fromFoucsCurrentPage : currentPage,
      fromFoucs: fromFoucs, // 确保这里正确传递了fromFoucs参数
      // 添加personType参数，确保在重点人员模式下保持筛选条件
      personType: fromFoucs ? Get.find<PeoplesController>().personType : '',
    );
  }

  bool _pageMax1(bool fromFoucs) {
    return fromFoucs ? fromFoucsCurrentPage > 1 : currentPage > 1;
  }

  // changedCategory(String value, {bool fromFoucs = false}) {
  //   fromFoucs ? fromFoucsSearchBy = value : searchBy = value;
  //   update([PeoplesControllerIDS.category]);
  // }

  /// 新增筛选标签
  // addTag(String tag) {
  //   if (!selectedTag.contains(tag)) selectedTag.add(tag);
  //   update([PeoplesControllerIDS.category]);
  // }

  // /// 删除筛选标签
  // removeTag(int index) {
  //   selectedTag.removeAt(index);
  //   update([PeoplesControllerIDS.category]);
  // }
}
