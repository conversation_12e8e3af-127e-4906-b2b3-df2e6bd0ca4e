class PlaceModel {
  String? name;
  String? photo;
  String? introduce;
  List<dynamic>? pics;

  PlaceModel({
    this.name,
    this.photo,
    this.introduce,
    List<dynamic>? pics,
  }) : pics = pics ?? [];

  factory PlaceModel.fromJson(Map<String, dynamic> json) => PlaceModel(
        name: json['name'] as String?,
        photo: json['photo'] as String?,
        introduce: json['introduce'] as String?,
        pics: json['pics'] as List<dynamic>,
      );

  Map<String, dynamic> toJson() => {
        'name': name,
        'photo': photo,
        'introduce': introduce,
        'pics': pics,
      };
}
