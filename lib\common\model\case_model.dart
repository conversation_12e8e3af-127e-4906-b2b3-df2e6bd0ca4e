class CaseModel {
  int? id;
  int? villageId;
  String? types;
  String? sort;
  DateTime? occurrenceTime;
  String? title;
  String graphics;
  List<dynamic>? policeFeedBack;
  List<dynamic>? opinions;
  List<dynamic>? finalFeedback;

  CaseModel({
    this.id,
    this.villageId,
    this.types,
    this.sort,
    this.occurrenceTime,
    this.title,
    this.graphics = '',
    this.policeFeedBack = const [],
    this.opinions = const [],
    this.finalFeedback = const [],
  });

  factory CaseModel.fromJson(Map<String, dynamic> json) => CaseModel(
        id: json['id'] as int?,
        villageId: json['village_id'] as int?,
        types: json['types'] as String?,
        sort: json['sort'] as String?,
        occurrenceTime: json['occurrence_time'] == null
            ? null
            : DateTime.parse(json['occurrence_time'] as String),
        title: json['title'] as String?,
        graphics: json['graphics'] as String,
        policeFeedBack: json['police_feedback'] as List<dynamic>?,
        opinions: json['opinions'] as List<dynamic>?,
        finalFeedback: json['final_feedback'] as List<dynamic>?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'village_id': villageId,
        'types': types,
        'sort': sort,
        'occurrence_time': occurrenceTime?.toIso8601String(),
        'title': title,
        'graphics': graphics,
        'police_feedback': policeFeedBack,
        'opinions': opinions,
        'final_feedback': finalFeedback,
      };

  // 用于复制当前实例
  CaseModel copy() {
    return CaseModel(
      id: id,
      villageId: villageId,
      title: title,
      types: types,
      sort: sort,
      graphics: graphics,
      occurrenceTime: occurrenceTime,
      policeFeedBack: policeFeedBack,
      opinions: opinions,
      finalFeedback: finalFeedback,
    );
  }
}
