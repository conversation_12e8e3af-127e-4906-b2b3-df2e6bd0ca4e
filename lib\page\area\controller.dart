import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/area_api.dart';
import 'package:police/common/api/build_api.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/model/village_area_model/village_building.dart';
import 'package:police/common/routes/jump_page.dart';
import '../../common/model/village_area_model/village_area_model.dart';

class AreaControllerIDS {
  static String drawer = 'drawer';
  static String build = 'build';
}

class AreaController extends GetxController {
  /// 旧的区域列表 用于对筛选被修改的区域
  List<VillageAreaModel> oldAreas = [];

  /// 区域列表
  List<VillageAreaModel> areas = [];

  int newAreaCount = 0;

  /// 旧的楼栋列表 用于对筛选被修改的区域
  List<VillageBuilding> oldBuilds = [];

  /// 楼栋列表
  List<VillageBuilding> builds = [];

  int newBuildStartAt = 0;
  int newBuildEndAt = 0;

  /// 当前区域
  int currentArea = 0;

  /// 区域状态码
  int areaCode = 0;

  /// 是否正在加载区域
  bool areaLoading = false;

  /// 获取区域列表
  Future getAreas(int villageId) async {
    areas.clear();
    builds.clear();
    areaCode = 0;
    areaLoading = true;
    update([AreaControllerIDS.build, AreaControllerIDS.drawer]);
    areas = await AreaApi.getAreas(
      villageId,
      fail: () {
        areaCode = 404;
        update([AreaControllerIDS.drawer, AreaControllerIDS.build]);
      },
    );

    oldAreas = areas
        .map((item) =>
            VillageAreaModel.fromJson(json.decode(json.encode(item.toJson()))))
        .toList();

    if (areas.isNotEmpty) {
      builds = areas[currentArea].villageBuilding ?? <VillageBuilding>[];
      builds.sort((a, b) => a.name!.compareTo(b.name!));

      oldBuilds = builds
          .map((item) =>
              VillageBuilding.fromJson(json.decode(json.encode(item.toJson()))))
          .toList();
    }

    areaCode = 200;
    areaLoading = false;

    update([AreaControllerIDS.drawer, AreaControllerIDS.build]);
  }

  /// 创建/修改区域
  Future createAreas(BuildContext context, int villageId) async {
    // 创建一个集合以记录需要保存的变化
    Set<VillageAreaModel> changedAreas = {};

    // 遍历当前的区域列表
    for (var area in areas) {
      bool isModified = true;

      for (var old in oldAreas) {
        if (old.name == area.name) {
          isModified = false;
          break;
        }
      }

      if (isModified) {
        changedAreas.add(area); // 将修改过的区域添加到集合中
      }
    }

    // 如果没有发现更改，则无需保存
    if (changedAreas.isEmpty) {
      showToast('没有发现更改的区域。');
      return;
    }

    try {
      // 保存所有修改过的区域
      for (var area in changedAreas) {
        if (area.id == null) {
          if (area.name == null) {
            showToast('请填写区域名称');
            return;
          }

          Dialogs.showLoadingDialog(context);
          final result = await AreaApi.createArea(
              newArea: {"village_id": villageId, "name": area.name});
          areas.removeAt(currentArea);
          areas.add(result);
          newAreaCount = 0;
        } else {
          Dialogs.showLoadingDialog(context);
          await AreaApi.patchAreaDetail(id: area.id!, detail: area.toJson());
        }
      }

      oldAreas = areas
          .map((item) => VillageAreaModel.fromJson(
              json.decode(json.encode(item.toJson()))))
          .toList();

      // 更新状态
      update([AreaControllerIDS.drawer, AreaControllerIDS.build]);
      showToast('保存成功');
    } catch (e) {
      showToast('保存失败');
    }
  }

  /// 创建/修改楼栋
  Future<void> createBuilds(BuildContext context, int villageId) async {
    // 创建一个集合以记录需要保存的变化
    Set<VillageBuilding> changedBuilds = {};
    List<Map<String, dynamic>> newBuilds = [];

    // 遍历当前区域的楼栋列表
    for (VillageBuilding build
        in areas[currentArea].villageBuilding ?? <VillageBuilding>[]) {
      bool isModified = true;

      for (var old in oldBuilds) {
        if (old.name == build.name) {
          isModified = false;
          break;
        }
      }

      if (isModified) {
        changedBuilds.add(build); // 将修改过的楼栋添加到集合中
      }
    }

    // 如果没有发现更改，则无需保存
    if (changedBuilds.isEmpty) {
      showToast('没有发现更改的楼栋。');
      return;
    }

    try {
      // 保存所有修改过的楼栋
      for (var build in changedBuilds) {
        if (build.id == null) {
          newBuilds.add(
              {"village_area_id": areas[currentArea].id, "name": build.name});
        } else {
          Dialogs.showLoadingDialog(context);
          await BuildApi.patchBuildDetail(
              id: build.id!, detail: build.toJson());
        }
      }

      for (var build in newBuilds) {
        if (build['name'] == null) {
          showToast('请填写楼栋名称');
          return;
        }
      }

      if (newBuilds.isNotEmpty) {
        // ignore: use_build_context_synchronously
        Dialogs.showLoadingDialog(context);
        final results = await BuildApi.createBuilds(newBuilds: newBuilds);
        // builds.removeRange(newBuildStartAt, builds.length);
        // builds.addAll(results);
        // newBuildStartAt = builds.length;
      }

      oldAreas = areas
          .map((item) => VillageAreaModel.fromJson(
              json.decode(json.encode(item.toJson()))))
          .toList();

      if (areas.isNotEmpty) {
        builds = areas[currentArea].villageBuilding ?? <VillageBuilding>[];
        oldBuilds = builds
            .map((item) => VillageBuilding.fromJson(
                json.decode(json.encode(item.toJson()))))
            .toList();
      }
      getAreas(villageId);
      // 更新状态
      update([AreaControllerIDS.drawer, AreaControllerIDS.build]);
      showToast('保存成功');
    } catch (e) {
      showToast('保存失败');
    }
  }

  /// 删除区域
  deleteArea(BuildContext context,
      {int? id, String? name, required int index}) {
    if (id != null) {
      Dialogs.showConfirmDialog(
        context,
        title: '是否确认删除',
        subTitle: ' $name',
        content: '删除后无法恢复！',
        onConfirm: () async {
          Dialogs.showLoadingDialog(context);
          await AreaApi.deleteArea(id);
          showToast('删除成功');
          areas.removeAt(index);
          currentArea -= 1;
          update([AreaControllerIDS.drawer, AreaControllerIDS.build]);
        },
      );
    } else {
      newAreaCount -= 1;
      currentArea -= 1;
      areas.removeAt(index);
      update([AreaControllerIDS.drawer, AreaControllerIDS.build]);
    }
  }
}
