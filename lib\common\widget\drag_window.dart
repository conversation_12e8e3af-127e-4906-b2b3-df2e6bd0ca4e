import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/material.dart';

class DragWindowWidget extends StatelessWidget {
  const DragWindowWidget({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.deferToChild, // 允许子组件处理触摸事件
      onPanStart: (details) {
        appWindow.startDragging();
      },
      child: Container(
        color: Colors.transparent,
        child: child,
      ),
    );
  }
}
