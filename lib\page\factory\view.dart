import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/keep_alive_widget.dart';
import 'package:police/page/factory/widgets/factory_info_two.dart';

import '../main/controller.dart';
import 'index.dart';

class FactoryPage extends GetView<FactoryController> {
  const FactoryPage({super.key});

  // 主视图
  Widget _buildView(BuildContext context, int crossAxisCount) {
    return Padding(
      padding: const EdgeInsets.only(top: 60),
      child: GetBuilder<MainController>(
          id: 'factory',
          builder: (controller) {
            return GridView.builder(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 50),
              itemCount: 50,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                childAspectRatio: .7,
                mainAxisSpacing: 30,
                crossAxisSpacing: 30,
              ),
              itemBuilder: (_, index) => const FactoryInfoTwo(),
            );
          }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return KeepAliveWidget(
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final crossAxisCount = (constraints.maxWidth / 300).round();
          return GetBuilder<FactoryController>(
            init: FactoryController(),
            id: "factory",
            builder: (_) {
              return _buildView(context, crossAxisCount);
            },
          );
        },
      ),
    );
  }
}
