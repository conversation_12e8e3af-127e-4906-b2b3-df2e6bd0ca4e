import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MapWidget extends StatelessWidget {
  const MapWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Container(
        height: Get.height,
        padding: const EdgeInsets.symmetric(vertical: 100, horizontal: 100),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.asset(
            'assets/pic/map_mini.png',
            fit: BoxFit.contain,
          ),
        ),
      );
    });
  }
}
