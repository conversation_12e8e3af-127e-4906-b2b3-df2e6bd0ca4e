import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:lottie/lottie.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/corner_card.dart';
import 'package:police/common/widget/empty_widget.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/common/widget/scale_animated_widget.dart';

import 'api/people_api.dart';
import 'model/people_detail_model.dart';

class Dialogs {
  static showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return UnconstrainedBox(
          child: Lottie.asset(
            'assets/animations/ekg.json',
            fit: BoxFit.cover,
            width: 200,
            height: 200,
          ),
        );
      },
    );
  }

  /// 确认删除弹窗
  static showConfirmDialog(
    BuildContext context, {
    required String title,
    String? subTitle,
    String content = '',
    Widget otherButton = const SizedBox(),
    required Function() onConfirm,
    double width = 500,
    double? height,
  }) {
    showDialog(
      context: context,
      builder: (context) {
        return ScaleAnimatedWidget(
          begin: 0,
          end: 1,
          duration: const Duration(milliseconds: 300),
          userMouse: false,
          child: Material(
            color: Colors.transparent,
            child: UnconstrainedBox(
              child: CornerCard(
                width: width,
                height: height,
                // padding: const EdgeInsets.all(30),
                // width: 500,
                // decoration: const BoxDecoration(
                //   image: DecorationImage(
                //     image: AssetImage('assets/pic/234-2.png'),
                //     fit: BoxFit.fill,
                //   ),
                // ),
                child: Padding(
                  padding: const EdgeInsets.all(30),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text.rich(
                        TextSpan(children: [
                          TextSpan(
                            text: title,
                            style: const TextStyle(
                                fontSize: 22, color: Colors.white),
                          ),
                          TextSpan(
                            text: subTitle,
                            style: const TextStyle(
                                fontSize: 22, color: Colors.red),
                          ),
                        ]),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        content,
                        style:
                            const TextStyle(fontSize: 17, color: Colors.white),
                      ),
                      const SizedBox(height: 80),
                      Row(
                        children: [
                          otherButton,
                          const Spacer(),
                          NormolButton(
                            lable: '取消',
                            width: 80,
                            onTap: () => pageback(),
                          ),
                          const SizedBox(width: 10),
                          NormolButton(
                            lable: '确认',
                            width: 80,
                            color: Colors.red.withOpacity(.45),
                            onTap: () {
                              pageback();
                              onConfirm();
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 输入框弹窗
  static showEditTextDialog(
    BuildContext context, {
    required TextEditingController controller,
    required Function(String text) onConfirm,
    String? title,
    String? hint = '请输入详情',
    String? defaultText,
    double width = 500,
    double height = 300,
  }) {
    showDialog(
        context: context,
        builder: (context) {
          if (defaultText != null) {
            controller.text = defaultText;
          }
          return UnconstrainedBox(
            child: ScaleAnimatedWidget(
              begin: 0,
              end: 1,
              duration: const Duration(milliseconds: 300),
              userMouse: false,
              child: CornerCard(
                width: width,
                height: height,
                child: Padding(
                  padding: const EdgeInsets.all(30),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      title != null
                          ? Text(
                              title,
                              style: const TextStyle(
                                  fontSize: 20, color: Colors.white),
                            )
                          : const SizedBox(),
                      const SizedBox(height: 20),
                      Expanded(
                        child: InputText(
                          controller: controller,
                          maxLines: null,
                          isDense: false,
                          hint: hint,
                        ),
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          NormolButton(
                            lable: '取消',
                            width: 80,
                            onTap: () => pageback(),
                          ),
                          const SizedBox(width: 10),
                          NormolButton(
                            lable: '确认',
                            width: 80,
                            color: Colors.red.withOpacity(.45),
                            onTap: () {
                              pageback();
                              onConfirm(controller.text.trim());
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }

  static showPickerPersonDialog(
    BuildContext context, {
    required Function(PeopleDetailModel person) onSelected,
  }) {
    TextEditingController search = TextEditingController();
    List<PeopleDetailModel> peoples = [];
    int page = 1;
    int code = 0;
    bool loading = false;
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return UnconstrainedBox(
            child: ScaleAnimatedWidget(
              begin: 0,
              end: 1,
              duration: const Duration(milliseconds: 300),
              userMouse: false,
              child: CornerCard(
                padding: const EdgeInsets.all(30),
                child: SizedBox(
                  width: 300,
                  height: 500,
                  child: StatefulBuilder(builder: (context, state) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        InputText(
                          controller: search,
                          isDense: false,
                          hint: '请输入人员姓名',
                          textAlign: TextAlign.center,
                          boxDecoration: BoxDecoration(
                            color: Colors.grey.withOpacity(.3),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          onEditingComplete: (text) async {
                            if (text.isNotEmpty) {
                              page = 1;
                              code = 0;
                              loading = true;
                              state(() {});
                              peoples = await PeopleApi.getPeopleList(
                                parameters: {'page': page, 'search': text},
                                fail: (_) {
                                  code = 404;
                                  state(() {});
                                },
                              );
                              loading = false;
                              code = 200;
                              state(() {});
                            } else {
                              showToast('请不要留空');
                            }
                          },
                        ),
                        _buildSearchResults(code, loading, peoples,
                            onSelected: onSelected),
                        NormolButton(
                          lable: '取消',
                          onTap: () => pageback(),
                        ),
                      ],
                    );
                  }),
                ),
              ),
            ),
          );
        });
  }

  static Expanded _buildSearchResults(
    int code,
    bool loading,
    List<PeopleDetailModel> peoples, {
    required Function(PeopleDetailModel person) onSelected,
  }) {
    return Expanded(
      child: Center(
        child: code == 404
            ? const HttpErrorWidget()
            : code == 0 && loading
                ? const LoadingWidget()
                : code == 200 && peoples.isEmpty
                    ? const EmptyWidget()
                    : code == 0 && !loading
                        ? const SizedBox()
                        : ListView.separated(
                            separatorBuilder: (_, __) =>
                                const SizedBox(height: 5),
                            itemCount: peoples.length,
                            itemBuilder: (_, index) {
                              return InkWell(
                                onTap: () {
                                  onSelected(peoples[index]);
                                },
                                child: Container(
                                  height: 60,
                                  padding: const EdgeInsets.all(10),
                                  child: Row(
                                    children: [
                                      ClipOval(
                                        child: ImageWidget.network(
                                          peoples[index].photo ?? '',
                                          width: 40,
                                          height: 40,
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Text(
                                        peoples[index].name ?? '',
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
      ),
    );
  }
}
