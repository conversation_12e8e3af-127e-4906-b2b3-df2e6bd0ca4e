import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:police/common/model/house_model.dart';

import '../../../common/widget/check_box.dart';
import '../../../common/widget/form_input.dart';
import '../controller.dart';

class FeedBack extends StatelessWidget {
  const FeedBack({super.key, required this.detail});

  final HouseModel detail;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HousedetailController>(
        id: HousedetailIDS.feedback,
        builder: (contro) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '反映问题建议',
                style: TextStyle(fontSize: 40, color: Color(0xffffffff)),
              ),
              const SizedBox(height: 10),
              CheckBoxWidegt(
                label: '',
                chekcs: const ['信访', '人社', '司法', '民政', '文旅', '卫健', '土地', '其他'],
                checked: detail.feedback,
                otherStr: detail.feedbackAdditional ?? '',
                onChanged: (val, text) {
                  if (val) {
                    detail.feedback.add(text);
                  } else {
                    detail.feedback.remove(text);
                  }
                  contro.update([HousedetailIDS.feedback]);
                },
                onChangedOther: (text) {
                  detail.feedbackAdditional = text;
                },
              ),
              const SizedBox(height: 20),
              FormInput(
                label: '简要情况',
                defaultText: detail.feedbackSummary ?? '',
                maxLines: 999,
                fontSize: 20,
                textInputAction: TextInputAction.newline,
                maxHeight: 200,
                onChanged: (text) {
                  detail.feedbackSummary = text;
                },
              ),
            ],
          );
        });
  }
}
