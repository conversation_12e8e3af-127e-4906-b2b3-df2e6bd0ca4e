import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;
import 'package:police/common/api/apis.dart';
import 'package:police/common/model/house_model.dart';
import 'package:police/common/model/people_rela_model/people_rela_model.dart';
import 'package:police/common/util/https.dart';
import 'package:police/page/house/controller.dart';

class HouseApi {
  /// 获取房屋列表
  static Future<List<HouseModel>> getHouses(int unitId,
      {required Function(Response) fail, int page = 1}) async {
    List<HouseModel> houses = [];
    Response response = await Https.get(
      Apis.house,
      queryParameters: {'village_unit_id': unitId, 'page': page},
      fail: fail,
    );

    for (var house in response.data['items']) {
      houses.add(HouseModel.fromJson(house));
    }

    Get.find<HouseController>().maxpages = response.data['pages'];

    return houses;
  }

  /// 获取房屋详情
  static Future<HouseModel> getHouseDetail(int houseId,
      {required Function(Response) fail}) async {
    Response response = await Https.get('${Apis.house}/$houseId', fail: fail);

    return HouseModel.fromJson(response.data);
  }

  /// 创建房屋
  static Future<HouseModel> createHouse(HouseModel house,
      {required Function(Response) fail}) async {
    Response response = await Https.post(
      Apis.house,
      data: house.toJson(),
      contentType: 'application/json',
      pageBack: true,
      fail: fail,
    );

    return HouseModel.fromJson(response.data);
  }

  /// 修改房屋
  static Future<HouseModel> patchHouse(
      {required int id,
      required HouseModel house,
      required Function(Response) fail}) async {
    Response response = await Https.patch(
      '${Apis.house}/$id',
      data: house.toJson(),
      pageBack: true,
      fail: fail,
    );

    return HouseModel.fromJson(response.data);
  }

  /// 删除房屋
  static deleteHouse(int houseId) async {
    await Https.delete('${Apis.house}/$houseId', pageBack: true);
  }

  /// 根据房屋获取家庭关系列表
  static Future<List<PeopleRelaModel>> getHouseRelationFromHouse({
    required int houseId,
    required Function(Response) fail,
  }) async {
    List<PeopleRelaModel> peoples = [];
    Response response = await Https.get(
      Apis.houseRelation,
      queryParameters: {'household_id': houseId},
      fail: fail,
    );

    for (var item in response.data['items']) {
      peoples.add(PeopleRelaModel.fromJson(item));
    }

    return peoples;
  }

  /// 根据人员获取家庭关系列表
  static Future<List<PeopleRelaModel>> getHouseRelationFromPerson({
    required Function(Response) fail,
    required int personId,
  }) async {
    List<PeopleRelaModel> peoples = [];
    Response response = await Https.get(
      Apis.houseRelation,
      queryParameters: {'person_id': personId},
      fail: fail,
    );

    for (var item in response.data['items']) {
      peoples.add(PeopleRelaModel.fromJson(item));
    }

    return peoples;
  }

  /// 创建人员关系
  static Future<PeopleRelaModel> createHouseRelation(
      {required int houseId,
      required int peopelId,
      required String title,
      required Function(Response) fail}) async {
    Response response = await Https.post(
      Apis.houseRelation,
      data: {
        "household_id": houseId,
        "person_id": peopelId,
        "relation_name": title,
      },
      fail: fail,
      pageBack: true,
    );

    return PeopleRelaModel.fromJson(response.data);
  }

  /// 修改人员关系
  static Future<PeopleRelaModel> patchHouseRelation(
      {required int relaId, required Map<String, dynamic> data}) async {
    Response response = await Https.patch(
      '${Apis.houseRelation}/$relaId',
      data: data,
      // pageBack: true,
    );

    return PeopleRelaModel.fromJson(response.data);
  }

  /// 删除人员关系
  static deleteHouseRelation(int id) async {
    await Https.delete('${Apis.houseRelation}/$id', pageBack: true);
  }
}
