import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/empty_widget.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/scaffold_widget.dart';
import 'package:police/page/house/widgets/float_button.dart';
import 'package:police/page/house/widgets/table_data.dart';
import 'package:police/page/house/widgets/table_header.dart';

import '../../common/widget/turn_page.dart';
import 'index.dart';

class HousePage extends GetView<HouseController> {
  const HousePage(this.unitId, {super.key});

  final int unitId;

  // 主视图
  Widget _buildView() {
    return Padding(
      padding: const EdgeInsets.only(top: 60),
      child: GetBuilder<HouseController>(
          id: HouseControllerIDS.houses,
          builder: (context) {
            return controller.code == 0
                ? const LoadingWidget()
                : controller.code == 404
                    ? const HttpErrorWidget()
                    : controller.code == 200 && controller.houses.isEmpty
                        ? const EmptyWidget()
                        : SingleChildScrollView(
                            child: Column(
                              children: [
                                HouseTableHeader(controller: controller),
                                HouseTableData(unitId: unitId),
                                const SizedBox(height: 50),
                                GetBuilder<HouseController>(
                                    id: HouseControllerIDS.turnpage,
                                    builder: (context) {
                                      return Visibility(
                                        visible: controller.houses.isNotEmpty,
                                        child: Align(
                                          alignment: Alignment.bottomCenter,
                                          child: TurnPage(
                                            itemCount: controller.maxpages,
                                            currentIndex:
                                                controller.currentPage,
                                            onTap: (index, total) => controller
                                                .turnPage(index, total,
                                                    unitId: unitId),
                                          ),
                                        ),
                                      );
                                    }),
                                const SizedBox(height: 50),
                              ],
                            ),
                          );
          }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HouseController>(
      init: HouseController(),
      id: "house",
      builder: (_) {
        controller.getHouses(unitId);
        return ScaffoldWidget(
          body: _buildView(),
          floatButton: [
            FloatButton(
              controller: controller,
              unitId: unitId,
            )
          ],
        );
      },
    );
  }
}
