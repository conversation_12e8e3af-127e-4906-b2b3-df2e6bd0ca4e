import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/widget/empty_widget.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/page/area/controller.dart';

import '../../../common/model/village_area_model/village_area_model.dart';
import '../../../common/model/village_area_model/village_building.dart';
import '../../../common/widget/normol_button.dart';

class DrawerWidget extends StatelessWidget {
  const DrawerWidget({super.key, required this.villageId});

  final int villageId;

  @override
  Widget build(BuildContext context) {
    // int currentIndex = 0;

    return GetBuilder<AreaController>(
        id: AreaControllerIDS.drawer,
        builder: (contro) {
          return Container(
            width: 270,
            height: double.infinity,
            padding: const EdgeInsets.all(5),
            alignment: Alignment.center,
            child: Column(
              children: [
                Expanded(
                  child: contro.areaLoading
                      ? const LoadingWidget()
                      : contro.areaCode == 200 && contro.areas.isEmpty
                          ? const EmptyWidget()
                          : contro.areaCode == 404
                              ? const HttpErrorWidget()
                              : ScrollConfiguration(
                                  behavior: ScrollConfiguration.of(context)
                                      .copyWith(scrollbars: false), // 隐藏滚动条
                                  child: ListView.separated(
                                    itemCount: contro.areas.length,
                                    // itemCount: 20,
                                    separatorBuilder: (context, index) =>
                                        const SizedBox(height: 5),
                                    itemBuilder: (context, index) {
                                      VillageAreaModel area =
                                          contro.areas[index];
                                      return InkWell(
                                        onTap: () => contro.currentArea =
                                            _switchArea(contro.currentArea,
                                                index, contro),
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 270 / 4,
                                            vertical: 5,
                                          ),
                                          decoration:
                                              contro.currentArea == index
                                                  ? const BoxDecoration(
                                                      image: DecorationImage(
                                                        image: AssetImage(
                                                            'assets/pic/chart_border.png'),
                                                        fit: BoxFit.fill,
                                                      ),
                                                      // color: Color(int.parse(area.color!)).withOpacity(.3),
                                                    )
                                                  : null,
                                          alignment: Alignment.center,
                                          child: InputText(
                                            defaultText: area.name ?? '',
                                            textAlign: TextAlign.center,
                                            hint: '请输入名称',
                                            isDense: false,
                                            style: const TextStyle(
                                                color: Colors.white),
                                            onChanged: (text) =>
                                                area.name = text,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                ),
                const SizedBox(height: 10),
                Visibility(
                  visible: !contro.areaLoading,
                  child: NormolButton(
                    lable: '保存区域',
                    color: const Color(0xca073773),
                    onTap: () => contro.createAreas(context, villageId),
                  ),
                ),
                const SizedBox(height: 10),
                Visibility(
                  visible: !contro.areaLoading,
                  child: NormolButton(
                    lable: '新增区域',
                    color: const Color.fromARGB(201, 5, 104, 224),
                    onTap: () async {
                      if (contro.newAreaCount < 1) {
                        contro.areas.add(VillageAreaModel(
                          villageId: villageId,
                          villageBuilding: <VillageBuilding>[],
                        ));
                        contro.newAreaCount += 1;
                        contro.builds.clear();
                        contro.currentArea = contro.areas.length - 1;
                        contro.oldBuilds = contro.builds
                            .map((item) => VillageBuilding.fromJson(
                                json.decode(json.encode(item.toJson()))))
                            .toList();
                        contro.update([
                          AreaControllerIDS.drawer,
                          AreaControllerIDS.build
                        ]);
                      } else {
                        showToast('请逐条添加');
                      }
                      // await contro.saveArea(contro.areas.last, villageId);
                    },
                  ),
                ),
                const SizedBox(height: 10),
                Visibility(
                  visible: !contro.areaLoading,
                  child: NormolButton(
                    lable: '删除区域',
                    color: Colors.red.withOpacity(.5),
                    onTap: () => contro.deleteArea(
                      context,
                      index: contro.currentArea,
                      id: contro.areas[contro.currentArea].id,
                      name: contro.areas[contro.currentArea].name,
                    ),
                  ),
                ),
              ],
            ),
          );
        });
  }

  /// 切换区域
  int _switchArea(int currentIndex, int index, AreaController contro) {
    currentIndex = index;
    contro.currentArea = index;
    contro.builds = contro.areas[index].villageBuilding ?? <VillageBuilding>[];
    contro.oldBuilds = contro.builds
        .map((item) =>
            VillageBuilding.fromJson(json.decode(json.encode(item.toJson()))))
        .toList();
    contro.newBuildStartAt = contro.builds.length;
    contro.update([AreaControllerIDS.drawer, AreaControllerIDS.build]);
    return currentIndex;
  }
}
