import 'dart:ui';

import 'package:flutter/material.dart';

class RightClickMenu extends StatelessWidget {
  const RightClickMenu({
    super.key,
    required this.child,
    this.items,
  });

  final Widget child;
  final List<PopupMenuEntry>? items;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (event) {
        if (event.kind == PointerDeviceKind.mouse && event.buttons == 2) {
          _showMenu(context, event);
        }
      },
      child: child,
    );
  }

  void _showMenu(BuildContext context, PointerDownEvent event) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        event.position.dx,
        event.position.dy,
        event.position.dx,
        event.position.dy,
      ),
      items: items ?? [],
    );
  }
}
