import 'village_building.dart';

class VillageAreaModel {
  int? id;
  int? villageId;
  String? name;
  DateTime? createTime;
  List<VillageBuilding>? villageBuilding;

  VillageAreaModel({
    this.id,
    this.villageId,
    this.name,
    this.createTime,
    this.villageBuilding,
  });

  factory VillageAreaModel.fromJson(Map<String, dynamic> json) {
    return VillageAreaModel(
      id: json['id'] as int?,
      villageId: json['village_id'] as int?,
      name: json['name'] as String?,
      createTime: json['create_time'] == null
          ? null
          : DateTime.parse(json['create_time'] as String),
      villageBuilding: (json['village_building'] as List<dynamic>?)
          ?.map((e) => VillageBuilding.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'village_id': villageId,
        'name': name,
        'create_time': createTime?.toIso8601String(),
        'village_building': villageBuilding?.map((e) => e.toJson()).toList(),
      };
}
