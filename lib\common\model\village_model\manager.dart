class ManagerModel {
  String name;
  String avatar;
  List<dynamic>? pics;

  ManagerModel({
    this.name = '',
    this.avatar = '',
    List<dynamic>? pics,
  }) : pics = pics ?? [];

  factory ManagerModel.fromJson(Map<String, dynamic> json) => ManagerModel(
        name: json['name'] as String,
        avatar: json['avatar'] as String,
        pics: json['pics'] as List<dynamic>,
      );

  Map<String, dynamic> toJson() => {
        'name': name,
        'avatar': avatar,
        'pics': pics,
      };
}
