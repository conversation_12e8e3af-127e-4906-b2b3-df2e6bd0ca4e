class List {
  int? villageId;
  String? createTime;
  String? belief;
  dynamic nickname;
  dynamic riskAssessmentFamilyStability;
  dynamic hairstyle;
  dynamic qq;
  dynamic resume;
  int? id;
  DateTime? updateTime;
  String? veteranStatus;
  dynamic employer;
  dynamic socialSupportStatus;
  dynamic faceShape;
  dynamic driverLicense;
  dynamic socialRelations;
  String? photo;
  dynamic telephone;
  dynamic profession;
  dynamic personalActivityRange;
  dynamic realPerformance;
  dynamic eyes;
  dynamic passport;
  dynamic sourceIncome;
  String? idNumber;
  dynamic residenceAddress;
  dynamic graphics;
  dynamic healthStatus;
  dynamic overallAssessment;
  bool? scars;
  dynamic fingerprint;
  dynamic associates;
  String? name;
  bool? residenceSeparation;
  String? personInterest;
  dynamic nationality;
  dynamic height;
  bool? tattoos;
  dynamic dna;
  dynamic notes;
  String? gender;
  String? politicalStatus;
  bool? isKeyPersonnel;
  dynamic personType;
  dynamic weight;
  dynamic otherCharacteristics;
  dynamic voiceprint;
  dynamic birthDate;
  String? maritalStatus;
  String? serialNumber;
  dynamic strengths;
  dynamic skinColor;
  dynamic wechat;
  dynamic iris;
  dynamic education;
  dynamic formerName;
  bool? wechatGroup;
  dynamic accent;
  dynamic alipay;
  dynamic criminalRecord;

  List({
    this.villageId,
    this.createTime,
    this.belief,
    this.nickname,
    this.riskAssessmentFamilyStability,
    this.hairstyle,
    this.qq,
    this.resume,
    this.id,
    this.updateTime,
    this.veteranStatus,
    this.employer,
    this.socialSupportStatus,
    this.faceShape,
    this.driverLicense,
    this.socialRelations,
    this.photo,
    this.telephone,
    this.profession,
    this.personalActivityRange,
    this.realPerformance,
    this.eyes,
    this.passport,
    this.sourceIncome,
    this.idNumber,
    this.residenceAddress,
    this.graphics,
    this.healthStatus,
    this.overallAssessment,
    this.scars,
    this.fingerprint,
    this.associates,
    this.name,
    this.residenceSeparation,
    this.personInterest,
    this.nationality,
    this.height,
    this.tattoos,
    this.dna,
    this.notes,
    this.gender,
    this.politicalStatus,
    this.isKeyPersonnel,
    this.personType,
    this.weight,
    this.otherCharacteristics,
    this.voiceprint,
    this.birthDate,
    this.maritalStatus,
    this.serialNumber,
    this.strengths,
    this.skinColor,
    this.wechat,
    this.iris,
    this.education,
    this.formerName,
    this.wechatGroup,
    this.accent,
    this.alipay,
    this.criminalRecord,
  });

  factory List.fromJson(Map<String, dynamic> json) => List(
        villageId: json['village_id'] as int?,
        createTime: json['create_time'] as String?,
        belief: json['belief'] as String?,
        nickname: json['nickname'] as dynamic,
        riskAssessmentFamilyStability:
            json['risk_assessment_family_stability'] as dynamic,
        hairstyle: json['hairstyle'] as dynamic,
        qq: json['qq'] as dynamic,
        resume: json['resume'] as dynamic,
        id: json['id'] as int?,
        updateTime: json['update_time'] == null
            ? null
            : DateTime.parse(json['update_time'] as String),
        veteranStatus: json['veteran_status'] as String?,
        employer: json['employer'] as dynamic,
        socialSupportStatus: json['social_support_status'] as dynamic,
        faceShape: json['face_shape'] as dynamic,
        driverLicense: json['driver_license'] as dynamic,
        socialRelations: json['social_relations'] as dynamic,
        photo: json['photo'] as String?,
        telephone: json['telephone'] as dynamic,
        profession: json['profession'] as dynamic,
        personalActivityRange: json['personal_activity_range'] as dynamic,
        realPerformance: json['real_performance'] as dynamic,
        eyes: json['eyes'] as dynamic,
        passport: json['passport'] as dynamic,
        sourceIncome: json['source_income'] as dynamic,
        idNumber: json['id_number'] as String?,
        residenceAddress: json['residence_address'] as dynamic,
        graphics: json['graphics'] as dynamic,
        healthStatus: json['health_status'] as dynamic,
        overallAssessment: json['overall_assessment'] as dynamic,
        scars: json['scars'] as bool?,
        fingerprint: json['fingerprint'] as dynamic,
        associates: json['associates'] as dynamic,
        name: json['name'] as String?,
        residenceSeparation: json['residence_separation'] as bool?,
        personInterest: json['person_interest'] as String?,
        nationality: json['nationality'] as dynamic,
        height: json['height'] as dynamic,
        tattoos: json['tattoos'] as bool?,
        dna: json['dna'] as dynamic,
        notes: json['notes'] as dynamic,
        gender: json['gender'] as String?,
        politicalStatus: json['political_status'] as String?,
        isKeyPersonnel: json['is_key_personnel'] as bool?,
        personType: json['person_type'] as dynamic,
        weight: json['weight'] as dynamic,
        otherCharacteristics: json['other_characteristics'] as dynamic,
        voiceprint: json['voiceprint'] as dynamic,
        birthDate: json['birth_date'] as dynamic,
        maritalStatus: json['marital_status'] as String?,
        serialNumber: json['serial_number'] as String?,
        strengths: json['strengths'] as dynamic,
        skinColor: json['skin_color'] as dynamic,
        wechat: json['wechat'] as dynamic,
        iris: json['iris'] as dynamic,
        education: json['education'] as dynamic,
        formerName: json['former_name'] as dynamic,
        wechatGroup: json['wechat_group'] as bool?,
        accent: json['accent'] as dynamic,
        alipay: json['alipay'] as dynamic,
        criminalRecord: json['criminal_record'] as dynamic,
      );

  Map<String, dynamic> toJson() => {
        'village_id': villageId,
        'create_time': createTime,
        'belief': belief,
        'nickname': nickname,
        'risk_assessment_family_stability': riskAssessmentFamilyStability,
        'hairstyle': hairstyle,
        'qq': qq,
        'resume': resume,
        'id': id,
        'update_time': updateTime?.toIso8601String(),
        'veteran_status': veteranStatus,
        'employer': employer,
        'social_support_status': socialSupportStatus,
        'face_shape': faceShape,
        'driver_license': driverLicense,
        'social_relations': socialRelations,
        'photo': photo,
        'telephone': telephone,
        'profession': profession,
        'personal_activity_range': personalActivityRange,
        'real_performance': realPerformance,
        'eyes': eyes,
        'passport': passport,
        'source_income': sourceIncome,
        'id_number': idNumber,
        'residence_address': residenceAddress,
        'graphics': graphics,
        'health_status': healthStatus,
        'overall_assessment': overallAssessment,
        'scars': scars,
        'fingerprint': fingerprint,
        'associates': associates,
        'name': name,
        'residence_separation': residenceSeparation,
        'person_interest': personInterest,
        'nationality': nationality,
        'height': height,
        'tattoos': tattoos,
        'dna': dna,
        'notes': notes,
        'gender': gender,
        'political_status': politicalStatus,
        'is_key_personnel': isKeyPersonnel,
        'person_type': personType,
        'weight': weight,
        'other_characteristics': otherCharacteristics,
        'voiceprint': voiceprint,
        'birth_date': birthDate,
        'marital_status': maritalStatus,
        'serial_number': serialNumber,
        'strengths': strengths,
        'skin_color': skinColor,
        'wechat': wechat,
        'iris': iris,
        'education': education,
        'former_name': formerName,
        'wechat_group': wechatGroup,
        'accent': accent,
        'alipay': alipay,
        'criminal_record': criminalRecord,
      };
}
