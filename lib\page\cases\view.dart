import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/empty_widget.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/keep_alive_widget.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/turn_page.dart';
import 'package:police/page/cases/widgets/category.dart';
import 'package:police/page/cases/widgets/table_data.dart';
import 'package:police/page/cases/widgets/table_header.dart';
import 'package:police/page/peoples/widgets/category.dart';

import 'index.dart';
import 'widgets/float_button.dart';

class CasesPage extends GetView<CasesController> {
  const CasesPage({super.key});

  // 主视图
  Widget _buildView() {
    return Stack(
      children: [
        SingleChildScrollView(
          controller: controller.scrollController,
          child: Padding(
            padding: const EdgeInsets.only(top: 60),
            child: Column(
              children: [
                const CaseCategory(),
                const SizedBox(height: 10),
                controller.code == 0
                    ? const LoadingWidget()
                    : controller.code == 404
                        ? const Center(child: HttpErrorWidget())
                        : controller.code == 200 && controller.cases.isEmpty
                            ? const Center(child: EmptyWidget())
                            : Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  TableHeader(controller: controller),
                                  const TableData(),
                                  const SizedBox(height: 50),
                                  GetBuilder<CasesController>(
                                      id: CaseControllerIDS.turnpage,
                                      builder: (context) {
                                        return Visibility(
                                          visible: controller.cases.isNotEmpty,
                                          child: Align(
                                            alignment: Alignment.bottomCenter,
                                            child: TurnPage(
                                              itemCount: controller.maxpages,
                                              currentIndex:
                                                  controller.currentPage,
                                              onTap: (index, total) =>
                                                  controller.turnPage(
                                                      index, total),
                                            ),
                                          ),
                                        );
                                      }),
                                  const SizedBox(height: 50),
                                ],
                              ),
              ],
            ),
          ),
        ),
        FloatButton(controller: controller),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return KeepAliveWidget(
      child: GetBuilder<CasesController>(
          init: CasesController(),
          id: "cases",
          builder: (_) {
            return _buildView();
          }),
    );
  }
}
