import 'package:flutter/material.dart';
import 'package:police/common/widget/form_text.dart';

import '../../../common/widget/scale_animated_widget.dart';

class FactoryInfoTwo extends StatelessWidget {
  const FactoryInfoTwo({super.key});

  @override
  Widget build(BuildContext context) {
    return ScaleAnimatedWidget(
        child: InkWell(
      onTap: () {},
      child: Container(
        padding: const EdgeInsets.all(25),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/pic/234.png'),
            fit: BoxFit.fill,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.asset(
                'assets/pic/4162280569_048dd611ec_c.jpg',
              ),
            ),
            const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text(
                    "工厂名",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 25,
                      color: Color(0xf0e5edff),
                    ),
                  ),
                ),
                FormText(
                  labelText: '类型',
                  valueText: '宗教',
                  expandValue: true,
                  icon: Icons.sort,
                  maxLines: 2,
                  padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
                ),
                FormText(
                  labelText: '员工数',
                  valueText: '1000',
                  expandValue: true,
                  icon: Icons.people,
                  padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
                ),
                FormText(
                  labelText: '负责民警',
                  valueText: '啊水水',
                  expandValue: true,
                  icon: Icons.person,
                  padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
                ),
                FormText(
                  labelText: '电话',
                  valueText: '123245454564',
                  expandValue: true,
                  icon: Icons.phone_android_rounded,
                  padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
                ),
              ],
            ),
          ],
        ),
      ),
    ));
  }
}
