import 'alarms_statistic.dart';
import 'month_analyze.dart';
import 'village_list.dart';

class AnalyzeDetailsModel {
  List<VillageList>? villageList;
  List<AlarmsStatistic>? alarmsStatistics;
  List<MonthAnalyze>? monthAnalyze;

  AnalyzeDetailsModel({
    List<VillageList>? villageList,
    List<AlarmsStatistic>? alarmsStatistics,
    List<MonthAnalyze>? monthAnalyze,
  })  : villageList = villageList ?? <VillageList>[],
        alarmsStatistics = alarmsStatistics ?? <AlarmsStatistic>[],
        monthAnalyze = monthAnalyze ?? <MonthAnalyze>[];

  factory AnalyzeDetailsModel.fromJson(Map<String, dynamic> json) {
    return AnalyzeDetailsModel(
      villageList: (json['village_list'] as List<dynamic>?)
              ?.map((e) => VillageList.fromJson(e as Map<String, dynamic>))
              .toList() ??
          <VillageList>[],
      alarmsStatistics: (json['alarms_statistics'] as List<dynamic>?)
              ?.map((e) => AlarmsStatistic.fromJson(e as Map<String, dynamic>))
              .toList() ??
          <AlarmsStatistic>[],
      monthAnalyze: (json['monthly_frequency'] as List<dynamic>?)
              ?.map((e) => MonthAnalyze.fromJson(e as Map<String, dynamic>))
              .toList() ??
          <MonthAnalyze>[],
    );
  }

  Map<String, dynamic> toJson() => {
        'village_list': villageList?.map((e) => e.toJson()).toList(),
        'alarms_statistics': alarmsStatistics?.map((e) => e.toJson()).toList(),
        'monthly_frequency': monthAnalyze?.map((e) => e.toJson()).toList(),
      };
}
