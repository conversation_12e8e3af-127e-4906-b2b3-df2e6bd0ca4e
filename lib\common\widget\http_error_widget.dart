import 'package:flutter/material.dart';

class HttpErrorWidget extends StatelessWidget {
  const HttpErrorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // LottieAnimation('error.json', width: 150),
          Text(
            '网络错误',
            style: TextStyle(
              color: Color(0xd8cfe0ff),
              fontSize: 17,
            ),
          ),
        ],
      ),
    );
  }
}
