import 'package:flutter/material.dart';

class NormolButton extends StatelessWidget {
  const NormolButton({
    super.key,
    this.onTap,
    this.color = const Color(0xca073773),
    this.width = double.infinity,
    this.height,
    required this.lable,
  });

  final Function()? onTap;
  final String lable;
  final Color color;
  final double width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      child: MaterialButton(
        onPressed: () => onTap?.call(),
        color: color,
        height: height,
        padding: const EdgeInsets.symmetric(vertical: 17),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0), // 设置圆角半径
        ),
        child: Text(
          lable,
          style: const TextStyle(color: Colors.white, fontSize: 15),
        ),
      ),
    );
  }
}
