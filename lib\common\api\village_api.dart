import 'package:dio/dio.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/model/village_model/village_model.dart';
import 'package:police/common/util/https.dart';
import 'package:get/get.dart' as getx;
import 'package:police/page/village/controller.dart';

class VillageApi {
  /// 获取村庄列表
  static Future<List<VillageModel>> getVillageList(
      {Function()? fail, int size = 100}) async {
    final List<VillageModel> villages = [];

    Response response = await Https.get(
      Apis.village,
      queryParameters: {'size': size},
      fail: (results) => fail?.call(),
    );
    for (var item in response.data['items']) {
      villages.add(VillageModel.fromJson(item));
    }

    getx.Get.find<VillageController>().maxpages = response.data['pages'];

    return villages;
  }

  /// 获取村庄详情
  static Future<VillageModel> getVillageDetail(
    int id, {
    Function()? fail,
  }) async {
    Response response =
        await Https.get('${Apis.village}/$id', fail: (results) => fail?.call());

    return VillageModel.fromJson(response.data);
  }

  /// 创建村庄
  static Future<VillageModel> createVillage(
    Map<String, dynamic> data, {
    Function()? fail,
  }) async {
    Response response = await Https.post(
      Apis.village,
      data: data,
      pageBack: true,
      fail: (results) => fail?.call(),
    );

    return VillageModel.fromJson(response.data);
  }

  /// 修改村庄详情
  static Future<VillageModel> updateVillageDetail(
      int id, Map<String, dynamic> data,
      {Function()? fail}) async {
    Response response = await Https.patch(
      '${Apis.village}/$id',
      data: data,
      pageBack: true,
      fail: (results) => fail?.call(),
    );

    return VillageModel.fromJson(response.data);
  }

  /// 删除村庄
  static Future<void> deleteVillage(int id, {Function()? fail}) async {
    await Https.delete(
      '${Apis.village}/$id',
      pageBack: true,
      fail: (results) => fail?.call(),
    );
  }
}
