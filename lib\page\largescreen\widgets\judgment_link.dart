import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/page/analyze/controller.dart';
import 'package:police/page/analyze/index.dart';
import 'package:police/page/largescreen/index.dart';

class JudgmentLink extends StatelessWidget {
  const JudgmentLink({super.key});

  @override
  Widget build(BuildContext context) {
    List<Map<String, String>> links = [
      {'title': '每日警情', 'type': AnalyzeSortTypes.day},
      {'title': '每月警情', 'type': AnalyzeSortTypes.month},
      {'title': '每季警情', 'type': AnalyzeSortTypes.quarter},
      {'title': '全年警情', 'type': AnalyzeSortTypes.year},
    ];

    return Padding(
      padding: const EdgeInsets.only(bottom: 20, right: 30, left: 30),
      child: Column(
        children: [
          InkWell(
            onTap: () => jump2page(const AnalyzePage()),
            child: const Text(
              '警情研判',
              style: TextStyle(fontSize: 20, color: Color(0xfffad12d)),
            ),
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: links.map((e) {
              return InkWell(
                onTap: () => Get.find<LargescreenController>()
                    .getAnalyze(context, e['type']!),
                child: Text(
                  e['title']!,
                  style:
                      const TextStyle(fontSize: 20, color: Color(0xbeff4848)),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
