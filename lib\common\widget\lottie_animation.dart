import 'package:flutter/cupertino.dart';
import 'package:lottie/lottie.dart';

class LottieAnimation extends StatefulWidget {
  const LottieAnimation(
    this.name, {
    super.key,
    this.width,
    this.height,
    this.progress,
    this.duration = const Duration(milliseconds: 500),
  });

  final String name;
  final double? progress;
  final double? width;
  final double? height;
  final Duration duration;

  @override
  State<LottieAnimation> createState() => _LottieAnimationState();
}

class _LottieAnimationState extends State<LottieAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);
    if (widget.progress == null) {
      _controller.repeat();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.progress != null) {
      _controller.animateTo(widget.progress!);
    }
    return Lottie.asset(
      'assets/animations/${widget.name}',
      controller: _controller,
      width: widget.width,
      height: widget.height,
    );
  }
}
