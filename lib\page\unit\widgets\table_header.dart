import 'package:flutter/material.dart';
import 'package:police/page/unit/index.dart';

class UnitTableHeader extends StatelessWidget {
  const UnitTableHeader({super.key, required this.controller});

  final UnitController controller;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(controller.tableHeader.length, (index) {
        return Expanded(
          child: Container(
            color: const Color(0xd3041e3e),
            alignment: Alignment.center,
            margin: const EdgeInsets.all(.5),
            // padding: const EdgeInsets.symmetric(vertical: 15),
            height: 70,
            child: Text(
              controller.tableHeader[index],
              style: const TextStyle(
                  fontSize: 17,
                  fontWeight: FontWeight.bold,
                  color: Color(0xf0e5edff)),
            ),
          ),
        );
      }),
    );
  }
}
