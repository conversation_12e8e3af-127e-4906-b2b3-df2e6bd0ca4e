import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

class WelcomeText extends StatelessWidget {
  const WelcomeText({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: 3,
      child: Padding(
        padding: const EdgeInsets.all(30.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Spacer(
              flex: 1,
            ),
            Image.asset(
              'assets/pic/logo_001.png',
              width: 300,
            ),
            const SizedBox(height: 20),
            const AutoSizeText(
              '综合指挥室指挥系统',
              maxLines: 1,
              style: TextStyle(
                color: Color(0xc2ffffff),
                fontSize: 80,
                fontWeight: FontWeight.w800,
                shadows: [
                  Shadow(
                    offset: Offset(2.0, 2.0),
                    blurRadius: 30,
                    color: Color.fromARGB(128, 0, 0, 0),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            const AutoSizeText(
              '云冈派出所',
              maxLines: 1,
              style: TextStyle(
                color: Color(0xc2ffffff),
                fontSize: 50,
                fontWeight: FontWeight.w500,
                shadows: [
                  Shadow(
                    offset: Offset(2.0, 2.0),
                    blurRadius: 30,
                    color: Color.fromARGB(128, 0, 0, 0),
                  ),
                ],
              ),
            ),
            const Spacer(flex: 3),
          ],
        ),
        // child: AnimatedTextKit(
        //   totalRepeatCount: 1,
        //   isRepeatingAnimation: false,
        //   animatedTexts: [
        //     RotateAnimatedText(
        //       'Fade First',
        //       textStyle: TextStyle(
        //           fontSize: 32.0,
        //           fontWeight: FontWeight.bold,
        //           color: Colors.white),
        //     ),
        //     RotateAnimatedText(
        //       'Then Scale',
        //       textStyle: TextStyle(fontSize: 70.0, color: Colors.white),
        //     ),
        //   ],
        // ),
      ),
    );
  }
}
