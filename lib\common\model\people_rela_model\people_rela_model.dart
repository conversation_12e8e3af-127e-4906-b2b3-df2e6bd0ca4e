import 'package:police/common/model/house_model.dart';
import 'package:police/common/model/people_detail_model.dart';

class PeopleRelaModel {
  int? id;
  int? householdId;
  int? personId;
  String? relationName;
  DateTime? createTime;
  HouseModel? household;
  PeopleDetailModel? person;

  PeopleRelaModel({
    this.id,
    this.householdId,
    this.personId,
    this.relationName,
    this.createTime,
    this.household,
    this.person,
  });

  factory PeopleRelaModel.fromJson(Map<String, dynamic> json) {
    return PeopleRelaModel(
      id: json['id'] as int?,
      householdId: json['household_id'] as int?,
      personId: json['person_id'] as int?,
      relationName: json['relation_name'] as String?,
      createTime: json['create_time'] == null
          ? null
          : DateTime.parse(json['create_time'] as String),
      household: json['household'] == null
          ? null
          : HouseModel.fromJson(json['household'] as Map<String, dynamic>),
      person: json['person'] == null
          ? null
          : PeopleDetailModel.fromJson(json['person'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'household_id': householdId,
        'person_id': personId,
        'relation_name': relationName,
        'create_time': createTime?.toIso8601String(),
        'household': household?.toJson(),
        'person': person?.toJson(),
      };
}
