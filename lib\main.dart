import 'dart:async';
import 'dart:io';

import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/write_error_to_file.dart';
import 'package:police/page/main/controller.dart';
import 'package:police/page/user/controller.dart';

import 'page/splash/view.dart';
// import 'package:window_manager/window_manager.dart';

void main() async {
  // 设置debugZoneErrorsAreFatal为true，以便在区域错误时抛出异常
  BindingBase.debugZoneErrorsAreFatal = true;

  runZonedGuarded(
    () {
      WidgetsFlutterBinding.ensureInitialized();
      Get.put(MainController());
      Get.put(UserController());
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        doWhenWindowReady(() async {
          const initialSize = Size(1280, 800);
          appWindow.minSize = initialSize;
          appWindow.size = initialSize;
          // appWindow.alignment = Alignment.center;
          appWindow.show();
        });
      }
      runApp(const MyApp());
    },
    (error, stackTrace) => writeErrorToFile(error, stackTrace),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    return OKToast(
      child: GetMaterialApp(
        title: '综合指挥室指挥系统',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          // platform: TargetPlatform.iOS,
          highlightColor: Colors.transparent, // 高亮颜色
          splashColor: Colors.transparent, // 水波纹颜色
          appBarTheme: const AppBarTheme(
            backgroundColor: Colors.transparent, // 导航栏背景颜色
            iconTheme: IconThemeData(
              color: Color(0xf0e5edff), // 导航栏图标颜色
            ),
          ),
          scaffoldBackgroundColor: Colors.transparent, // 背景颜色
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue,
          ).copyWith(
            primary: const Color(0xd8cfe0ff),
            onPrimary: Colors.black,
          ),
          useMaterial3: true,
          scrollbarTheme: ScrollbarThemeData(
            thumbColor: MaterialStateColor.resolveWith(
                (states) => Colors.blue), // scrollBar 颜色
            // thumbColor: WidgetStateProperty.all(Colors.blue), // scrollBar 颜色
          ),
        ),
        darkTheme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue,
          ).copyWith(
            primary: const Color(0xd8cfe0ff),
            onPrimary: Colors.black,
          ),
        ),
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('zh', 'CN')],
        locale: const Locale('zh', 'CN'),
        home: const SplashPage(),

        // home: const MainPage(),
      ),
    );
  }
}
