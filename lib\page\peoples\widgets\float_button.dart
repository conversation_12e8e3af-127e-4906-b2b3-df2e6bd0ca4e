import 'package:flutter/material.dart';
import 'package:police/common/model/people_detail_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/page/peopledetail/index.dart';
import 'package:police/page/peoples/index.dart';

import '../../../common/widget/float_square_button.dart';

class FloatButton extends StatelessWidget {
  const FloatButton({
    super.key,
    required this.controller,
    this.fromFocus = false,
  });

  final PeoplesController controller;
  final bool fromFocus;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.all(36),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FloatSquareButton(
              onTap: () => controller.getPeopleList(
                page: fromFocus
                    ? controller.fromFoucsCurrentPage
                    : controller.currentPage,
                fromFoucs: fromFocus,
              ),
              icon: Icons.refresh,
            ),
            const SizedBox(height: 10),
            if (!fromFocus)
              FloatSquareButton(
                onTap: () => jump2page(
                  PeopledetailPage(PeopleDetailModel()),
                  onBack: (results) {
                    if (results.id != null) {
                      controller.peoples.insert(0, results);
                      controller.update([PeoplesControllerIDS.peopleList]);
                    }
                  },
                ),
                icon: Icons.add,
              ),
          ],
        ),
      ),
    );
  }
}
