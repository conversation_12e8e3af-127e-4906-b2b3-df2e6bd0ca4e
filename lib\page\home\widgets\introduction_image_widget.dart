import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controller.dart';

class IntroductionImageWidget extends StatelessWidget {
  const IntroductionImageWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        children: [
          Container(
            // key: ,
            // width: constraints.maxWidth * .9,
            height: Get.height - 60,
            padding: const EdgeInsets.symmetric(vertical: 50),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.asset(
                'assets/pic/IMG_8858.JPG',
                fit: BoxFit.cover,
              ),
            ),
          )
        ],
      );
    });
  }
}
