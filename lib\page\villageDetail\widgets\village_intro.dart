import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/model/village_model/statistics.dart';
import 'package:police/common/model/village_model/village_model.dart';
import 'package:police/common/util/image_picker.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/common/widget/image_upload.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/page/villageDetail/index.dart';

class VillageIntro extends StatelessWidget {
  const VillageIntro({super.key});

  @override
  Widget build(BuildContext context) {
    VillageModel villageDetail =
        Get.find<VillagedetailController>().villageDetail;
    List<Statistics> statistics = villageDetail.statistics ?? [];

    for (var item in villageDetail.statisticsType ?? []) {
      statistics.add(Statistics(name: item.name, count: item.count));
    }

    return Column(
      children: [
        SizedBox(
          key: Get.find<VillagedetailController>().intro,
          width: double.infinity,
          height: 600,
          child: Stack(
            fit: StackFit.expand,
            children: [
              // _buildVillageImage(villageDetail),
              ImageUploadWidget(
                photoUrl: villageDetail.cover ?? '',
                onUploaded: (url) {
                  villageDetail.cover = url;
                },
              ),

              Positioned(
                bottom: 20,
                left: 20,
                right: 20,
                child: Container(
                  padding: const EdgeInsets.all(10),
                  color: Colors.black54,
                  child: Wrap(
                    // mainAxisAlignment: MainAxisAlignment.center,
                    alignment: WrapAlignment.center,
                    children: List.generate(
                        villageDetail.statistics!.length + 1, (index) {
                      late Statistics statistics;
                      if (index > 0) {
                        statistics = villageDetail.statistics![index - 1];
                      }
                      return index == 0
                          ? Text(
                              '人口总数： ${villageDetail.personCount}    ',
                              style: const TextStyle(
                                fontSize: 15,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            )
                          : Text(
                              '${statistics.name}： ${statistics.count}    ',
                              style: const TextStyle(
                                fontSize: 15,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            );
                    }),
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.fromLTRB(70, 0, 70, 30),
          decoration: const BoxDecoration(
            color: Color(0xca073773),
            borderRadius: BorderRadius.all(Radius.circular(4)),
          ),
          child: Column(
            children: [
              InputText(
                defaultText: villageDetail.name,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 100,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  shadows: [
                    Shadow(
                      blurRadius: 10.0,
                      color: Colors.black,
                      offset: Offset(5.0, 5.0),
                    ),
                  ],
                ),
                onChanged: (text) {
                  villageDetail.name = text;
                },
              ),
              const SizedBox(height: 30),
              InputText(
                defaultText: villageDetail.graphics,
                maxLines: null,
                textInputAction: TextInputAction.newline,
                onChanged: (text) {
                  villageDetail.graphics = text;
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVillageImage(VillageModel villageDetail) {
    String photo = '';
    return InkWell(
      onTap: () async {
        XFile? file = await imagePicker();
        if (file != null) {
          photo = file.path;
          Get.find<VillagedetailController>()
              .update([VillageDetailControllerIDS.villagePhoto]);
        }
      },
      child: GetBuilder<VillagedetailController>(
          id: VillageDetailControllerIDS.villagePhoto,
          builder: (contro) {
            return villageDetail.cover != null && photo == ''
                ? ImageWidget.network(
                    villageDetail.cover ?? '',
                    fit: BoxFit.cover,
                  )
                : ImageUploadWidget(
                    photoUrl: photo,
                    onUploaded: (url) {
                      villageDetail.cover = url;
                      Get.find<VillagedetailController>()
                          .update([VillageDetailControllerIDS.villagePhoto]);
                    },
                  );
          }),
    );
  }
}
