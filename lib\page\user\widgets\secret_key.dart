import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/secret_key.dart';
import 'package:police/common/util/shared.dart';
import 'package:police/common/widget/input_text.dart';
import '../../../common/widget/normol_button.dart';

class Secret<PERSON>ey extends StatelessWidget {
  const SecretKey({super.key, required this.keyError});

  final bool keyError;

  @override
  Widget build(BuildContext context) {
    TextEditingController key = TextEditingController(text: secretKey);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '密钥',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xd8cfe0ff),
          ),
        ),
        const SizedBox(height: 10),
        InputText(
          controller: key,
          obscureText: true,
        ),
        const SizedBox(height: 10),
        Normo<PERSON><PERSON>utton(
          lable: '确定更改',
          width: 300,
          onTap: () async {
            try {
              await SharedUtil.setString('keyStr', key.text.trim());
              showToast('更改成功');
            } catch (e) {
              showToast('更改失败');
            }
          },
        ),
      ],
    );
  }
}
