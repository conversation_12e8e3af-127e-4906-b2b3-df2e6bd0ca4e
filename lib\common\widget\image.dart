import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:police/common/widget/lottie_animation.dart';

import '../api/apis.dart';

class ImageWidget {
  static Widget network(
    String url, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    BorderRadius borderRadius = BorderRadius.zero,
    Alignment alignment = Alignment.center,
    Function()? onTap,
    bool addhost = true,
  }) {
    return InkWell(
      onTap: onTap,
      child: ClipRRect(
        borderRadius: borderRadius,
        child: CachedNetworkImage(
          imageUrl: addhost ? '${Apis.baseUrl}/assets/$url' : url,
          fit: fit,
          width: width,
          height: height,
          alignment: alignment,
          placeholder: (context, url) => _loadingBuilder(width, height),
          errorWidget: (context, url, error) => _errorBuilder(width, height),
        ),
      ),
    );
  }

  static Widget asset(
    String path, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    Alignment alignment = Alignment.center,
    Function()? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Image.asset(
        path,
        fit: fit,
        width: width,
        height: height,
        alignment: alignment,
      ),
    );
  }

  static Widget file(
    String path, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    Alignment alignment = Alignment.center,
    Function()? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Image.file(
        File(path),
        fit: fit,
        width: width,
        height: height,
        alignment: alignment,
      ),
    );
  }

  static Widget _errorBuilder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey,
      child: const Center(
        child: Icon(Icons.error),
      ),
    );
  }

  static Widget _loadingBuilder(
    double? width,
    double? height,
  ) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey,
      child: const Center(
        child: LottieAnimation(
          'circular_loading.json',
          duration: Duration(seconds: 3),
        ),
      ),
    );
  }
}
