import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/house_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/page/house/index.dart';
import 'package:police/page/housedetail/index.dart';

class HouseTableData extends StatelessWidget {
  const HouseTableData({super.key, required this.unitId});

  final int unitId;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HouseController>(
      id: HouseControllerIDS.housesItem,
      builder: (controller) {
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: controller.houses.length,
          itemBuilder: (_, index) {
            return InkWell(
              onTap: () {
                jump2page(
                  HousedetailPage(
                      detail: controller.houses[index], unitId: unitId),
                  onBack: (result) {
                    if (result != null) {
                      if (result == 'remove') {
                        controller.houses.removeAt(index);
                      } else {
                        controller.houses[index] = result;
                      }
                      controller.update([HouseControllerIDS.housesItem]);
                    }
                  },
                );
              },
              child: Row(
                children: List.generate(controller.tableHeaders.length, (i) {
                  return Expanded(
                    flex: i == 4 ? 3 : 1,
                    child: Container(
                      height: 50,
                      color: index % 2 == 1
                          ? const Color(0xd3021a37)
                          : const Color(0xd804254c),
                      alignment: Alignment.center,
                      margin: const EdgeInsets.all(.5),
                      child: Text(
                        _setDefaultText(i, controller, index),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                            color: Color(0xf0e5edff), fontSize: 16),
                      ),
                    ),
                  );
                }),
              ),
            );
          },
        );
      },
    );
  }

  String _setDefaultText(int i, HouseController controller, int index) {
    HouseModel item = controller.houses[index];
    switch (i) {
      case 0:
        return item.propertyOwner ?? '';
      case 1:
        return item.propertyTelephone ?? '';
      case 2:
        return item.housingPurposes;
      case 3:
        return item.numberResidents == null
            ? ''
            : item.numberResidents.toString();
      default:
        return item.address ?? '';
    }
  }
}
