import 'package:flutter/material.dart';

class ScaleAnimatedWidget extends StatefulWidget {
  const ScaleAnimatedWidget({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 150),
    this.begin = 1.0,
    this.end = 1.03,
    this.userMouse = true,
  });

  final Widget child;
  final Duration? duration;
  final double? begin;
  final double? end;
  final bool userMouse;

  @override
  State<ScaleAnimatedWidget> createState() => _ScaleAnimatedWidgetState();
}

class _ScaleAnimatedWidgetState extends State<ScaleAnimatedWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);

    _animation = Tween<double>(begin: widget.begin, end: widget.end)
        .animate(_controller);

    if (!widget.userMouse) _controller.forward();
  }

  @override
  void dispose() {
    if (!widget.userMouse) _controller.reverse();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (event) {
        if (widget.userMouse) _controller.forward();
      },
      onExit: (event) {
        if (widget.userMouse) _controller.reverse();
      },
      child: ScaleTransition(
        scale: _animation,
        child: widget.child,
      ),
    );
  }
}
