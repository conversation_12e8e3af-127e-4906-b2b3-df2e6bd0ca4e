import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InputText extends StatelessWidget {
  const InputText({
    super.key,
    this.controller,
    this.lableText,
    this.width,
    this.height,
    this.boxDecoration,
    this.inputBorder = const OutlineInputBorder(),
    this.enabledBorder,
    this.focusedBorder,
    this.fillColor,
    this.fontColor = Colors.white,
    this.fontSize = 16,
    this.labelStyle,
    this.canEdit,
    this.defaultText,
    this.onChanged,
    this.obscureText = false,
    this.maxLength,
    this.onEditingComplete,
    this.maxLines = 1,
    this.minLines = 1,
    this.style,
    this.textAlign = TextAlign.start,
    this.hint,
    this.onTap,
    this.focusNode,
    this.inputFormatters,
    this.onRemoveFocus,
    this.isDense = true,
    this.onTapUp,
    this.autofocus = false,
    this.textInputAction = TextInputAction.next,
    this.hintStyle,
    this.rightClickMenu = true,
  });

  final TextEditingController? controller;
  final String? lableText;
  final Color? fontColor;
  final double? fontSize;
  final double? width;
  final double? height;
  final BoxDecoration? boxDecoration;
  final InputBorder? inputBorder;
  final InputBorder? enabledBorder;
  final InputBorder? focusedBorder;
  final Color? fillColor;
  final TextStyle? labelStyle;
  final bool? canEdit;
  final String? defaultText;
  final Function(String text)? onChanged;
  final Function(String text)? onEditingComplete;
  final bool obscureText;
  final int? maxLength;
  final int? maxLines;
  final int minLines;
  // final List<Shadow>? shadows;
  final TextStyle? style;
  final TextAlign textAlign;
  final String? hint;
  final Function()? onTap;
  final FocusNode? focusNode;
  final List<TextInputFormatter>? inputFormatters;
  final Function(String text)? onRemoveFocus;
  final bool isDense;
  final Function(TapUpDetails details)? onTapUp;
  final bool autofocus;
  final TextInputAction textInputAction;
  final TextStyle? hintStyle;
  final bool rightClickMenu;

  @override
  Widget build(BuildContext context) {
    focusNode?.addListener(() {
      if (!focusNode!.hasFocus) {
        onRemoveFocus?.call(controller!.text.trim());
      }
    });

    return InkWell(
      onTap: onTap,
      onTapUp: onTapUp,
      child: Container(
        decoration: boxDecoration,
        width: width,
        height: height,
        child: TextField(
          controller: controller ??
              TextEditingController.fromValue(
                  TextEditingValue(text: defaultText ?? '')),
          focusNode: focusNode,
          autofocus: autofocus,
          contextMenuBuilder:
              (BuildContext context, EditableTextState editableTextState) {
            return rightClickMenu
                ? AdaptiveTextSelectionToolbar.editableText(
                    editableTextState: editableTextState,
                  )
                : const SizedBox.shrink(); // 返回一个空的 SizedBox 来禁用右键菜单
          },
          style: style ??
              TextStyle(
                color: fontColor, // 设置输入文本的颜色
                fontSize: fontSize, // 设置输入文本的字体大小
              ),
          enabled: canEdit,
          obscureText: obscureText,
          maxLength: maxLength,
          maxLines: maxLines,
          minLines: minLines,
          obscuringCharacter: '*',
          textAlign: textAlign,
          inputFormatters: inputFormatters,
          textInputAction: textInputAction,
          decoration: InputDecoration(
            // filled: true,
            // fillColor: fillColor, // 设置输入框的背景颜色
            border: InputBorder.none,
            enabledBorder: enabledBorder,
            labelText: lableText,
            labelStyle: labelStyle,
            focusedBorder: focusedBorder,
            counterText: '',
            hintText: hint,
            isDense: isDense,
            hintStyle: hintStyle ??
                TextStyle(
                  color: Colors.grey, // 设置提示文本的颜色
                  fontSize: fontSize, // 设置提示文本的字体大小
                ),
          ),
          onChanged: (value) => onChanged?.call(value.trim()),
          onEditingComplete: () =>
              onEditingComplete?.call(controller!.text.trim()),
        ),
      ),
    );
  }
}
