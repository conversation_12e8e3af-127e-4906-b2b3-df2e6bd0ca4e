import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/model/user_info_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/page/login/controller.dart';
import 'package:police/page/main/controller.dart';
import 'package:police/page/user/index.dart';

class UserInfo extends StatelessWidget {
  const UserInfo({super.key, required this.controller});

  final MainController controller;

  @override
  Widget build(BuildContext context) {
    UserInfoModel userInfo = Get.find<UserController>().userInfo;
    return InkWell(
      onTap: () => jump2page(const UserPage()),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ClipOval(
            child: ImageWidget.network(
              userInfo.avatar ?? '',
              width: 40,
              height: 40,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: 10),
          Text(
            userInfo.username ?? '',
            style: const TextStyle(color: Color(0xd8cfe0ff)),
          ),
        ],
      ),
    );
  }
}
