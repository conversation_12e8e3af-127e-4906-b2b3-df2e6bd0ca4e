import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/page/home/<USER>';

class AboutText extends StatelessWidget {
  const AboutText({super.key});

  @override
  Widget build(BuildContext context) {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        AutoSizeText(
          '综合指挥室指挥系统',
          maxLines: 1,
          style: TextStyle(
            color: Color(0xd8cfe0ff),
            fontSize: 100,
            fontWeight: FontWeight.w800,
            shadows: [
              Shadow(
                offset: Offset(2.0, 2.0),
                blurRadius: 30,
                color: Color.fromARGB(128, 0, 0, 0),
              ),
            ],
          ),
        ),
        // SizedBox(height: 20),
        AutoSizeText(
          '云冈派出所',
          maxLines: 1,
          style: TextStyle(
            color: Color(0xd8cfe0ff),
            fontSize: 70,
            fontWeight: FontWeight.w500,
            shadows: [
              Shadow(
                offset: Offset(2.0, 2.0),
                blurRadius: 30,
                color: Color.fromARGB(128, 0, 0, 0),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
