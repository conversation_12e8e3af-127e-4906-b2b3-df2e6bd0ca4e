import 'package:flutter/material.dart';

class EmptyWidget extends StatelessWidget {
  const EmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // LottieAnimation('empty.json', width: 150),
          Text(
            '暂无数据',
            style: TextStyle(
              color: Color(0xd8cfe0ff),
              fontSize: 17,
            ),
          ),
        ],
      ),
    );
  }
}
