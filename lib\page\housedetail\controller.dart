import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/house_api.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/routes/jump_page.dart';

import '../../common/model/house_model.dart';
import '../../common/model/people_rela_model/people_rela_model.dart';

class HousedetailIDS {
  static String housedetail = 'housedetail';
  static String houseInfo = 'house_info';
  static String otherInfo = 'other_info';
  static String safeInfo = 'safe_info';
  static String focusPeople = 'foucs_people';
  static String feedback = 'feedback';
  static String peoples = 'peoples';
}

class HousedetailController extends GetxController {
  int code = 0;
  List<PeopleRelaModel> oldPeoples = [];
  List<PeopleRelaModel> peoples = [];
  HouseModel house = HouseModel();

  /// 获取家庭关系
  getPeopleRela(int houseId) async {
    code = 0;
    update([HousedetailIDS.peoples]);
    peoples = await HouseApi.getHouseRelationFromHouse(
      houseId: houseId,
      fail: (result) {
        code = 404;
        update([HousedetailIDS.peoples]);
      },
    );

    oldPeoples = peoples
        .map((e) =>
            PeopleRelaModel.fromJson(json.decode(json.encode(e.toJson()))))
        .toList();

    code = 200;
    update([HousedetailIDS.peoples]);
  }

  /// 获取家庭信息
  Future<HouseModel> getHouseInfo(int houseId,
      {required Function(dynamic) fail}) async {
    house = await HouseApi.getHouseDetail(
      houseId,
      fail: (result) => fail(result),
    );

    return house;
  }

  /// 创建/修改家庭
  Future updateHouseDetail(BuildContext context,
      {int? houseId, required HouseModel detail, required int unitId}) async {
    Dialogs.showLoadingDialog(context);
    if (houseId == null) {
      detail.villageUnitId = unitId;
      house = await HouseApi.createHouse(
        detail,
        fail: (p0) {
          return;
        },
      );
    } else {
      house = await HouseApi.patchHouse(
        id: houseId,
        house: detail,
        fail: (p0) {
          return;
        },
      );
    }
    if (peoples.isNotEmpty) {
      await savePeople(context, house.id!); // 使用 house.id 而不是 houseId
    }
    update([HousedetailIDS.housedetail]);
  }

  /// 删除家庭
  deleteHouse(BuildContext context, int houseId, {String? name}) async {
    Dialogs.showConfirmDialog(
      context,
      title: '是否确认删除',
      subTitle: ' $name ',
      content: '删除后无法恢复！',
      onConfirm: () async {
        Dialogs.showLoadingDialog(context);
        await HouseApi.deleteHouse(houseId);
        pageback(result: 'remove');
        showToast('删除成功');
      },
    );
  }

  /// 保存人员关系
  savePeople(BuildContext context, int houseId) async {
    try {
      // 用于存储需要更新的关系数据
      List<Map<String, dynamic>> updatedPeoples = [];

      for (var people in peoples) {
        if (people.id == null) {
          Dialogs.showLoadingDialog(context);
          // 如果人员关系是新的，立即保存
          final result = await HouseApi.createHouseRelation(
            houseId: houseId,
            peopelId: people.person!.id!,
            title: people.relationName ?? '',
            fail: (p0) {
              peoples.remove(people);
              update([HousedetailIDS.peoples]);
            },
          );
          // 保存成功后，更新ID
          people.id = result.id;
        } else {
          // 如果人员关系已存在，则将其加入到更新列表中
          updatedPeoples.add({
            "relation_name": people.relationName,
            'person_id': people.person!.id!,
          });
        }
      }

      // 更新已经存在的关系
      if (updatedPeoples.isNotEmpty) {
        Dialogs.showLoadingDialog(context);
        for (var i = 0; i < updatedPeoples.length; i++) {
          await HouseApi.patchHouseRelation(
            relaId: peoples[i].id!, // 使用当前关系的ID进行更新
            data: updatedPeoples[i],
          );
        }
        pageback();
      }

      // 更新旧的人员列表
      oldPeoples = peoples
          .map((e) =>
              PeopleRelaModel.fromJson(json.decode(json.encode(e.toJson()))))
          .toList();

      showToast('保存成功');
    } catch (e) {
      // showToast('保存失败');
    }
  }

  /// 新增人员关系
  addPeople(BuildContext context, int? houseId) {
    Dialogs.showPickerPersonDialog(
      context,
      onSelected: (result) async {
        pageback();
        PeopleRelaModel person = PeopleRelaModel(person: result);
        peoples.add(person);
        update([HousedetailIDS.peoples]);

        if (houseId != null) {
          // 立即保存新添加的人员关系
          await savePeople(context, houseId);
        }
      },
    );
  }

  /// 删除人员关系
  deletePeople(BuildContext context,
      {required int index, int? peopleId, String? name}) async {
    if (peopleId != null) {
      Dialogs.showConfirmDialog(
        context,
        title: '是否确认删除',
        subTitle: ' $name',
        content: '删除后无法恢复！',
        onConfirm: () async {
          Dialogs.showLoadingDialog(context);
          await HouseApi.deleteHouseRelation(peopleId);
          showToast('删除成功');
          peoples.removeAt(index);
          update([HousedetailIDS.peoples]);
        },
      );
    } else {
      peoples.removeAt(index);
      update([HousedetailIDS.peoples]);
    }
  }
}
