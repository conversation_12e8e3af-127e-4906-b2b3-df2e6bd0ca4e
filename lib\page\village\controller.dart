import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/model/village_model/village_model.dart';
import 'package:police/common/routes/jump_page.dart';

import '../../common/api/village_api.dart';

class VillageControllerIDS {
  static const String village = 'village';
}

class VillageController extends GetxController {
  int code = 0;
  int maxpages = 1;
  int currentpage = 1;

  List<VillageModel> villages = [];

  /// 获取村庄列表
  Future getVillages() async {
    code = 0;
    update([VillageControllerIDS.village]);

    villages = await VillageApi.getVillageList(
      fail: () {
        code = 404;
        update([VillageControllerIDS.village]);
      },
    );

    code = 200;
    update([VillageControllerIDS.village]);
  }

  /// 删除村庄
  deleteVillage(BuildContext context,
      {required int id, required String name, required int index}) {
    Dialogs.showConfirmDialog(
      context,
      title: '是否确认删除',
      subTitle: ' $name',
      content: '删除后将不可恢复！',
      onConfirm: () async {
        pageback();
        Dialogs.showLoadingDialog(context);
        await VillageApi.deleteVillage(id);
        villages.removeAt(index);
        update([VillageControllerIDS.village]);
      },
    );
  }
}
