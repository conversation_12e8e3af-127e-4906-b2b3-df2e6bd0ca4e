import 'package:shared_preferences/shared_preferences.dart';

class SharedUtil {
  static late SharedPreferences _shared;
  static _init() async {
    _shared = await SharedPreferences.getInstance();
  }

  static Future setString(String key, String value) async {
    await _init();
    _shared.setString(key, value);
  }

  static Future getString(String key) async {
    await _init();
    return _shared.getString(key);
  }
}
