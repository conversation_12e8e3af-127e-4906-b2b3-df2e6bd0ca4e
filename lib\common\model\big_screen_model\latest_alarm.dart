class LatestAlarm {
  int? villageId;
  String? types;
  DateTime? occurrenceTime;
  String? graphics;
  String? sort;
  int? id;
  String? title;

  LatestAlarm({
    this.villageId,
    this.types,
    this.occurrenceTime,
    this.graphics,
    this.sort,
    this.id,
    this.title,
  });

  factory LatestAlarm.fromJson(Map<String, dynamic> json) => LatestAlarm(
        villageId: json['village_id'] as int?,
        types: json['types'] as String?,
        occurrenceTime: json['occurrence_time'] == null
            ? null
            : DateTime.parse(json['occurrence_time'] as String),
        graphics: json['graphics'] as String?,
        sort: json['sort'] as String?,
        id: json['id'] as int?,
        title: json['title'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'village_id': villageId,
        'types': types,
        'occurrence_time': occurrenceTime?.toIso8601String(),
        'graphics': graphics,
        'sort': sort,
        'id': id,
        'title': title,
      };
}
