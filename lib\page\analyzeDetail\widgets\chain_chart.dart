import 'package:fl_chart/fl_chart.dart' as fl_charts;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../common/model/analyze_details_model/alarms_statistic.dart';
import '../../../common/model/analyze_details_model/sort.dart';
import '../../../common/widget/corner_card.dart';
import '../controller.dart';

class ChainChart extends StatelessWidget {
  const ChainChart({super.key, required this.controller});

  final AnalyzedetailController controller;

  @override
  Widget build(BuildContext context) {
    List<AlarmsStatistic> datas = controller.details.alarmsStatistics!;
    List<AlarmsStatistic> chain = controller.chain.value.alarmsStatistics!;

    return CornerCard(
      height: 300,
      child: Column(
        children: [
          Text(
            '警情环比',
            style: const TextStyle(fontSize: 25, color: Colors.white),
          ),
          Expanded(
            child: fl_charts.BarChart(
              fl_charts.BarChartData(
                // maxY: datas[j].alarmCount.toDouble(),
                barGroups: _buildBarGroups(datas, chain),
                borderData: fl_charts.FlBorderData(show: false),
                gridData: const fl_charts.FlGridData(show: false),
                titlesData: _buildBarChartTitles(datas),
                barTouchData: _buildBarChartTouthData(),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// Tooltip 样式
  fl_charts.BarTouchData _buildBarChartTouthData() {
    return fl_charts.BarTouchData(
      touchTooltipData: fl_charts.BarTouchTooltipData(
        getTooltipItem: (group, groupIndex, rod, rodIndex) =>
            fl_charts.BarTooltipItem(
          rod.toY.round().toString(),
          const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        getTooltipColor: (group) => Colors.transparent,
        tooltipPadding: EdgeInsets.zero,
        tooltipMargin: 8,
      ),
    );
  }

  /// 标题样式
  fl_charts.FlTitlesData _buildBarChartTitles(List<AlarmsStatistic> sort) {
    return fl_charts.FlTitlesData(
        leftTitles: _hideTitles(),
        rightTitles: _hideTitles(),
        topTitles: _hideTitles(),
        bottomTitles: fl_charts.AxisTitles(
          sideTitles: fl_charts.SideTitles(
            showTitles: true,
            getTitlesWidget: (value, meta) {
              return fl_charts.SideTitleWidget(
                axisSide: meta.axisSide,
                child: Text(sort[value.toInt()].name ?? '',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    )),
              );
            },
          ),
        ));
  }

  /// 柱状图样式
  List<fl_charts.BarChartGroupData> _buildBarGroups(
      List<AlarmsStatistic> sort, List<AlarmsStatistic> chain) {
    return List.generate(sort.length, (index) {
      return fl_charts.BarChartGroupData(
        x: index,
        barRods: [
          fl_charts.BarChartRodData(
            toY: sort[index].alarmCount.toDouble(),
            color: Colors.blue,
            // width: 13,
            // backDrawRodData: fl_charts
            //     .BackgroundBarChartRodData(
            //   show: true,
            //   toY: datas[j]
            //       .alarmCount
            //       .toDouble(),
            //   color:
            //       Colors.white.withOpacity(.3),
            // ),
          ),
          fl_charts.BarChartRodData(
            toY: chain[index].alarmCount.toDouble(),
            color: Colors.cyan,
          ),
        ],
      );
    });
  }

  /// 隐藏标题
  fl_charts.AxisTitles _hideTitles() {
    return const fl_charts.AxisTitles(
      sideTitles: fl_charts.SideTitles(
        showTitles: false,
      ),
    );
  }
}
