import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/common/widget/scale_animated_widget.dart';
import 'package:police/page/peopledetail/index.dart';
import 'package:police/page/peoples/controller.dart';
import '../../../common/model/people_detail_model.dart';

class PersonInfoTwo extends StatelessWidget {
  const PersonInfoTwo(
      {super.key, required this.peopleInfo, required this.controller});

  final PeopleDetailModel peopleInfo;
  final PeoplesController controller;

  @override
  Widget build(BuildContext context) {
    return ScaleAnimatedWidget(
      child: InkWell(
        onTap: () => jump2page(
          PeopledetailPage(peopleInfo),
          onBack: (results) {
            if (results != null) {
              if (results == 'remove') {
                controller.peoples.remove(peopleInfo);
                Get.find<PeoplesController>()
                    .update([PeoplesControllerIDS.peopleList]);
                return;
              }
              peopleInfo.toJson().updateAll((key, value) => results);
              Get.find<PeoplesController>()
                  .update([PeoplesControllerIDS.peopleList]);
              Get.find<PeopledetailController>().photoPath = '';
            }
          },
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 25, horizontal: 25),
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(peopleInfo.isKeyPersonnel
                  ? 'assets/pic/234-2-red.png'
                  : 'assets/pic/234-2.png'),
              fit: BoxFit.fill,
            ),
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: ImageWidget.network(
                  peopleInfo.photo ?? '',
                  fit: BoxFit.cover,
                  width: 130,
                  height: 130 * 4 / 3,
                ),
              ),
              const SizedBox(width: 20),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    peopleInfo.name ?? '',
                    style: const TextStyle(
                        color: Color(0xf0e5edff),
                        fontSize: 18,
                        fontWeight: FontWeight.bold),
                  ),
                  const Spacer(flex: 2),
                  Visibility(
                    visible: peopleInfo.isKeyPersonnel,
                    child: Row(
                      children: [
                        const Text("等级评估：",
                            style: TextStyle(color: Color(0xd8cfe0ff))),
                        Container(
                          width: 15,
                          height: 15,
                          margin: const EdgeInsets.only(right: 10),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Colors.white,
                              width: 1,
                            ),
                            color: peopleInfo.level == 0
                                ? Colors.green
                                : peopleInfo.level == 1
                                    ? Colors.yellow
                                    : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(flex: 1),
                  Text("证件号: ${peopleInfo.idNumber ?? ''}",
                      style: const TextStyle(color: Color(0xd8cfe0ff))),
                  const Spacer(flex: 1),
                  Text("出生日期: ${peopleInfo.birthDate ?? ''}",
                      style: const TextStyle(color: Color(0xd8cfe0ff))),
                  const Spacer(flex: 1),
                  Text("联系电话: ${peopleInfo.telephone ?? ''}",
                      style: const TextStyle(color: Color(0xd8cfe0ff))),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
