import 'package:flutter/material.dart';
import 'package:police/common/widget/drag_window.dart';
import 'package:police/common/widget/window_manager.dart';

import '../routes/jump_page.dart';

class ScaffoldWidget extends StatelessWidget {
  const ScaffoldWidget({
    super.key,
    required this.body,
    this.floatButton,
    this.strTitle,
    this.floatButtonMargin = const EdgeInsets.all(36),
    this.backResult,
    this.showBack = true,
    this.title,
    this.canBack = true,
    this.onBack,
  });

  final Widget body;
  final String? strTitle;
  final List<Widget>? floatButton;
  final EdgeInsets floatButtonMargin;
  final dynamic backResult;
  final bool showBack;
  final Widget? title;
  final bool canBack;
  final Function()? onBack;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Image.asset(
            'assets/gif/bg.gif',
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
          ),
          body,
          Align(
            alignment: Alignment.topCenter,
            child: DragWindowWidget(
              child: Container(
                width: double.infinity,
                height: 60,
                alignment: Alignment.centerLeft,
                child: Row(
                  children: [
                    Visibility(
                      visible: showBack,
                      child: IconButton(
                        // onPressed: () => pageback(result: backResult),
                        onPressed: () {
                          canBack
                              ? pageback(result: backResult)
                              : onBack?.call();
                        },
                        padding: const EdgeInsets.all(15),
                        icon: const Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    // Expanded(
                    //   child: Center(
                    //     child: Text(
                    //       strTitle ?? '',
                    //       style: TextStyle(fontSize: 20, color: Colors.white),
                    //     ),
                    //   ),
                    // ),
                    const Spacer(),
                    const WindowManager(),
                  ],
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomRight,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: floatButton ?? [],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(15),
            child: Align(
              alignment: Alignment.topCenter,
              child: title ??
                  Text(
                    strTitle ?? '',
                    style: const TextStyle(fontSize: 22, color: Colors.white),
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
