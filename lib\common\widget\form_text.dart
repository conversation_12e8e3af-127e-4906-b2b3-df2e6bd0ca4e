import 'package:flutter/widgets.dart';

class FormText extends StatelessWidget {
  const FormText({
    super.key,
    required this.labelText,
    required this.valueText,
    this.icon,
    this.padding,
    this.maxLines = 1,
    this.style,
    this.expandValue = false,
    this.fontSize,
  });

  final String labelText;
  final String valueText;
  final IconData? icon;
  final int? maxLines;
  final EdgeInsets? padding;
  final TextStyle? style;
  final bool expandValue;
  final double? fontSize;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          icon != null
              ? Icon(
                  icon,
                  size: 17,
                  color: const Color(0xffffffff),
                )
              : const SizedBox(),
          // const SizedBox(width: 8),
          Text(
            '$labelText：',
            style: style ??
                TextStyle(color: const Color(0xffffffff), fontSize: fontSize),
          ),
          expandValue
              ? Expanded(
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      valueText,
                      maxLines: maxLines,
                      overflow: TextOverflow.ellipsis,
                      style: style ??
                          TextStyle(
                              color: const Color(0xffffffff),
                              fontSize: fontSize),
                    ),
                  ),
                )
              : Text(
                  valueText,
                  maxLines: maxLines,
                  overflow: TextOverflow.ellipsis,
                  style: style ??
                      TextStyle(
                          color: const Color(0xffffffff), fontSize: fontSize),
                ),
        ],
      ),
    );
  }
}
