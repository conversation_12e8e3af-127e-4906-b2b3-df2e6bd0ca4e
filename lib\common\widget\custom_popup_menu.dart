import 'package:flutter/material.dart';

class CustomPopupMenu extends StatelessWidget {
  final Widget Function(BuildContext, void Function(BuildContext, Offset, Size))
      buttonBuilder;
  final List<PopupMenuEntry> menuItems;

  CustomPopupMenu({
    required this.buttonBuilder,
    required this.menuItems,
  });

  void showCustomPopupMenu(
      BuildContext context, Offset globalPosition, Size buttonSize) {
    Navigator.of(context).push(_PopupRoute(
      globalPosition: globalPosition,
      buttonSize: buttonSize,
      menuItems: menuItems,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return buttonBuilder(context, (context, globalPosition, buttonSize) {
      showCustomPopupMenu(context, globalPosition, buttonSize);
    });
  }
}

class _PopupRoute extends PopupRoute {
  final Offset globalPosition;
  final Size buttonSize;
  final List<PopupMenuEntry> menuItems;

  _PopupRoute({
    required this.globalPosition,
    required this.buttonSize,
    required this.menuItems,
  });

  @override
  Color get barrierColor => Colors.transparent;

  @override
  bool get barrierDismissible => true;

  @override
  String get barrierLabel => '';

  @override
  Duration get transitionDuration => const Duration(milliseconds: 200);

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    return CustomSingleChildLayout(
      delegate: _PopupMenuRouteLayout(globalPosition, buttonSize),
      child: FadeTransition(
        opacity: animation,
        child: Column(
          children: [
            Material(
              color: Colors.white,
              shape: ArrowShapeBorder(),
              elevation: 8.0,
              child: IntrinsicWidth(
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: menuItems,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _PopupMenuRouteLayout extends SingleChildLayoutDelegate {
  final Offset globalPosition;
  final Size buttonSize;

  _PopupMenuRouteLayout(this.globalPosition, this.buttonSize);

  @override
  BoxConstraints getConstraintsForChild(BoxConstraints constraints) {
    return BoxConstraints.loose(constraints.biggest);
  }

  @override
  Offset getPositionForChild(Size size, Size childSize) {
    double dx = globalPosition.dx - childSize.width / 2 + buttonSize.width / 2;
    double dy = globalPosition.dy + buttonSize.height + 10;
    if (dx + childSize.width > size.width) {
      dx = size.width - childSize.width;
    } else if (dx < 0) {
      dx = 0;
    }
    return Offset(dx, dy);
  }

  @override
  bool shouldRelayout(_PopupMenuRouteLayout oldDelegate) {
    return globalPosition != oldDelegate.globalPosition ||
        buttonSize != oldDelegate.buttonSize;
  }
}

class ArrowShapeBorder extends ShapeBorder {
  @override
  EdgeInsetsGeometry get dimensions => const EdgeInsets.all(10.0);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return getOuterPath(rect, textDirection: textDirection);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    const double arrowHeight = 10.0;
    const double arrowWidth = 20.0;
    const double radius = 10.0;

    final path = Path()
      ..moveTo(rect.left + rect.width / 2 - arrowWidth / 2, rect.top)
      ..lineTo(rect.left + rect.width / 2, rect.top - arrowHeight)
      ..lineTo(rect.left + rect.width / 2 + arrowWidth / 2, rect.top)
      ..addRRect(RRect.fromRectAndCorners(
        Rect.fromLTWH(rect.left, rect.top, rect.width, rect.height),
        topLeft: const Radius.circular(radius),
        topRight: const Radius.circular(radius),
        bottomLeft: const Radius.circular(radius),
        bottomRight: const Radius.circular(radius),
      ));

    return path;
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final paint = Paint()
      ..color = Colors.white // 确保背景颜色为白色
      ..style = PaintingStyle.stroke;
    canvas.drawPath(getOuterPath(rect, textDirection: textDirection), paint);
  }

  @override
  ShapeBorder scale(double t) {
    return ArrowShapeBorder();
  }
}
