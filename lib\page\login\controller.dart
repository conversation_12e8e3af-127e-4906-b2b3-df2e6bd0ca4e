import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/user_api.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/enums.dart';
import 'package:police/common/model/user_info_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/util/shared.dart';
import 'package:police/page/main/main_page.dart';
import 'package:police/page/splash/controller.dart';
import 'package:police/page/user/controller.dart';

import '../../common/api/apis.dart';

class LoginController extends GetxController {
  bool isSignup = false;
  bool isLogin = false;
  String token = '';

  /// 是否已登录
  Future<bool> getIsLogin() async {
    String? token = await SharedUtil.getString('token');

    if (token != '' && token != null) {
      isLogin = true;
      token = await SharedUtil.getString('token');
    }

    return isLogin;
  }

  /// 登录
  Future login(
    BuildContext context,
    String server,
    String username,
    String password,
    String key,
  ) async {
    if (server.isEmpty) {
      showToast('请输入服务器地址');
      return;
    }
    if (key.isEmpty) {
      showToast('请输入密钥');
      return;
    }
    if (username.isNotEmpty && password.isNotEmpty) {
      Apis.baseUrl = server;
      Dialogs.showLoadingDialog(context);
      await SharedUtil.setString('keyStr', key);

      token = await UserApi.login(
        username,
        password,
      ).then((value) => value['access_token']);
      await SharedUtil.setString('token', token);
      await SharedUtil.setString('server', server);

      Get.find<LoginController>().isLogin = true;

      await getUserInfo();
      pageback();

      jump2page(
        const MainPage(),
        jumpPageType: JumpPageType.offAll,
        args: token,
      );
    } else {
      showToast('账号或密码不能为空');
    }
    return token;
  }

  /// 注册
  Future signup(
    BuildContext context,
    String username,
    String password,
    String password2,
  ) async {
    if (username.isNotEmpty && password.isNotEmpty && password2.isNotEmpty) {
      if (password == password2) {
        Dialogs.showLoadingDialog(context);
        await UserApi.signup(username, password);
        showToast('注册成功');
      } else {
        showToast('密码不一致');
      }
    } else {
      showToast('账号或密码不能为空');
    }
  }

  /// 获取用户信息
  getUserInfo() async {
    token = await SharedUtil.getString('token');
    final userInfo = await UserApi.getUserInfo(
      token,
      fail: (p0) {
        pageback();
        Get.find<SplashController>().countdown = 0;
        Get.find<SplashController>().timer.cancel();
        Get.find<SplashController>().update(['server_button']);
      },
    );
    Get.put(UserController()).userInfo = userInfo;
  }

  /// 更改密码
  Future changedPwd() async {}
}
