import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:police/common/api/uplaod_image_api.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/util/image_picker.dart';
import 'package:police/common/widget/float_square_button.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/common/widget/image_upload.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/page/villageDetail/index.dart';

import '../../../common/model/village_model/village_model.dart';

class Police extends StatelessWidget {
  const Police({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    VillageModel detail = Get.find<VillagedetailController>().villageDetail;

    ScrollController scrollController = ScrollController();
    PageController pageController = PageController();
    final double screenHeight = MediaQuery.of(context).size.height;

    return GetBuilder<VillagedetailController>(
        id: VillageDetailControllerIDS.police,
        builder: (contro) {
          return Container(
            key: contro.police,
            height: screenHeight,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center, // 垂直居中
              children: [
                Container(
                  width: 450,
                  // height: 350,
                  padding: const EdgeInsets.only(top: 75),
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/pic/1117.png'),
                      fit: BoxFit.fill,
                    ),
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            InkWell(
                              onTap: () async {},
                              child: ClipOval(
                                child: ImageUploadWidget(
                                  width: 140,
                                  height: 140,
                                  photoUrl: detail.policeDetail!.avatar,
                                  onUploaded: (url) {
                                    detail.policeDetail!.avatar = url;
                                  },
                                ), // 头像图片的路径
                              ),
                            ),
                            const SizedBox(height: 20), // 添加一个间距
                            InputText(
                              defaultText: detail.policeDetail!.name,
                              isDense: true,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                fontSize: 30,
                                fontWeight: FontWeight.bold,
                                color: Colors.white, // 确保文本在背景上可见
                              ),
                              onChanged: (text) =>
                                  detail.policeDetail!.name = text,
                            ),
                            const SizedBox(height: 20), // 添加一个间距
                            SizedBox(
                              height: 100,
                              child: Scrollbar(
                                controller: scrollController,
                                child: ListView.builder(
                                  controller: scrollController,
                                  scrollDirection: Axis.horizontal,
                                  itemCount:
                                      detail.policeDetail!.pics!.length + 1,
                                  itemBuilder: (context, index) {
                                    return _buildPicItem(index, detail, contro,
                                        pageController, context);
                                  },
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  InkWell _buildPicItem(
      int index,
      VillageModel detail,
      VillagedetailController contro,
      PageController pageController,
      BuildContext context) {
    return InkWell(
      onTap: () async {
        if (index == detail.policeDetail!.pics!.length) {
          XFile? file = await imagePicker();
          if (file != null) {
            Dialogs.showLoadingDialog(context);
            detail.policeDetail!.pics!.add(file.path);
            contro.update([VillageDetailControllerIDS.police]);
            final result = await uploadImage(
              file.path,
              fail: (p0) {
                pageback();
                detail.policeDetail!.pics!.remove(file.path);
                contro.update([VillageDetailControllerIDS.police]);
              },
            );
            pageback();
            detail.policeDetail!.pics![index] = result['files'][0];
            contro.update([VillageDetailControllerIDS.police]);
          }
        } else {
          pageController = PageController(initialPage: index);

          showDialog(
              context: context,
              builder: (context) {
                return _buildPhotoView(
                    context, contro, pageController, detail, index);
              });
        }
      },
      child: SizedBox(
        width: 100,
        height: 100,
        child: index < detail.policeDetail!.pics!.length
            ? !detail.policeDetail!.pics![index].contains(':\\')
                ? ImageWidget.network(detail.policeDetail!.pics![index])
                : ImageWidget.file(detail.policeDetail!.pics![index])
            : Container(
                color: Colors.grey.withOpacity(.3),
                child: const Icon(Icons.photo),
              ),
      ),
    );
  }

  Widget _buildPhotoView(
      BuildContext context,
      VillagedetailController controller,
      PageController pageController,
      VillageModel detail,
      int index) {
    return Stack(
      children: [
        PageView.builder(
            controller: pageController,
            itemCount: detail.policeDetail!.pics!.length,
            itemBuilder: (_, i) {
              return Stack(
                children: [
                  Center(
                    child: ImageWidget.network(
                      detail.policeDetail!.pics![i],
                      fit: BoxFit.contain,
                    ),
                  ),
                  Center(
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            pageController.animateToPage(i - 1,
                                duration: const Duration(microseconds: 500),
                                curve: Curves.linear);
                          },
                          icon: const Icon(
                            CupertinoIcons.chevron_left,
                            size: 50,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () {
                            pageController.animateToPage(i + 1,
                                duration: const Duration(microseconds: 500),
                                curve: Curves.linear);
                          },
                          icon: const Icon(
                            CupertinoIcons.chevron_right,
                            size: 50,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 50),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          FloatSquareButton(
                            onTap: () {
                              Dialogs.showConfirmDialog(
                                context,
                                title: '是否确认删除此图片',
                                content: '删除后无法恢复！',
                                onConfirm: () {
                                  detail.policeDetail!.pics!.removeAt(index);
                                  pageback();
                                  controller.update(
                                      [VillageDetailControllerIDS.police]);
                                },
                              );
                            },
                            icon: Icons.delete,
                            color: Colors.red,
                          ),
                          const SizedBox(width: 20),
                          FloatSquareButton(
                            onTap: () async {
                              XFile? file = await imagePicker();
                              if (file != null) {
                                pageback();

                                Dialogs.showLoadingDialog(context);

                                detail.policeDetail!.pics![index] = file.path;

                                controller.update(
                                    [VillageDetailControllerIDS.police]);
                                final result = await uploadImage(
                                  file.path,
                                  fail: (p0) {
                                    pageback();
                                    detail.policeDetail!.pics!
                                        .remove(file.path);
                                    controller.update(
                                        [VillageDetailControllerIDS.police]);
                                  },
                                );
                                pageback();
                                detail.policeDetail!.pics![index] =
                                    result['files'][0];
                                controller.update(
                                    [VillageDetailControllerIDS.police]);
                              }
                            },
                            icon: Icons.cached,
                            color: Colors.blue,
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              );
            }),
        Padding(
          padding: const EdgeInsets.fromLTRB(0, 70, 20, 0),
          child: Align(
            alignment: Alignment.topRight,
            child: FloatSquareButton(
              onTap: () => pageback(),
              icon: Icons.close,
            ),
          ),
        )
      ],
    );
  }
}
