import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/page/largescreen/controller.dart';

import '../../../common/model/big_screen_model/alarm_type.dart';
import '../../../common/widget/corner_card.dart';
import '../../cases/controller.dart';
import '../../main/controller.dart';

class PoliceInformationCase extends StatelessWidget {
  const PoliceInformationCase({super.key});

  @override
  Widget build(BuildContext context) {
    LargescreenController controller = Get.find<LargescreenController>();
    return CornerCard(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          children: [
            Row(
              children: [
                const Text(
                  "案件与警情",
                  style: TextStyle(fontSize: 16, color: Color(0xe7ffffff)),
                ),
                const Spacer(),
                InkWell(
                  onTapDown: (details) =>
                      _showMenu(context, details, controller),
                  child: GetBuilder<LargescreenController>(
                      id: LargescernnIDS.cases,
                      builder: (context) {
                        return Text(
                          TimeRangeStr[controller.timeRange]!,
                          style: const TextStyle(
                              fontSize: 14, color: Color(0xe7ffffff)),
                        );
                      }),
                ),
              ],
            ),
            const SizedBox(height: 30), // 增加一些间距
            const BarChartSample3(), // 添加 BarChartSample3
          ],
        ),
      ),
    );
  }

  void _showMenu(BuildContext context, TapDownDetails details,
      LargescreenController controller) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    if (controller.loading) {
      showToast('请先等待加载完成');
      return;
    }
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        details.globalPosition.dx,
        details.globalPosition.dy,
        overlay.size.width - details.globalPosition.dx,
        overlay.size.height - details.globalPosition.dy,
      ),
      items: List.generate(TimeRangeStr.keys.length, (index) {
        final key = TimeRangeStr.keys.elementAt(index);
        return PopupMenuItem(value: key, child: Text(TimeRangeStr[key]!));
      }),
    ).then((value) {
      if (value != null) {
        controller.loading = true;
        controller.timeRange = value!;
        controller.update([LargescernnIDS.cases]);
        controller.getBigScreenData();
      }
    });
  }
}

class BarChartSample3 extends StatelessWidget {
  const BarChartSample3({super.key});

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.6,
      child: GetBuilder<LargescreenController>(
          id: LargescernnIDS.cases,
          builder: (controller) {
            List<AlarmType> cases = controller.bigscreenData.alarmTypes!;
            if (cases.isEmpty) {
              return Container();
            } else {
              // 找到列表中的最大值
              AlarmType maxNumber =
                  cases.reduce((a, b) => a.alarmCount! > b.alarmCount! ? a : b);

              // 向上取整到最接近的十的倍数
              int roundedUp = ((maxNumber.alarmCount! / 10).ceil()) * 10;

              return BarChart(
                BarChartData(
                  barTouchData: _buildBarTouchData(),
                  titlesData: _buildTitles(cases),
                  borderData: FlBorderData(show: false),
                  barGroups: _buildBarGroups(cases),
                  gridData: const FlGridData(show: false),
                  alignment: BarChartAlignment.spaceAround,
                  maxY: roundedUp.toDouble(),
                ),
              );
            }
          }),
    );
  }

  List<BarChartGroupData> _buildBarGroups(List<AlarmType> cases) {
    // 定义一组颜色，数量应与 `cases` 列表的长度匹配
    final List<Color> colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.yellow,
      Colors.pink,
    ];

    return List.generate(cases.length, (index) {
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: cases[index].alarmCount!.toDouble(),
            color: colors[index % colors.length], // 使用不同的颜色
          )
        ],
        showingTooltipIndicators: [0],
      );
    });
  }

  FlTitlesData _buildTitles(List<AlarmType> cases) {
    return FlTitlesData(
      show: true,
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 30,
          getTitlesWidget: (value, meta) {
            const style = TextStyle(
              color: Colors.blue,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            );
            return InkWell(
              onTap: () {
                LargescreenController largescreenController =
                    Get.find<LargescreenController>();
                CasesController casesController = Get.find<CasesController>();
                DateTime dateTime = DateTime.now();
                String year = dateTime.year.toString();
                String month = dateTime.month.toString();
                String day = dateTime.day.toString();
                for (var item in casesController.type) {
                  if (item.text.contains(cases[value.toInt()].name!)) {
                    switch (largescreenController.timeRange) {
                      case 'today':
                        casesController.selectedYear = year;
                        casesController.selectedMonth = month;
                        casesController.selectedDay = day;
                        break;
                      case 'this_month':
                        casesController.selectedYear = year;
                        casesController.selectedMonth = month;
                        casesController.selectedDay = '';
                        break;
                      case 'last_month':
                        casesController.selectedYear = year;
                        casesController.selectedMonth =
                            (int.parse(month) - 1).toString();
                        casesController.selectedDay = '';
                        break;
                      case 'this_quarter':
                        casesController.selectedYear = year;
                        casesController.selectedMonth = '';
                        casesController.selectedDay = '';
                        break;
                      case 'this_year':
                        casesController.selectedYear = year;
                        casesController.selectedMonth = '';
                        casesController.selectedDay = '';
                        break;
                    }
                    casesController.selectedType = item.value!;
                    Get.find<MainController>().tabController.animateTo(3);
                    casesController.update([CaseControllerIDS.category]);
                    casesController.getCases(page: 1);
                    break;
                  }
                }
              },
              child: SideTitleWidget(
                axisSide: meta.axisSide,
                space: 4,
                child: Text(cases[value.toInt()].name ?? '', style: style),
              ),
            );
          },
        ),
      ),
      leftTitles: const AxisTitles(
        sideTitles: SideTitles(showTitles: false),
      ),
      topTitles: const AxisTitles(
        sideTitles: SideTitles(showTitles: false),
      ),
      rightTitles: const AxisTitles(
        sideTitles: SideTitles(showTitles: false),
      ),
    );
  }

  BarTouchData _buildBarTouchData() {
    return BarTouchData(
      enabled: false,
      touchTooltipData: BarTouchTooltipData(
        getTooltipColor: (group) => Colors.transparent,
        tooltipPadding: EdgeInsets.zero,
        tooltipMargin: 8,
        getTooltipItem: (
          BarChartGroupData group,
          int groupIndex,
          BarChartRodData rod,
          int rodIndex,
        ) {
          return BarTooltipItem(
            rod.toY.round().toString(),
            const TextStyle(
              color: Colors.cyan,
              fontWeight: FontWeight.bold,
            ),
          );
        },
      ),
    );
  }
}
