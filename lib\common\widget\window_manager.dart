import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class WindowManager extends StatelessWidget {
  const WindowManager({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: () => appWindow.minimize(),
          icon: const Icon(FontAwesomeIcons.minus),
          color: const Color(0xd8cfe0ff),
        ),
        const SizedBox(width: 5),
        StatefulBuilder(builder: (context, state) {
          return IconButton(
            onPressed: () {
              appWindow.maximizeOrRestore();
              state(() {});
            },
            icon: Icon(
                appWindow.isMaximized
                    ? FontAwesomeIcons.windowRestore
                    : FontAwesomeIcons.expand,
                size: 20),
            color: const Color(0xd8cfe0ff),
          );
        }),
        const SizedBox(width: 5),
        IconButton(
          onPressed: () => appWindow.close(),
          icon: const Icon(FontAwesomeIcons.xmark),
          color: const Color(0xd8cfe0ff),
        ),
        const SizedBox(width: 10),
      ],
    );
  }
}
