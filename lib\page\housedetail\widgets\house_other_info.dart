import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:police/common/model/house_model.dart';
import 'package:police/common/widget/check_box.dart';
import 'package:police/common/widget/radio.dart';
import 'package:police/page/housedetail/index.dart';

class HouseOtherInfo extends StatelessWidget {
  const HouseOtherInfo({super.key, required this.detail});

  final HouseModel detail;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HousedetailController>(
      id: HousedetailIDS.otherInfo,
      builder: (contro) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '家庭环境信息',
              style: TextStyle(fontSize: 40, color: Color(0xd8ffffff)),
            ),
            const SizedBox(height: 10),
            Wrap(
              spacing: 20,
              runSpacing: 20,
              children: [
                CheckBoxWidegt(
                  label: '车辆情况',
                  chekcs: const [
                    '汽车(燃油、混动、电动)',
                    '摩托车(燃油、电动)',
                    '农运三四轮(燃油、电动)',
                    '其他'
                  ],
                  checked: detail.vehicle,
                  otherStr: detail.vehicleAdditional ?? '',
                  onChanged: (val, text) {
                    if (val) {
                      detail.vehicle.add(text);
                    } else {
                      detail.vehicle.remove(text);
                    }
                    contro.update([HousedetailIDS.otherInfo]);
                  },
                  onChangedOther: (text) {
                    detail.vehicleAdditional = text;
                  },
                ),
                CheckBoxWidegt(
                  label: '半年内成员报警(12345记录)',
                  chekcs: const ['求助', '刑事类', '治安类', '举报类', '12345', '其他'],
                  checked: detail.recentAlerts,
                  otherStr: detail.recentAlertsAdditional ?? '',
                  onChanged: (val, text) {
                    if (val) {
                      detail.recentAlerts.add(text);
                    } else {
                      detail.recentAlerts.remove(text);
                    }
                    contro.update([HousedetailIDS.otherInfo]);
                  },
                  onChangedOther: (text) {
                    detail.recentAlertsAdditional = text;
                  },
                ),
                RadioWidget(
                  label: '处置办结情况',
                  radios: const ['已办结', '未办结', '处置中'],
                  valueGroup: detail.caseStatus ?? '',
                  onChanged: (val) {
                    detail.caseStatus = val;
                    contro.update([HousedetailIDS.otherInfo]);
                  },
                ),
                RadioWidget(
                  label: '有无家庭暴力情况',
                  radios: const ['是', '否'],
                  valueGroup: detail.domesticViolence ? '是' : '否',
                  onChanged: (val) {
                    detail.domesticViolence = val == '是';
                    contro.update([HousedetailIDS.otherInfo]);
                  },
                ),
                RadioWidget(
                  label: '同住人是否存在抑郁精神障碍或暴力倾向',
                  radios: const ['有', '无'],
                  valueGroup: detail.cohabitantIssues ? '有' : '无',
                  onChanged: (val) {
                    detail.cohabitantIssues = val == '有';
                    contro.update([HousedetailIDS.otherInfo]);
                  },
                ),
                RadioWidget(
                  label: '同住人是否需要心里疏导服务',
                  radios: const ['是', '否'],
                  valueGroup: detail.needsCounseling ? '是' : '否',
                  onChanged: (val) {
                    detail.needsCounseling = val == '是';
                    contro.update([HousedetailIDS.otherInfo]);
                  },
                ),
                RadioWidget(
                  label: '犬只体型',
                  radios: const ['大', '中', '小', '无'],
                  valueGroup: detail.dogSize ?? '无',
                  onChanged: (val) {
                    detail.dogSize = val;
                    contro.update([HousedetailIDS.otherInfo]);
                  },
                ),
                RadioWidget(
                  label: '犬只办证',
                  radios: const ['是', '否'],
                  valueGroup: detail.dogDocuments ? '是' : '否',
                  onChanged: (val) {
                    detail.dogDocuments = val == '是';
                    contro.update([HousedetailIDS.otherInfo]);
                  },
                ),
                RadioWidget(
                  label: '反诈App是否下载',
                  radios: const ['是', '否'],
                  valueGroup: detail.isAppDownload ? '是' : '否',
                  onChanged: (val) {
                    detail.isAppDownload = val == '是';
                    contro.update([HousedetailIDS.otherInfo]);
                  },
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
