import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/page/area/index.dart';
import 'package:police/page/villageDetail/controller.dart';
import 'package:police/page/villageDetail/index.dart';

import '../../../common/widget/float_square_button.dart';

class FloatButton extends StatelessWidget {
  const FloatButton({super.key, required this.controller, this.id});

  final VillagedetailController controller;
  final int? id;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.all(36),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Visibility(
              visible: id != null && controller.code == 200,
              child: FloatSquareButton(
                onTap: () => controller.deleteVillage(
                    context, controller.villageDetail.name!, id!),
                color: Colors.red,
                icon: Icons.delete,
              ),
            ),
            const SizedBox(height: 10),
            Visibility(
              visible: id != null,
              child: FloatSquareButton(
                onTap: () => controller.getVillageDetail(id!),
                icon: Icons.refresh,
              ),
            ),
            const SizedBox(height: 10),
            FloatSquareButton(
              onTap: () {
                String villageName = controller.villageDetail.name ?? '';
                if (villageName != '') {
                  controller.updateVillageDetail(
                    context,
                    id: id,
                    data: controller.villageDetail.toJson(),
                  );
                } else {
                  showToast('村庄名不能为空');
                }
              },
              icon: FontAwesomeIcons.floppyDisk,
            ),
            const SizedBox(height: 10),
            FloatSquareButton(
              onTap: () {
                if (id != null) {
                  jump2page(AreaPage(
                    villageId: id!,
                    villageName: controller.villageDetail.name!,
                  ));
                } else {
                  showToast('请先保存村庄信息');
                }
              },
              icon: Icons.list_alt_rounded,
            ),
          ],
        ),
      ),
    );
  }
}
