import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/empty_widget.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/keep_alive_widget.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/page/village/widgets/float_button.dart';
import 'package:police/page/village/widgets/village_info.dart';

import 'index.dart';

class VillagePage extends GetView<VillageController> {
  const VillagePage({super.key});

  // 主视图
  Widget _buildView(BuildContext context, int crossAxisCount) {
    return Stack(
      children: [
        Center(
          child: controller.code == 0
              ? const LoadingWidget()
              : controller.code == 404
                  ? const HttpErrorWidget()
                  : controller.code == 200 && controller.villages.isEmpty
                      ? const EmptyWidget()
                      : Padding(
                          padding: const EdgeInsets.only(top: 60),
                          child: GridView.builder(
                            padding: const EdgeInsets.symmetric(
                                vertical: 10, horizontal: 50),
                            itemCount: controller.villages.length,
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: crossAxisCount,
                              childAspectRatio: 1.5,
                              mainAxisSpacing: 30,
                              crossAxisSpacing: 30,
                            ),
                            itemBuilder: (_, index) => VillageInfo(
                              villageInfo: controller.villages[index],
                              index: index,
                            ),
                          ),
                        ),
        ),
        FloatButton(controller: controller)
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    Get.put(VillageController()).getVillages();
    return KeepAliveWidget(
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final crossAxisCount = (constraints.maxWidth / 500).round();
          return GetBuilder<VillageController>(
            init: VillageController(),
            id: VillageControllerIDS.village,
            builder: (_) {
              return _buildView(context, crossAxisCount);
            },
          );
        },
      ),
    );
  }
}
