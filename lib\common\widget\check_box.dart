import 'package:flutter/material.dart';
import 'package:police/common/widget/input_text.dart';

class CheckBoxWidegt extends StatelessWidget {
  final String label;
  final List<String> chekcs;

  final Function(bool, String text) onChanged;
  final Axis scrollDirection;
  final ScrollPhysics physics;
  final bool shrinkWrap;
  final Widget divider;
  final Widget Function(int)? leading;
  final Function(int)? onTap;
  final bool value;
  final EdgeInsets? margin;
  final Function(String text)? onChangedOther;
  final double fontSize;
  final List<dynamic> checked;
  final String otherStr;

  const CheckBoxWidegt({
    super.key,
    this.label = '',
    required this.onChanged,
    required this.chekcs,
    this.scrollDirection = Axis.horizontal,
    this.physics = const NeverScrollableScrollPhysics(),
    this.shrinkWrap = true,
    this.divider = const SizedBox(),
    this.leading,
    this.onTap,
    this.value = false,
    this.margin,
    this.onChangedOther,
    this.fontSize = 20,
    this.checked = const [],
    this.otherStr = '',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(4)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          label != ''
              ? Text(
                  label,
                  style: TextStyle(
                      color: const Color(0xffffffff), fontSize: fontSize),
                )
              : Container(width: 0),
          const SizedBox(height: 10),
          Wrap(
            spacing: 10,
            children: List.generate(chekcs.length, (index) {
              return GestureDetector(
                onTap: () => onTap?.call(index),
                child: Container(
                  color: Colors.transparent,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Checkbox(
                        value: checked.contains(chekcs[index]),
                        onChanged: (val) {
                          onChanged(val!, chekcs[index]);
                        },
                      ),
                      const SizedBox(width: 10),
                      leading == null ? const SizedBox() : leading!(index),
                      leading == null
                          ? chekcs[index] == '其他'
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      chekcs[index],
                                      style: TextStyle(
                                        color: const Color(0xffffffff),
                                        fontSize: fontSize,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    SizedBox(
                                      width: 200,
                                      child: InputText(
                                        canEdit:
                                            checked.contains(chekcs[index]),
                                        defaultText: checked.contains('其他')
                                            ? otherStr
                                            : '',
                                        fontSize: fontSize,
                                        fontColor: const Color(0xffffffff),
                                        onChanged: (text) {
                                          // chekcs[index] = text;
                                          onChangedOther?.call(text);
                                        },
                                      ),
                                    ),
                                  ],
                                )
                              : Text(
                                  chekcs[index],
                                  style: TextStyle(
                                      color: const Color(0xffffffff),
                                      fontSize: fontSize),
                                )
                          : Expanded(
                              child: Text(
                              chekcs[index],
                              style: TextStyle(
                                  color: const Color(0xffffffff),
                                  fontSize: fontSize),
                            )),
                    ],
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
