import 'package:get/get.dart';

import '../../common/api/people_api.dart';

class FoucsPeoplesController extends GetxController {
  int code = 0;
  List<Map<String, dynamic>> focusData = [];

  @override
  void onReady() {
    super.onReady();
    getFocusPeople();
  }

  /// 获取重点人员
  Future<void> getFocusPeople() async {
    code = 0;
    Map<String, dynamic> result = await PeopleApi.getFocusPeopleInfo(
      fail: (response) {
        code = 404;
      },
    );

    focusData = List<Map<String, dynamic>>.from(result['data'] ?? []);
    code = 200;
    update(['peoples']);
  }
}
