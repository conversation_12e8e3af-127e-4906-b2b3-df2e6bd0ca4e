import 'package:police/common/model/village_model/manager.dart';
import 'package:police/common/model/village_model/organize/organize.dart';
import 'package:police/common/model/village_model/place.dart';

import 'person_interest_detail.dart';
import 'statistics.dart';

class VillageModel {
  int? id;
  String? name;
  String? cover;
  String graphics;
  List<dynamic>? personInterest;
  List<Organize>? organize;
  int? policeId;
  List<PlaceModel>? place;
  DateTime? createTime;
  List<PersonInterestDetail>? personInterestDetails;
  ManagerModel? policeDetail;
  List<Statistics>? statistics;
  List<Statistics>? statisticsType;
  int? personCount;

  VillageModel({
    this.id,
    this.name,
    this.cover,
    this.graphics = '',
    this.personInterest,
    List<Organize>? organize,
    this.policeId,
    List<PlaceModel>? place,
    this.createTime,
    List<PersonInterestDetail>? personInterestDetails,
    ManagerModel? policeDetail,
    List<Statistics>? statistics,
    List<Statistics>? statisticsType,
    this.personCount = 0,
  })  : organize = organize ?? <Organize>[],
        place = place ?? <PlaceModel>[],
        personInterestDetails =
            personInterestDetails ?? <PersonInterestDetail>[],
        statistics = statistics ?? <Statistics>[],
        statisticsType = statisticsType ?? <Statistics>[],
        policeDetail = policeDetail ?? ManagerModel();

  factory VillageModel.fromJson(Map<String, dynamic> json) => VillageModel(
        id: json['id'] as int?,
        name: json['name'] as String?,
        cover: json['cover'] as String?,
        graphics: json['graphics'] as String,
        personInterest: json['person_interest'] as List<dynamic>?,
        place: (json['place'] as List<dynamic>?)
                ?.map((e) => PlaceModel.fromJson(e as Map<String, dynamic>))
                .toList() ??
            <PlaceModel>[],
        organize: (json['organize'] as List<dynamic>?)
                ?.map((e) => Organize.fromJson(e as Map<String, dynamic>))
                .toList() ??
            <Organize>[],
        policeId: json['police_id'] as int?,
        createTime: json['create_time'] == null
            ? null
            : DateTime.parse(json['create_time'] as String),
        personInterestDetails: (json['person_interest_details']
                    as List<dynamic>?)
                ?.map((e) =>
                    PersonInterestDetail.fromJson(e as Map<String, dynamic>))
                .toList() ??
            <PersonInterestDetail>[],
        policeDetail: json['police_details'] == null ||
                (json['police_details'] as Map<String, dynamic>).isEmpty
            ? ManagerModel()
            : ManagerModel.fromJson(
                json['police_details'] as Map<String, dynamic>),
        statistics: (json['statistics'] as List<dynamic>?)
                ?.map((e) => Statistics.fromJson(e as Map<String, dynamic>))
                .toList() ??
            <Statistics>[],
        statisticsType: (json['statistics_type'] as List<dynamic>?)
                ?.map((e) => Statistics.fromJson(e as Map<String, dynamic>))
                .toList() ??
            <Statistics>[],
        personCount: json['person_count'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'cover': cover,
        'graphics': graphics,
        'person_interest': personInterest,
        'organize': organize?.map((e) => e.toJson()).toList(),
        'police_id': policeId,
        'place': place?.map((e) => e.toJson()).toList(),
        'create_time': createTime?.toIso8601String(),
        'person_interest_details':
            personInterestDetails?.map((e) => e.toJson()).toList(),
        'police_details': policeDetail?.toJson(),
        'statistics': statistics?.map((e) => e.toJson()).toList(),
        'statistics_type': statisticsType?.map((e) => e.toJson()).toList(),
        'person_count': personCount,
      };
}
