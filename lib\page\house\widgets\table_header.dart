import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/page/cases/controller.dart';
import 'package:police/page/house/controller.dart';

class HouseTableHeader extends StatelessWidget {
  const HouseTableHeader({super.key, required this.controller});

  final HouseController controller;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HouseController>(
        id: CaseControllerIDS.header,
        builder: (context) {
          return Row(
            children: List.generate(controller.tableHeaders.length, (index) {
              return Expanded(
                flex: index == 4 ? 3 : 1,
                child: Container(
                  color: const Color(0xd3041e3e),
                  alignment: Alignment.center,
                  margin: const EdgeInsets.all(.5),
                  // padding: const EdgeInsets.symmetric(vertical: 15),
                  height: 70,
                  child: Text(
                    controller.tableHeaders[index],
                    style: const TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                        color: Color(0xf0e5edff)),
                  ),
                ),
              );
            }),
          );
        });
  }
}
