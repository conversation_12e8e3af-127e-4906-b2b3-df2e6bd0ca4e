import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/common/widget/scaffold_widget.dart';
import 'package:police/page/user/widgets/password_manager.dart';
import 'package:police/page/user/widgets/secret_key.dart';
import 'package:police/page/user/widgets/user_info.dart';
import 'package:police/page/user/widgets/user_list.dart';

import 'index.dart';

class UserPage extends GetView<UserController> {
  const UserPage({super.key, this.keyError = false});

  final bool keyError;

  // 主视图
  Widget _buildView(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(60),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const UserInfo(),
                const SizedBox(height: 60),
                const PasswordManager(),
                // const SizedBox(height: 20),
                // SecretKey(keyError: keyError),
                const Spacer(),
                NormolButton(
                  lable: '退出登录',
                  width: 200,
                  onTap: () => controller.logout(context),
                ),
              ],
            ),
          ),
          const Expanded(child: UserList()),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (Get.find<UserController>().userInfo.permissions == 'advanced' &&
        !keyError) {
      Get.find<UserController>().getUserList();
    }
    return GetBuilder<UserController>(
      id: "user",
      builder: (_) {
        return ScaffoldWidget(
          body: _buildView(context),
          strTitle: controller.userInfo.name ?? '',
        );
      },
    );
  }
}
