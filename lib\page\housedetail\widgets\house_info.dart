import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:police/common/model/house_model.dart';
import 'package:police/common/widget/form_input.dart';
import 'package:police/common/widget/radio.dart';
import 'package:police/page/housedetail/index.dart';

class HouseInfo extends StatelessWidget {
  const HouseInfo({super.key, required this.detail});

  final HouseModel detail;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HousedetailController>(
        id: HousedetailIDS.houseInfo,
        builder: (contro) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '房屋基本信息',
                style: TextStyle(fontSize: 40, color: Color(0xffffffff)),
              ),
              const SizedBox(height: 10),
              Wrap(
                spacing: 20,
                runSpacing: 20,
                children: [
                  // BrnFormInput(
                  //   title: '产权人姓名/公有房所属单位',
                  //   defaultText: detail.propertyOwner ?? '',
                  // )
                  FormInput(
                    width: 400,
                    maxLength: 20,
                    label: '产权人姓名/公有房所属单位',
                    defaultText: detail.propertyOwner ?? '',
                    onChanged: (text) => detail.propertyOwner = text,
                  ),
                  FormInput(
                    width: 300,
                    label: '身份证号',
                    defaultText: detail.propertyIdNumber ?? '',
                    maxLength: 18,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9Xx]'))
                    ],
                    onChanged: (text) => detail.propertyIdNumber = text,
                  ),
                  FormInput(
                    width: 200,
                    maxLength: 18,
                    label: '联系电话',
                    defaultText: detail.propertyTelephone ?? '',
                    onlyNumber: true,
                    onChanged: (text) => detail.propertyTelephone = text,
                  ),
                  RadioWidget(
                    label: '房屋用途',
                    valueGroup: detail.housingPurposes,
                    radios: const [
                      '自住',
                      '租赁',
                      '办公场所',
                      '库房仓储',
                      '生产加工',
                      '商业服务(网约房、茶社棋牌、产品销售、职工宿舍等)',
                      '其他',
                    ],
                    onChanged: (val) {
                      detail.housingPurposes = val;
                      contro.update([HousedetailIDS.houseInfo]);
                    },
                    onChangedOther: (text, p1) {
                      detail.housingPurposes = text;
                    },
                  ),
                  RadioWidget(
                    label: '居住证申领',
                    valueGroup: detail.residencePermit ? '是' : '否',
                    radios: const ['是', '否'],
                    onChanged: (val) {
                      if (val == '是') {
                        detail.residencePermit = true;
                      } else {
                        detail.residencePermit = false;
                      }
                      contro.update([HousedetailIDS.houseInfo]);
                    },
                  )
                ],
              ),
            ],
          );
        });
  }
}
