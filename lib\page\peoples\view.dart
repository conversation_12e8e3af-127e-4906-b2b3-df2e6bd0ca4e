import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/people_detail_model.dart';
import 'package:police/common/widget/empty_widget.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/keep_alive_widget.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/turn_page.dart';
import 'package:police/page/peoples/widgets/category.dart';
import 'package:police/page/peoples/widgets/float_button.dart';
import 'package:police/page/peoples/widgets/person_info_two.dart';

import '../../common/widget/scaffold_widget.dart';
import 'index.dart';

class PeoplesPage extends GetView<PeoplesController> {
  const PeoplesPage({super.key, this.fromFoucs = false, this.personType = ''});

  final bool fromFoucs;
  final String personType;

  // 主视图
  Widget _buildView(BuildContext context, int crossAxisCount) {
    return ScaffoldWidget(
      strTitle: fromFoucs ? personType : '',
      showBack: fromFoucs,
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 60),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Category(fromFoucs: fromFoucs),
                  GetBuilder<PeoplesController>(
                      id: PeoplesControllerIDS.peopleList,
                      builder: (context) {
                        return _isEmpty() && _status() == 0
                            ? const LoadingWidget()
                            : _isEmpty() && _status() == 200
                                ? const EmptyWidget()
                                : _status() == 404
                                    ? const HttpErrorWidget()
                                    : GridView.builder(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemCount: fromFoucs
                                            ? Get.find<PeoplesController>()
                                                .fromFoucsPeoples
                                                .length
                                            : Get.find<PeoplesController>()
                                                .peoples
                                                .length,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 50),
                                        gridDelegate:
                                            SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: crossAxisCount,
                                          childAspectRatio: 2.6,
                                          mainAxisSpacing: 30,
                                          crossAxisSpacing: 30,
                                        ),
                                        itemBuilder: (context, index) {
                                          PeopleDetailModel peopleInfo =
                                              fromFoucs
                                                  ? Get.find<
                                                          PeoplesController>()
                                                      .fromFoucsPeoples[index]
                                                  : Get.find<
                                                          PeoplesController>()
                                                      .peoples[index];
                                          return PersonInfoTwo(
                                            peopleInfo: peopleInfo,
                                            controller: controller,
                                          );
                                        },
                                        // delegate: SliverChildBuilderDelegate(
                                        //   (BuildContext context, int index) {
                                        //     PeopleDetailModel peopleInfo =
                                        //         Get.find<PeoplesController>()
                                        //             .peoples[index];
                                        //     return PersonInfoTwo(peopleInfo: peopleInfo);
                                        //   },
                                        //   childCount: Get.find<PeoplesController>()
                                        //       .peoples
                                        //       .length,
                                        // ),
                                      );
                      }),
                  const SizedBox(height: 50),
                  GetBuilder<PeoplesController>(
                      id: PeoplesControllerIDS.turnPage,
                      builder: (context) {
                        return Visibility(
                          visible: controller.peoples.isNotEmpty,
                          child: Align(
                            alignment: Alignment.bottomCenter,
                            child: TurnPage(
                              itemCount: fromFoucs
                                  ? controller.fromFoucsMaxpage
                                  : controller.maxpage,
                              currentIndex: fromFoucs
                                  ? controller.fromFoucsCurrentPage
                                  : controller.currentPage,
                              onTap: (index, total) => controller
                                  .turnPage(index, total, fromFoucs: fromFoucs),
                            ),
                          ),
                        );
                      }),
                  const SizedBox(height: 50),
                ],
              ),
            ),
          ),
          FloatButton(controller: controller),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return KeepAliveWidget(
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final crossAxisCount = (constraints.maxWidth / 570).round();
          return GetBuilder<PeoplesController>(
            init: PeoplesController(),
            id: "peoples",
            builder: (_) {
              return _buildView(context, crossAxisCount);
            },
          );
        },
      ),
    );
  }

  bool _isEmpty() {
    return fromFoucs
        ? Get.find<PeoplesController>().fromFoucsPeoples.isEmpty
        : Get.find<PeoplesController>().peoples.isEmpty;
  }

  int _status() {
    return fromFoucs
        ? Get.find<PeoplesController>().fromFoucsCode
        : Get.find<PeoplesController>().code;
  }
}
