import 'dart:convert';

import 'package:fleather/fleather.dart';
import 'package:flutter/material.dart';
import 'package:police/common/widget/image.dart';

import '../api/apis.dart';
import '../api/uplaod_image_api.dart';
import '../dialog.dart';
import '../util/image_picker.dart';

class RichEditor extends StatefulWidget {
  final String label;
  final List<dynamic>? document;
  // final Function(List<Operation> results) onSave;
  final Function(List<dynamic> document)? onDocumentChange;
  const RichEditor(
      {super.key,
      required this.label,
      this.document,
      // required this.onSave,
      this.onDocumentChange});

  @override
  State<RichEditor> createState() => _RichEditorState();
}

class _RichEditorState extends State<RichEditor> {
  late FleatherController controller;

  @override
  void initState() {
    super.initState();
    controller = FleatherController(
        document: widget.document == null || widget.document!.isEmpty
            ? null
            : ParchmentDocument.fromJson(widget.document!));
    controller.document.changes.listen((event) {
      widget.onDocumentChange?.call(controller.document.toJson());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                widget.label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ),
            // InkWell(
            //   child: Container(
            //     decoration: BoxDecoration(
            //       borderRadius: BorderRadius.circular(2),
            //       color: Theme.of(context).canvasColor,
            //     ),
            //     padding: const EdgeInsets.all(8.0),
            //     child: const Icon(Icons.save, size: 16),
            //   ),
            //   onTap: () {
            //     widget.onSave(controller.document.toJson());
            //   },
            // ),
          ],
        ),
        Expanded(
            child: FleatherTheme(
          data: FleatherThemeData(
            bold: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 18), // 加粗
            italic: const TextStyle(fontStyle: FontStyle.italic), // 斜体
            underline:
                const TextStyle(decoration: TextDecoration.underline), // 下划线
            strikethrough:
                const TextStyle(decoration: TextDecoration.lineThrough),
            inlineCode: InlineCodeThemeData(
                style: const TextStyle(color: Colors.white)),
            link: const TextStyle(color: Colors.blue),
            paragraph: TextBlockTheme(
              style: const TextStyle(color: Colors.white, fontSize: 18),
              spacing: VerticalSpacing.zero(),
            ),
            heading1: TextBlockTheme(
              style: const TextStyle(color: Colors.white),
              spacing: VerticalSpacing.zero(),
            ),
            heading2: TextBlockTheme(
              style: const TextStyle(color: Colors.white),
              spacing: VerticalSpacing.zero(),
            ),
            heading3: TextBlockTheme(
              style: const TextStyle(color: Colors.white),
              spacing: VerticalSpacing.zero(),
            ),
            heading4: TextBlockTheme(
              style: const TextStyle(color: Colors.white),
              spacing: VerticalSpacing.zero(),
            ),
            heading5: TextBlockTheme(
              style: const TextStyle(color: Colors.white),
              spacing: VerticalSpacing.zero(),
            ),
            heading6: TextBlockTheme(
              style: const TextStyle(color: Colors.white),
              spacing: VerticalSpacing.zero(),
            ),
            lists: TextBlockTheme(
              style: const TextStyle(color: Colors.white),
              spacing: VerticalSpacing.zero(),
            ),
            quote: TextBlockTheme(
              style: const TextStyle(color: Colors.white),
              spacing: VerticalSpacing.zero(),
            ),
            code: TextBlockTheme(
              style: const TextStyle(color: Colors.white),
              spacing: VerticalSpacing.zero(),
            ),
          ),
          child: FleatherEditor(
            controller: controller,
            expands: true,
            embedBuilder: (context, node) {
              if (node.value.type == 'image') {
                String source = node.value.data['source'];
                if (source.startsWith('http')) {
                  return ImageWidget.network(
                    source,
                    alignment: Alignment.centerLeft,
                    height: 200,
                    fit: BoxFit.contain,
                    addhost: false,
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: ImageWidget.network(
                              source,
                              addhost: false,
                              fit: BoxFit.contain,
                            ),
                          );
                        },
                      );
                    },
                  );
                }
                return ImageWidget.file(
                  node.value.data['source'],
                  alignment: Alignment.centerLeft,
                  height: 200,
                  fit: BoxFit.contain,
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: ImageWidget.file(
                            node.value.data['source'],
                            fit: BoxFit.contain,
                          ),
                        );
                      },
                    );
                  },
                );
              }
              return SizedBox();
            },
            // decoration: const InputDecoration(border: InputBorder.none),
          ),
        )),
        FleatherToolbar(children: [
          ColorButton(
            controller: controller,
            attributeKey: ParchmentAttribute.foregroundColor,
            nullColorLabel: '默认',
            builder: (context, value) {
              Color effectiveColor = value ?? Colors.white; // 自定义默认颜色
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.color_lens, size: 16),
                  Container(
                    width: 18,
                    height: 4,
                    decoration: BoxDecoration(color: effectiveColor),
                  )
                ],
              );
            },
          ),
          const SizedBox(width: 2),
          InkWell(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: Theme.of(context).canvasColor,
              ),
              padding: const EdgeInsets.all(8.0),
              child: const Icon(Icons.photo, size: 16),
            ),
            onTap: () async {
              var file = await imagePicker();
              if (file != null) {
                Dialogs.showLoadingDialog(context);
                // 上传图片
                Map<String, dynamic> result = await uploadImage(file.path);
                Navigator.pop(context);
                // 插入图片到光标位置
                controller.document.insert(
                    controller.selection.baseOffset,
                    BlockEmbed.image(
                        '${Apis.baseUrl}/assets/${result['files'][0]}'));
                // 换行
                controller.document
                    .insert(controller.selection.baseOffset + 1, '\n');
              }
            },
          ),

          const SizedBox(width: 10),
          UndoRedoButton.redo(controller: controller), // 重做
          const SizedBox(width: 2),
          UndoRedoButton.undo(controller: controller), // 撤销
        ])
      ],
    );
  }
}
