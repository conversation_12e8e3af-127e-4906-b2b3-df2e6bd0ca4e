import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:police/common/model/house_model.dart';

import '../../../common/widget/check_box.dart';
import '../../../common/widget/radio.dart';
import '../controller.dart';

class SafeInfo extends StatelessWidget {
  const SafeInfo({super.key, required this.detail});

  final HouseModel detail;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HousedetailController>(
        id: HousedetailIDS.safeInfo,
        builder: (contro) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '治安隐患排查情况',
                style: TextStyle(fontSize: 40, color: Color(0xffffffff)),
              ),
              const SizedBox(height: 10),
              RadioWidget(
                label: '是否存在治安安全隐患',
                radios: const ['是', '否'],
                valueGroup: detail.securityRisk ? '是' : '否',
                onChanged: (val) {
                  detail.securityRisk = val == '是';
                  contro.update([HousedetailIDS.safeInfo]);
                },
              ),
              const SizedBox(height: 10),
              Visibility(
                visible: detail.securityRisk,
                child: Wrap(
                  spacing: 20,
                  runSpacing: 20,
                  children: [
                    CheckBoxWidegt(
                      label: '矛盾纠纷类',
                      chekcs: const [
                        '涉医患',
                        '教育文旅',
                        '物业邻里',
                        '婚恋家庭',
                        '资源经济',
                        '征地拆迁',
                        '劳资涉企',
                        '其他'
                      ],
                      checked: detail.securityConflicts,
                      otherStr: detail.securityConflictsAdditional ?? '',
                      onChanged: (val, text) {
                        if (val) {
                          detail.securityConflicts.add(text);
                        } else {
                          detail.securityConflicts.remove(text);
                        }
                        contro.update([HousedetailIDS.safeInfo]);
                      },
                      onChangedOther: (text) {
                        detail.securityConflictsAdditional = text;
                      },
                    ),
                    CheckBoxWidegt(
                      label: '公共安全类',
                      chekcs: const [
                        '网络安全',
                        '递物流安全',
                        '危物品管理',
                        '消防安全',
                        '食品药品安全',
                        '涉黄赌毒',
                        '涉邪教',
                        '其他'
                      ],
                      checked: detail.securityPublicSafety,
                      otherStr: detail.securityPublicSafetyAdditional ?? '',
                      onChanged: (val, text) {
                        if (val) {
                          detail.securityPublicSafety.add(text);
                        } else {
                          detail.securityPublicSafety.remove(text);
                        }
                        contro.update([HousedetailIDS.safeInfo]);
                      },
                      onChangedOther: (text) {
                        detail.securityPublicSafetyAdditional = text;
                      },
                    ),
                    CheckBoxWidegt(
                      label: '处置情况',
                      chekcs: const [
                        '当面宣教提示警告',
                        '通告社区民警、村(居)委会',
                        '走访调查',
                        '复核整改',
                        '出具《家庭暴力告知书》',
                        '下达《治安隐患整改通知书》',
                        '下达《风险预警提示函》',
                        '建议属地派出所移交乡镇政府(街道办事处)及相关职能部门',
                        '其他'
                      ],
                      checked: detail.securityDisposalStatus,
                      otherStr: detail.securityDisposalStatusAdditional ?? '',
                      onChanged: (val, text) {
                        if (val) {
                          detail.securityDisposalStatus.add(text);
                        } else {
                          detail.securityDisposalStatus.remove(text);
                        }
                        contro.update([HousedetailIDS.safeInfo]);
                      },
                      onChangedOther: (text) {
                        detail.securityDisposalStatusAdditional = text;
                      },
                    ),
                  ],
                ),
              )
            ],
          );
        });
  }
}
