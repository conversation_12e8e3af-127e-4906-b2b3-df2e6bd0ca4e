import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/village_model/village_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/page/village/controller.dart';
import 'package:police/page/villageDetail/controller.dart';

import '../../../common/widget/float_square_button.dart';
import '../../villageDetail/view.dart';

class FloatButton extends StatelessWidget {
  const FloatButton({super.key, required this.controller});

  final VillageController controller;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.all(36),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FloatSquareButton(
              onTap: () => controller.getVillages(),
              icon: Icons.refresh,
            ),
            const SizedBox(height: 10),
            FloatSquareButton(
              onTap: () {
                Get.put(VillagedetailController()).villageDetail =
                    VillageModel();
                jump2page(
                  const VillagedetailPage(),
                  onBack: (results) {
                    if (results != null) {
                      controller.villages.add(results);
                      controller.update([VillageControllerIDS.village]);
                    }
                  },
                );
              },
              icon: Icons.add,
            ),
          ],
        ),
      ),
    );
  }
}
