import 'package:dio/dio.dart';
import '../model/village_area_model/village_area_model.dart';
import '../util/https.dart';
import 'apis.dart';

class AreaApi {
  /// 获取区域列表
  static Future<List<VillageAreaModel>> getAreas(int id,
      {required Function() fail}) async {
    List<VillageAreaModel> areas = [];
    Response results = await Https.get(
      Apis.area,
      // contentType: 'application/json',
      queryParameters: {'village_id': id},
      fail: fail(),
    );
    for (var area in results.data) {
      areas.add(VillageAreaModel.fromJson(area));
    }
    return areas;
  }

  /// 创建区域
  // static Future<VillageAreaModel> createArea(
  //     {required int villageId, required String name}) async {
  //   Response result = await Https.post(Apis.area, data: {
  //     'village_id': villageId,
  //     'name': name,
  //     'color': '',
  //   });

  //   return VillageAreaModel.fromJson(result.data);
  // }

  /// 创建区域
  static Future<VillageAreaModel> createArea(
      {required Map<String, dynamic> newArea}) async {
    // List<VillageAreaModel> areas = [];
    Response results = await Https.post(
      Apis.area,
      data: newArea,
      contentType: 'application/json',
      pageBack: true,
    );
    // for (var area in results.data) {
    //   areas.add(VillageAreaModel.fromJson(area));
    // }

    return VillageAreaModel.fromJson(results.data);
  }

  /// 获取区域详情
  static Future getAreaDetail(int id,
      {required Function(VillageAreaModel) success}) async {
    await Https.get(
      '${Apis.area}$id',
      // success: (results) {
      //   success(VillageAreaModel.fromJson(results.data));
      // },
    );
  }

  /// 修改区域详情
  static Future<VillageAreaModel> patchAreaDetail(
      {required Map<String, dynamic> detail, required int id}) async {
    Response result =
        await Https.patch('${Apis.area}/$id', data: detail, pageBack: true);

    return VillageAreaModel.fromJson(result.data);
  }

  /// 删除区域
  static deleteArea(int id) async {
    await Https.delete('${Apis.area}/$id', pageBack: true);
  }
}
