import 'package:dio/dio.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/model/village_unit_model.dart';
import 'package:get/get.dart' hide Response;
import 'package:police/page/unit/controller.dart';

import '../util/https.dart';
import 'apis.dart';

class UnitApi {
  /// 获取单元列表
  static Future<List<VillageUnitModel>> getUnits(int buildId,
      {int page = 1, required Function(Response) fail}) async {
    List<VillageUnitModel> units = [];

    Response results = await Https.get(
      Apis.unit,
      queryParameters: {'village_building_id': buildId, 'page': page},
      fail: fail,
    );

    for (var unit in results.data['items']) {
      units.add(VillageUnitModel.fromJson(unit));
    }

    Get.find<UnitController>().maxpages = results.data['pages'];

    return units;
  }

  /// 创建单元
  static Future<VillageUnitModel> createUnit({
    required Map<String, dynamic> detail,
  }) async {
    Response result = await Https.post(
      Apis.unit,
      data: detail,
      contentType: 'application/json',
      pageBack: true,
    );

    return VillageUnitModel.fromJson(result.data);
  }

  /// 获取单元详情
  static Future<VillageUnitModel> getUnitDetail(int id) async {
    Response result = await Https.get('${Apis.unit}/$id');

    return VillageUnitModel.fromJson(result.data);
  }

  /// 修改单元详情
  static Future<VillageUnitModel> patchUnitDetail(
      {required Map<String, dynamic> detail, required int id}) async {
    Response result = await Https.patch(
      '${Apis.unit}/$id',
      data: detail,
      pageBack: true,
    );

    return VillageUnitModel.fromJson(result.data);
  }

  /// 删除单元
  static deleteUnit(int id, {bool pageBack = true}) async {
    await Https.delete('${Apis.unit}/$id', pageBack: pageBack);
  }
}
