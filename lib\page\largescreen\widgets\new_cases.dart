import 'package:date_format/date_format.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/big_screen_model/latest_alarm.dart';
import 'package:police/page/largescreen/controller.dart';

import '../../../common/model/case_model.dart';
import '../../../common/util/date_format.dart';
import '../../../common/widget/corner_card.dart';
import '../../cases/controller.dart';

class NewCases extends StatelessWidget {
  const NewCases({super.key});

  @override
  Widget build(BuildContext context) {
    TextStyle style = const TextStyle(color: Color(0xD8FFFFFF));
    return GetBuilder<LargescreenController>(
        id: LargescernnIDS.newcases,
        builder: (controller) {
          return CornerCard(
            child: Column(
              children: [
                _buildTableHeader(controller, style),
                _buildTableData(controller, style),
              ],
            ),
          );
        });
  }

  Widget _buildTableData(LargescreenController controller, TextStyle style) {
    List<LatestAlarm> latestAlarm = controller.bigscreenData.latestAlarms!;
    return Column(
      children: List.generate(latestAlarm.length, (index) {
        return Container(
          color: index % 2 == 0 ? const Color(0xd2072354) : null,
          child: Row(
            children: List.generate(controller.headers.length, (i) {
              return Expanded(
                child: Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Text(
                    i == 0
                        ? latestAlarm[index].title ?? ''
                        : i == 1
                            ? latestAlarm[index].types ?? ''
                            : i == 2
                                ? latestAlarm[index].sort ?? ''
                                : i == 3
                                    ? _getVillageName(
                                        latestAlarm[index].villageId!)
                                    : latestAlarm[index].occurrenceTime == null
                                        ? ''
                                        : dateFormat(
                                            latestAlarm[index].occurrenceTime!,
                                            format: [yyyy, '-', mm, '-', dd]),
                    style: style,
                  ),
                ),
              );
            }),
          ),
        );
      }),
    );
  }

  Widget _buildTableHeader(LargescreenController controller, TextStyle style) {
    return Row(
      children: controller.headers.map((item) {
        return Expanded(
            child: Container(
          padding: const EdgeInsets.only(bottom: 16),
          alignment: Alignment.center,
          color: const Color(0xd2072354).withOpacity(.1),
          child: Text(item, style: style),
        ));
      }).toList(),
    );
  }

  /// 根据id获取村庄名
  String _getVillageName(int id) {
    String name = '';
    CasesController controller = Get.put(CasesController());
    for (var village in controller.villages) {
      if (id == village.id) {
        name = village.name ?? '';
        break;
      }
    }

    return name;
  }
}
