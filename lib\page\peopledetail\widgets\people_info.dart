import 'package:date_format/date_format.dart';
import 'package:flustars_flutter3/flustars_flutter3.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/float_square_button.dart';
import 'package:police/common/widget/form_input.dart';
import 'package:police/common/widget/form_text.dart';
import 'package:police/common/widget/image_upload.dart';
import 'package:police/page/housedetail/index.dart';
import 'package:police/page/peopledetail/controller.dart';

import '../../../common/model/people_detail_model.dart';
import '../../../common/util/date_format.dart';

class PeopleInfo extends StatelessWidget {
  const PeopleInfo(
      {super.key,
      required this.controller,
      required this.detail,
      this.fromHouse = false});

  final PeopledetailController controller;
  final PeopleDetailModel detail;
  final bool fromHouse;

  @override
  Widget build(BuildContext context) {
    TextEditingController idCardContro =
        TextEditingController(text: detail.idNumber);

    return GetBuilder<PeopledetailController>(
        id: PeopledetailControllerIDS.peopleInfo,
        builder: (context) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('基本人员信息',
                  style: TextStyle(
                    color: Color(0xc2ffffff),
                    fontSize: 40,
                    fontWeight: FontWeight.bold,
                  )),
              const SizedBox(height: 20),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  FormInput(
                                    label: '姓名',
                                    defaultText: detail.name ?? '',
                                    maxLength: 20,
                                    onChanged: (val) {
                                      detail.name = val.trim();
                                      _updata();
                                    },
                                  ),
                                  GetBuilder<PeopledetailController>(
                                      id: PeopledetailControllerIDS.gender,
                                      builder: (context) {
                                        return FormInput(
                                          label: '性别',
                                          enabled: false,
                                          defaultText: detail.gender ?? '',
                                        );
                                      }),
                                  FormInput(
                                    label: '联系电话',
                                    maxLength: 18,
                                    defaultText: detail.telephone ?? '',
                                    onChanged: (val) {
                                      detail.telephone = val.trim();
                                      _updata();
                                    },
                                  ),
                                  FormInput(
                                    label: '职业',
                                    maxLength: 10,
                                    defaultText: detail.profession ?? '',
                                    onChanged: (val) {
                                      detail.profession = val.trim();
                                      _updata();
                                    },
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  GetBuilder<PeopledetailController>(
                                      id: PeopledetailControllerIDS.birthDate,
                                      builder: (context) {
                                        return FormInput(
                                          label: '出生日期',
                                          enabled: false,
                                          defaultText: detail.birthDate ?? '',
                                        );
                                      }),
                                  GetBuilder<PeopledetailController>(
                                      id: PeopledetailControllerIDS.idCard,
                                      builder: (context) {
                                        return FormInput(
                                          label: '证件号',
                                          controller: idCardContro,
                                          defaultText: detail.idNumber ?? '',
                                          maxLength: 18,
                                          // errorText: controller.idCardErrorText,
                                          onChanged: (val) {
                                            // _idCardIsTrue(val);
                                            _setBirthDateAndGender(val);
                                            detail.idNumber = val.trim();
                                            _updata();
                                          },
                                        );
                                      }),
                                  FormInput(
                                    label: '文化程度',
                                    maxLength: 10,
                                    defaultText: detail.education ?? '',
                                    onChanged: (val) {
                                      detail.education = val.trim();
                                      _updata();
                                    },
                                  ),
                                  Visibility(
                                    visible: !detail.isOrganize!,
                                    child: FormInput(
                                      label: '关注人员',
                                      maxLength: 20,
                                      defaultText: detail.personInterest ?? '',
                                      onChanged: (val) {
                                        detail.personInterest = val.trim();
                                        _updata();
                                      },
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Visibility(
                                    visible: detail.isKeyPersonnel,
                                    child: Row(
                                      children: [
                                        const Text(
                                          '等级评估:',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 17,
                                          ),
                                        ),
                                        const SizedBox(width: 10),
                                        Expanded(
                                            child: Row(
                                          children: List.generate(3, (index) {
                                            return GestureDetector(
                                              onTap: () {
                                                detail.level = index;
                                                controller.update([
                                                  PeopledetailControllerIDS
                                                      .peopleInfo
                                                ]);
                                              },
                                              child: Container(
                                                width: 20,
                                                height: 20,
                                                margin: const EdgeInsets.only(
                                                    right: 10),
                                                decoration: BoxDecoration(
                                                  border: Border.all(
                                                    color: Colors.white,
                                                    width: 1,
                                                  ),
                                                  color: index == 0
                                                      ? Colors.green
                                                      : index == 1
                                                          ? Colors.yellow
                                                          : Colors.red,
                                                ),
                                                alignment: Alignment.center,
                                                child: Visibility(
                                                  visible:
                                                      index == detail.level!,
                                                  child: const Icon(
                                                    Icons.check,
                                                    color: Colors.black,
                                                    size: 15,
                                                  ),
                                                ),
                                              ),
                                            );
                                          }),
                                        )),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: GetBuilder<PeopledetailController>(
                      id: PeopledetailControllerIDS.photo,
                      builder: (context) => ImageUploadWidget(
                        width: 130,
                        height: 130 * 4 / 3,
                        photoUrl: detail.photo ?? '',
                        onUploaded: (url) {
                          detail.photo = url;
                          controller.update([PeopledetailControllerIDS.photo]);
                          _updata();
                        },
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: FormText(
                      labelText: '现住地址',
                      valueText: controller.detail.address ?? '',
                      fontSize: 17,
                    ),
                  ),
                  Visibility(
                    visible: detail.id != null,
                    child: FloatSquareButton(
                      onTap: () {
                        if (controller.detail.id != null) {
                          if (fromHouse) {
                            pageback();
                            return;
                          }
                          jump2page(HousedetailPage(
                            detail: controller.detail,
                            unitId: controller.detail.villageUnitId!,
                          ));
                        } else {
                          showToast('该人员未加入家庭');
                        }
                      },
                      icon: Icons.home,
                      color: Colors.blue.withOpacity(.3),
                    ),
                  ),
                ],
              ),
              FormInput(
                maxLength: 50,
                label: '户籍地址',
                defaultText: detail.residenceAddress ?? '',
                onChanged: (val) {
                  detail.residenceAddress = val.trim();
                  _updata();
                },
              ),
            ],
          );
        });
  }

  /// 判断身份证格式
  void _idCardIsTrue(String val) {
    if (!RegexUtil.isIDCard(val)) {
      controller.idCardErrorText = '身份证格式错误';
    } else {
      controller.idCardErrorText = '';
    }
    controller.update([PeopledetailControllerIDS.idCard]);
  }

  /// 根据身份证取生日及性别
  void _setBirthDateAndGender(String val) {
    if (val.length > 6) {
      String brith = val.substring(6, 14);
      try {
        DateTime dateTime = DateTime.parse(brith);
        detail.birthDate =
            dateFormat(dateTime, format: [yyyy, '-', mm, '-', dd]);
      } catch (e) {
        detail.birthDate = '';
      }
      controller.update([PeopledetailControllerIDS.birthDate]);
    }
    if (val.length >= 17) {
      int gender = int.parse(val.substring(16, 17));
      if (gender % 2 == 0) {
        detail.gender = '女';
      } else {
        detail.gender = '男';
      }
      controller.update([PeopledetailControllerIDS.gender]);
    }
  }

  void _updata() {
    // controller.isSave = false;
  }
}
