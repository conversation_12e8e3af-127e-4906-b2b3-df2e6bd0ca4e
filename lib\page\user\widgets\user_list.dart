import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/api/uplaod_image_api.dart';
import 'package:police/common/api/user_api.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/model/user_info_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/util/image_picker.dart';
import 'package:police/common/widget/corner_card.dart';
import 'package:police/common/widget/empty_widget.dart';
import 'package:police/common/widget/float_square_button.dart';
import 'package:police/common/widget/form_input.dart';
import 'package:police/common/widget/form_text.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/common/widget/slide_animation_widget.dart';
import 'package:police/page/home/<USER>';
import 'package:police/page/login/controller.dart';
import 'package:police/page/user/controller.dart';

class UserList extends StatelessWidget {
  const UserList({super.key});

  @override
  Widget build(BuildContext context) {
    UserController controller = Get.find<UserController>();

    return Visibility(
      visible: controller.userInfo.permissions == 'advanced',
      child: GetBuilder<UserController>(
        id: 'list',
        builder: (contro) {
          return controller.code == 0
              ? const LoadingWidget()
              : controller.code == 404
                  ? const HttpErrorWidget()
                  : controller.code == 200 && controller.userList.isEmpty
                      ? const EmptyWidget()
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Text(
                                  '用户列表',
                                  style: TextStyle(
                                      fontSize: 20, color: Colors.white),
                                ),
                                const Spacer(),
                                FloatSquareButton(
                                  icon: Icons.add,
                                  color: Colors.transparent,
                                  onTap: () =>
                                      _showUserDialog(context, controller),
                                )
                              ],
                            ),
                            const SizedBox(height: 20),
                            Expanded(
                              child: ListView.separated(
                                separatorBuilder: (_, __) =>
                                    const SizedBox(height: 10),
                                itemCount: controller.userList.length,
                                itemBuilder: (_, index) {
                                  UserInfoModel info =
                                      controller.userList[index];
                                  return SlideAnimationWidget(
                                    actions: _buildItemButton(
                                        controller, context, info),
                                    child: Row(
                                      children: [
                                        ClipOval(
                                          child: ImageWidget.network(
                                            info.avatar ?? '',
                                            width: 50,
                                            height: 50,
                                          ),
                                        ),
                                        const SizedBox(width: 10),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              info.username ?? '',
                                              style: const TextStyle(
                                                  fontSize: 17,
                                                  color: Colors.white),
                                            ),
                                            Row(
                                              children: [
                                                Text(
                                                  info.isActive ? '正常' : '冻结',
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    color: info.isActive
                                                        ? const Color(
                                                            0xd8cfe0ff)
                                                        : Colors.red,
                                                  ),
                                                ),
                                                const SizedBox(width: 10),
                                                FormText(
                                                  labelText: '姓名',
                                                  valueText: info.name ?? '',
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                    color: Color(0xd8cfe0ff),
                                                  ),
                                                ),
                                                const SizedBox(width: 10),
                                                FormText(
                                                  labelText: '警号',
                                                  valueText:
                                                      info.policeNumber ?? '',
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                    color: Color(0xd8cfe0ff),
                                                  ),
                                                ),
                                                const SizedBox(width: 10),
                                                Text(
                                                  info.isCommand ? '指挥人员' : '',
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    color: Colors.blue
                                                        .withOpacity(.7),
                                                  ),
                                                ),
                                              ],
                                            )
                                          ],
                                        )
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        );
        },
      ),
    );
  }

  List<Widget> _buildItemButton(
      UserController controller, BuildContext context, UserInfoModel info) {
    return [
      InkWell(
        onTap: () => controller.freezeUser(context, info),
        child: Container(
          height: 50,
          alignment: Alignment.center,
          child: const Icon(
            Icons.block,
            color: Colors.red,
          ),
        ),
      ),
      InkWell(
        onTap: () => _showUserDialog(
          context,
          controller,
          addUser: false,
          info: info,
        ),
        child: Container(
          height: 50,
          alignment: Alignment.center,
          child: const Icon(
            Icons.password,
            color: Colors.blue,
          ),
        ),
      ),
      Switch(
        value: info.isCommand,
        inactiveTrackColor: Colors.grey.withOpacity(0.3), // 未选中时轨道颜色
        activeColor: Colors.blue.withOpacity(.7),
        onChanged: (val) async {
          Dialogs.showLoadingDialog(context);
          await UserApi.setCommander(
            id: info.id,
            isCommand: val,
            fail: (result) {},
          );
          info.isCommand = val;
          if (val) {
            Get.find<HomeController>().sir.add(info);
          } else {
            Get.find<HomeController>().sir.remove(info);
          }
          Get.find<HomeController>().update(['sir']);
          controller.update(['list']);
        },
      )
    ];
  }

  /// 修改头像
  Future<void> _updateAvatar(BuildContext context, UserInfoModel info,
      UserController controller) async {
    XFile? file = await imagePicker();
    if (file != null) {
      Dialogs.showLoadingDialog(context);
      final result = await uploadImage(
        file.path,
        fail: (p0) {
          pageback();
          return;
        },
      );
      info.avatar = result['files'][0];
      // await controller.updateAvatar(context, info.avatar!);
      // pageback();
      controller.update(['list']);
    }
  }

  /// 修改密码/新增用户弹窗
  _showUserDialog(
    BuildContext context,
    UserController controller, {
    bool addUser = true,
    UserInfoModel? info,
  }) {
    TextEditingController username = TextEditingController();
    TextEditingController password = TextEditingController();
    TextEditingController password2 = TextEditingController();

    showDialog(
        context: context,
        builder: (_) {
          return UnconstrainedBox(
            child: CornerCard(
              width: 500,
              padding: const EdgeInsets.all(30),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    addUser ? '新增账号' : '更改密码',
                    style: const TextStyle(fontSize: 20, color: Colors.white),
                  ),
                  const SizedBox(height: 20),
                  Visibility(
                    visible: addUser,
                    child: Column(
                      children: [
                        InputText(
                          lableText: '账号',
                          maxLength: 16,
                          controller: username,
                          isDense: false,
                          focusedBorder: _inputBorder(),
                          enabledBorder: _inputBorder(),
                          labelStyle: const TextStyle(
                            color: Color(0xff00caee), // 设置标签文本的颜色
                          ),
                          fillColor: const Color(0xff084a89),
                        ),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                  InputText(
                    lableText: '密码',
                    maxLength: 16,
                    controller: password,
                    isDense: false,
                    obscureText: true,
                    focusedBorder: _inputBorder(),
                    enabledBorder: _inputBorder(),
                    labelStyle: const TextStyle(
                      color: Color(0xff00caee), // 设置标签文本的颜色
                    ),
                    fillColor: const Color(0xff084a89),
                  ),
                  const SizedBox(height: 10),
                  InputText(
                    lableText: '重复密码',
                    maxLength: 16,
                    controller: password2,
                    isDense: false,
                    obscureText: true,
                    focusedBorder: _inputBorder(),
                    enabledBorder: _inputBorder(),
                    labelStyle: const TextStyle(
                      color: Color(0xff00caee), // 设置标签文本的颜色
                    ),
                    fillColor: const Color(0xff084a89),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: NormolButton(
                          lable: '取消',
                          height: 50,
                          color: Colors.grey.withOpacity(.3),
                          onTap: () => pageback(),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: NormolButton(
                          lable: '确认',
                          height: 50,
                          onTap: () async {
                            if (username.text.trim().isEmpty && addUser) {
                              showToast('账号不能为空');
                              return;
                            }
                            if (password.text.trim().isEmpty) {
                              showToast('密码不能为空');
                              return;
                            }
                            if (password.text.trim() != password2.text.trim()) {
                              showToast('两次密码不一致');
                              return;
                            }
                            pageback();
                            if (addUser) {
                              LoginController loginController = Get.find();
                              await loginController.signup(context,
                                  username.text, password.text, password2.text);
                              controller.getUserList();
                            } else {
                              controller.changePWDForAdmin(
                                context,
                                password: password.text.trim(),
                                id: info!.id!,
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        });
  }

  OutlineInputBorder _inputBorder() {
    return const OutlineInputBorder(
      borderSide: BorderSide(
        color: Color(0xff00bcdd), // 聚焦时的边框颜色
        width: 2.0,
      ),
    );
  }
}
