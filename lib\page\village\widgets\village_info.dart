import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/model/village_model/village_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/image.dart';
import 'package:police/common/widget/scale_animated_widget.dart';
import 'package:police/page/village/index.dart';
import 'package:police/page/villageDetail/index.dart';

class VillageInfo extends StatelessWidget {
  const VillageInfo(
      {super.key, required this.villageInfo, required this.index});

  final VillageModel villageInfo;
  final int index;

  @override
  Widget build(BuildContext context) {
    return ScaleAnimatedWidget(
      child: InkWell(
        onTap: () => jump2page(
          VillagedetailPage(id: villageInfo.id!),
          onBack: (result) {
            if (result == 'remove') {
              Get.find<VillageController>().villages.remove(villageInfo);
              Get.find<VillageController>()
                  .update([VillageControllerIDS.village]);
              return;
            }
          },
        ),
        child: Container(
          padding: const EdgeInsets.all(25),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/pic/234-2.png'),
              fit: BoxFit.fill,
            ),
            // borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Opacity(
                  opacity: 0.8, // 设置透明度，0.0 是完全透明，1.0 是完全不透明
                  child: ImageWidget.network(
                    villageInfo.cover ?? '',
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10),
                    ),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black12,
                        Colors.black26,
                        Colors.black45,
                        Colors.black54,
                        Colors.black.withOpacity(.7),
                      ],
                    )),
                height: 100,
                child: Text(
                  villageInfo.name ?? '',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // Align(
              //   alignment: Alignment.topRight,
              //   child: FloatSquareButton(
              //     color: const Color(0x883875a6),
              //     onTap: () => Get.find<VillageController>().deleteVillage(
              //       context,
              //       id: villageInfo.id!,
              //       name: villageInfo.name!,
              //       index: index,
              //     ),
              //     icon: Icons.close,
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
