import 'package:dio/dio.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/util/https.dart';

/// 上传图片
Future<Map<String, dynamic>> uploadImage(
  String filePath, {
  Function(int now, int total)? onSendProgress,
  Function(Response)? fail,
}) async {
  Response result = await Https.post(
    Apis.image,
    data: FormData.fromMap(
      {'files': await MultipartFile.fromFile(filePath)},
    ),
    onSendProgress: (count, total) => onSendProgress?.call(count, total),
    fail: (p0) {
      // showToast('上传失败');
      fail?.call(p0);
    },
  );

  return result.data;
}
