class PeopleDetailModel {
  int? id;
  String? photo;
  String? idNumber;
  String? name;
  String? gender;
  String? birthDate;
  DateTime? createTime;
  DateTime? updateTime;
  String? telephone;
  String? residenceAddress;
  String? currentAddress;
  bool residenceSeparation;
  String? politicalStatus;
  String? maritalStatus;
  String? education;
  String? belief;
  String? veteranStatus;
  String? profession;
  String? graphics;
  String? personInterest;
  bool isKeyPersonnel;
  String? serialNumber;
  String? formerName;
  String? nickname;
  String? employer;
  String? healthStatus;
  String? nationality;
  String? personType;
  String? strengths;
  bool? wechatGroup;
  String? riskAssessmentFamilyStability;
  String? socialSupportStatus;
  String? personalActivityRange;
  String? realPerformance;
  String? overallAssessment;
  String? height;
  String? weight;
  String? skinColor;
  String? accent;
  String? hairstyle;
  String? faceShape;
  String? eyes;
  bool? scars;
  bool? tattoos;
  bool? isOrganize;
  String? otherCharacteristics;
  String? wechat;
  String? alipay;
  String? qq;
  String? driverLicense;
  String? passport;
  String? fingerprint;
  String? dna;
  String? voiceprint;
  String? iris;
  String? criminalRecord;
  String? resume;
  String? socialRelations;
  String? sourceIncome;
  String? associates;
  String? notes;
  String? actualResidence; // 实际居住地
  List<dynamic>? work;
  int? level;

  PeopleDetailModel({
    this.id,
    this.photo,
    this.idNumber,
    this.name,
    this.gender,
    this.birthDate,
    this.createTime,
    this.updateTime,
    this.telephone,
    this.residenceAddress,
    this.currentAddress,
    this.residenceSeparation = false,
    this.politicalStatus = '群众',
    this.maritalStatus = '未婚',
    this.education,
    this.belief = '无',
    this.veteranStatus = '未服',
    this.profession,
    this.graphics,
    this.personInterest,
    this.isKeyPersonnel = false,
    this.serialNumber,
    this.formerName,
    this.nickname,
    this.employer,
    this.healthStatus,
    this.nationality,
    this.personType,
    this.strengths,
    this.wechatGroup = false,
    this.riskAssessmentFamilyStability,
    this.socialSupportStatus,
    this.personalActivityRange,
    this.realPerformance,
    this.overallAssessment,
    this.height,
    this.weight,
    this.skinColor,
    this.accent,
    this.hairstyle,
    this.faceShape,
    this.eyes,
    this.scars = false,
    this.tattoos = false,
    this.otherCharacteristics,
    this.wechat,
    this.alipay,
    this.qq,
    this.driverLicense,
    this.passport,
    this.fingerprint,
    this.dna,
    this.voiceprint,
    this.iris,
    this.criminalRecord,
    this.resume,
    this.socialRelations,
    this.sourceIncome,
    this.associates,
    this.notes,
    bool? isOrganize,
    this.actualResidence,
    this.work = const [],
    int? level,
  })  : isOrganize = isOrganize ?? false,
        level = level ?? 0;

  factory PeopleDetailModel.fromJson(Map<String, dynamic> json) {
    return PeopleDetailModel(
      id: json['id'] as int?,
      photo: json['photo'] as String?,
      idNumber: json['id_number'] as String?,
      name: json['name'] as String?,
      gender: json['gender'] as String?,
      birthDate: json['birth_date'] as String?,
      createTime: json['create_time'] == null
          ? null
          : DateTime.parse(json['create_time'] as String),
      updateTime: json['update_time'] == null
          ? null
          : DateTime.parse(json['update_time'] as String),
      telephone: json['telephone'] as String?,
      residenceAddress: json['residence_address'] as String?,
      currentAddress: json['current_address'] as String?,
      residenceSeparation: json['residence_separation'] as bool,
      politicalStatus: json['political_status'] as String?,
      maritalStatus: json['marital_status'] as String?,
      education: json['education'] as String?,
      belief: json['belief'] as String?,
      veteranStatus: json['veteran_status'] as String?,
      profession: json['profession'] as String?,
      graphics: json['graphics'] as String?,
      personInterest: json['person_interest'] as String?,
      isKeyPersonnel: json['is_key_personnel'] as bool,
      serialNumber: json['serial_number'] as String?,
      formerName: json['former_name'] as String?,
      nickname: json['nickname'] as String?,
      employer: json['employer'] as String?,
      healthStatus: json['health_status'] as String?,
      nationality: json['nationality'] as String?,
      personType: json['person_type'] as String?,
      strengths: json['strengths'] as String?,
      wechatGroup: json['wechat_group'] as bool?,
      riskAssessmentFamilyStability:
          json['risk_assessment_family_stability'] as String?,
      socialSupportStatus: json['social_support_status'] as String?,
      personalActivityRange: json['personal_activity_range'] as String?,
      realPerformance: json['real_performance'] as String?,
      overallAssessment: json['overall_assessment'] as String?,
      height: json['height'] as String?,
      weight: json['weight'] as String?,
      skinColor: json['skin_color'] as String?,
      accent: json['accent'] as String?,
      hairstyle: json['hairstyle'] as String?,
      faceShape: json['face_shape'] as String?,
      eyes: json['eyes'] as String?,
      scars: json['scars'] as bool?,
      tattoos: json['tattoos'] as bool?,
      otherCharacteristics: json['other_characteristics'] as String?,
      wechat: json['wechat'] as String?,
      alipay: json['alipay'] as String?,
      qq: json['qq'] as String?,
      driverLicense: json['driver_license'] as String?,
      passport: json['passport'] as String?,
      fingerprint: json['fingerprint'] as String?,
      dna: json['dna'] as String?,
      voiceprint: json['voiceprint'] as String?,
      iris: json['iris'] as String?,
      criminalRecord: json['criminal_record'] as String?,
      resume: json['resume'] as String?,
      socialRelations: json['social_relations'] as String?,
      sourceIncome: json['source_income'] as String?,
      associates: json['associates'] as String?,
      notes: json['notes'] as String?,
      isOrganize: json['is_organize'] as bool? ?? false,
      actualResidence: json['actual_residence'] as String?,
      work: json['work'] as List<dynamic>?,
      level: json['level'] as int?,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'photo': photo,
        'id_number': idNumber,
        'name': name,
        'gender': gender,
        'birth_date': birthDate,
        'create_time': createTime?.toIso8601String(),
        'update_time': updateTime?.toIso8601String(),
        'telephone': telephone,
        'residence_address': residenceAddress,
        'current_address': currentAddress,
        'residence_separation': residenceSeparation,
        'political_status': politicalStatus,
        'marital_status': maritalStatus,
        'education': education,
        'belief': belief,
        'veteran_status': veteranStatus,
        'profession': profession,
        'graphics': graphics,
        'person_interest': personInterest,
        'is_key_personnel': isKeyPersonnel,
        'serial_number': serialNumber,
        'former_name': formerName,
        'nickname': nickname,
        'employer': employer,
        'health_status': healthStatus,
        'nationality': nationality,
        'person_type': personType,
        'strengths': strengths,
        'wechat_group': wechatGroup,
        'risk_assessment_family_stability': riskAssessmentFamilyStability,
        'social_support_status': socialSupportStatus,
        'personal_activity_range': personalActivityRange,
        'real_performance': realPerformance,
        'overall_assessment': overallAssessment,
        'height': height,
        'weight': weight,
        'skin_color': skinColor,
        'accent': accent,
        'hairstyle': hairstyle,
        'face_shape': faceShape,
        'eyes': eyes,
        'scars': scars,
        'tattoos': tattoos,
        'other_characteristics': otherCharacteristics,
        'wechat': wechat,
        'alipay': alipay,
        'qq': qq,
        'driver_license': driverLicense,
        'passport': passport,
        'fingerprint': fingerprint,
        'dna': dna,
        'voiceprint': voiceprint,
        'iris': iris,
        'criminal_record': criminalRecord,
        'resume': resume,
        'social_relations': socialRelations,
        'source_income': sourceIncome,
        'associates': associates,
        'notes': notes,
        'is_organize': isOrganize,
        'actual_residence': actualResidence,
        'work': work,
        'level': level,
      };
}
