import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class FormInput extends StatelessWidget {
  const FormInput({
    super.key,
    required this.label,
    this.color = Colors.white,
    this.fontSize = 17,
    this.defaultText = '',
    this.onChanged,
    this.width,
    this.maxLines,
    this.minLines = 1,
    this.textInputAction = TextInputAction.next,
    this.enabled = true,
    this.onTap,
    this.errorText = '',
    this.maxLength,
    this.controller,
    this.textAlign = TextAlign.start,
    this.isCollapsed = false,
    this.expandValue = false,
    this.maxHeight = 25,
    this.inputFormatters,
    this.onlyNumber = false,
    this.useMaxHeight = true,
    this.margin = const EdgeInsets.symmetric(vertical: 10),
    this.obscureText = false,
  });

  final TextEditingController? controller;
  final String label;
  final String defaultText;
  final Color? color;
  final double fontSize;
  final Function(String text)? onChanged;
  final double? width;
  final int? maxLines;
  final int minLines;
  final TextInputAction? textInputAction;
  final bool enabled;
  final Function()? onTap;
  final String errorText;
  final int? maxLength;
  final TextAlign textAlign;
  final bool isCollapsed;
  final bool expandValue;
  final double maxHeight;
  final List<TextInputFormatter>? inputFormatters;
  final bool onlyNumber;
  final bool useMaxHeight;
  final EdgeInsets margin;
  final bool obscureText;

  @override
  Widget build(BuildContext context) {
    Widget input = TextField(
      controller: controller ??
          TextEditingController.fromValue(TextEditingValue(text: defaultText)),
      maxLines: useMaxHeight ? maxLines ?? 1 : null,
      minLines: minLines,
      maxLength: maxLength,
      textInputAction: textInputAction,
      enabled: enabled,
      textAlign: textAlign,
      obscureText: obscureText,
      inputFormatters: onlyNumber
          ? [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))]
          : inputFormatters,
      decoration: const InputDecoration(
        border: InputBorder.none,
        counterText: "",
        contentPadding: EdgeInsets.all(0),
        isDense: true,
        enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent)),
        focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent)),
      ),
      style: TextStyle(color: color, fontSize: fontSize),
      onChanged: (value) => onChanged?.call(value.trim()),
    );
    return useMaxHeight
        ? Padding(
            padding: margin,
            child: ConstrainedBox(
              constraints: BoxConstraints(maxHeight: maxHeight),
              child: InkWell(
                onTap: onTap,
                child: SizedBox(
                  width: width,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('$label：',
                          style: TextStyle(color: color, fontSize: fontSize)),
                      Expanded(child: input),
                    ],
                  ),
                ),
              ),
            ),
          )
        : Padding(
            padding: margin,
            child: InkWell(
              onTap: onTap,
              child: SizedBox(
                width: width,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('$label：',
                        style: TextStyle(color: color, fontSize: fontSize)),
                    Expanded(child: input),
                  ],
                ),
              ),
            ),
          );
  }
}
