class VillageList {
  int? id;
  String? name;
  int alarmCount = 0;

  VillageList({this.id, this.name, required this.alarmCount});

  factory VillageList.fromJson(Map<String, dynamic> json) => VillageList(
        id: json['id'] as int?,
        name: json['name'] as String?,
        alarmCount: json['alarm_count'] as int,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'alarm_count': alarmCount,
      };
}
