import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/page/cases/index.dart';
import '../../../common/widget/drop_down.dart';

class CaseCategory extends StatelessWidget {
  const CaseCategory({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: GetBuilder<CasesController>(
          id: CaseControllerIDS.category,
          builder: (controller) {
            return Row(
              children: [
                DropdownWidget(
                  hint: '年',
                  selectedValue: controller.selectedYear,
                  items: controller.year,
                  onChanged: (val) async {
                    if (val == '年') {
                      controller.selectedYear = '';
                    } else {
                      controller.selectedYear = val;
                    }
                    controller.update([CaseControllerIDS.category]);
                    _getCases(controller);
                  },
                ),
                DropdownWidget(
                  hint: '月',
                  selectedValue: controller.selectedMonth,
                  items: controller.month,
                  onChanged: (val) async {
                    if (val == '月') {
                      controller.selectedMonth = '';
                    } else {
                      controller.selectedMonth = val;
                    }
                    controller.update([CaseControllerIDS.category]);
                    _getCases(controller);
                  },
                ),
                DropdownWidget(
                  hint: '日',
                  selectedValue: controller.selectedDay,
                  items: controller.day,
                  onChanged: (val) async {
                    if (val == '日') {
                      controller.selectedDay = '';
                    } else {
                      controller.selectedDay = val;
                    }
                    controller.update([CaseControllerIDS.category]);
                    _getCases(controller);
                  },
                ),
                const SizedBox(
                  width: 10,
                ),
                DropdownWidget(
                  hint: '警情类型',
                  selectedValue: controller.selectedType,
                  items: controller.type,
                  onChanged: (val) async {
                    controller.selectedType = val;
                    controller.currentPage = 1;
                    controller.update([CaseControllerIDS.category]);
                    _getCases(controller);
                  },
                ),
              ],
            );
          }),
    );
  }

  _getCases(CasesController controller) async {
    await controller.getCases(page: 1);
  }
}
