import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:police/common/widget/keep_alive_widget.dart';
import 'package:police/page/home/<USER>/about_text.dart';
import 'package:police/page/home/<USER>/honor.dart';
import 'package:police/page/home/<USER>/introduction_image_widget.dart';
import 'package:police/page/home/<USER>/map_widget.dart';
import 'package:police/page/home/<USER>/nav_button.dart';
import 'package:police/page/home/<USER>/person.dart';
import 'package:police/page/home/<USER>/police_station_introduction_widget.dart';

import 'index.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});

  // 主视图
  Widget _buildView() {
    List<GlobalKey> keys = [
      controller.person,
      controller.pic,
      controller.intro,
      controller.map,
      controller.honor,
      controller.honor2,
      controller.honor3,
      controller.honorWall,
    ];

    return Stack(
      children: [
        SingleChildScrollView(
          controller: controller.scrollController,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 60),
            child: Column(
              children: [
                LayoutBuilder(builder: (context, constraints) {
                  return SizedBox(
                    key: keys[0],
                    width: Get.width, // 使用屏幕宽度
                    height: Get.height - 60, // 使用屏幕高度
                    child: const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Person(),
                        AboutText(),
                      ],
                    ),
                  );
                }),
                IntroductionImageWidget(key: keys[1]),
                const SizedBox(height: 100),
                PoliceStationIntroductionWidget(key: keys[2]),
                MapWidget(key: keys[3]),
                const Honor(),
                // const SizedBox(height: 60),
              ],
            ),
          ),
        ),
        Align(
          alignment: Alignment.topRight,
          child: NavButton(globalKey: keys),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return KeepAliveWidget(
      child: GetBuilder<HomeController>(
        init: HomeController(),
        id: "home",
        builder: (_) {
          return _buildView();
        },
      ),
    );
  }
}
