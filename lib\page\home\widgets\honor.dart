import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/page/home/<USER>';

class Honor extends StatelessWidget {
  const Honor({super.key});

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;
    final List<String> imageList = [
      "assets/pic/ry01.png",
      "assets/pic/ry02.png",
      "assets/pic/ry03.png",
      "assets/pic/ry04.png",
      "assets/pic/ry04.png",
      "assets/pic/ry04.png",
    ];

    return LayoutBuilder(builder: (context, constrains) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 150),
        child: Column(
          children: [
            const AutoSizeText(
              '荣誉榜',
              maxLines: 1,
              style: TextStyle(
                color: Color(0xffbbb49a),
                fontSize: 60,
                fontWeight: FontWeight.w800,
                shadows: [
                  Shadow(
                    offset: Offset(2.0, 2.0),
                    blurRadius: 30,
                    color: Color.fromARGB(128, 0, 0, 0),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 50),
            Table(
              children: List.generate(
                (imageList.length / 2).ceil(), // Number of rows
                (rowIndex) {
                  return TableRow(
                    children: List.generate(2, (colIndex) {
                      final imageIndex = rowIndex * 2 + colIndex;
                      return SizedBox(
                        key: rowIndex == 0
                            ? colIndex == 0
                                ? Get.find<HomeController>().honor
                                : null
                            : rowIndex == 1
                                ? colIndex == 0
                                    ? Get.find<HomeController>().honor2
                                    : null
                                : colIndex == 0
                                    ? Get.find<HomeController>().honor3
                                    : null,
                        height: Get.height,
                        child: HonorImage(imagePath: imageList[imageIndex]),
                      );
                    }),
                  );
                },
              ),
            ),
            const Text(
              '荣誉墙',
              style: TextStyle(
                color: Color(0xffbbb49a),
                fontSize: 60,
                fontWeight: FontWeight.w800,
                shadows: [
                  Shadow(
                    offset: Offset(2.0, 2.0),
                    blurRadius: 30,
                    color: Color.fromARGB(128, 0, 0, 0),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 50),
            Image.asset(
              'assets/pic/ry04.png',
              key: Get.find<HomeController>().honorWall,
              fit: BoxFit.contain,
              height: Get.height * .9,
            ),
          ],
        ),
      );
    });
  }
}

class HonorImage extends StatelessWidget {
  final String imagePath;

  const HonorImage({super.key, required this.imagePath});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.asset(
          imagePath,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
