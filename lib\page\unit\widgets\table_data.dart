import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/model/village_unit_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/input_text.dart';
import 'package:police/common/widget/slide_animation_widget.dart';
import 'package:police/page/house/index.dart';
import 'package:police/page/unit/index.dart';

class UnitTableData extends StatelessWidget {
  const UnitTableData({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<UnitController>(
      id: UnitControllerIDS.unitItem,
      builder: (controller) {
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: controller.units.length,
          itemBuilder: (_, index) {
            return SlideAnimationWidget(
              actions: [
                InkWell(
                  onTap: () => controller.deleteCase(
                    context,
                    index,
                    id: controller.units[index].id,
                    name: controller.units[index].name,
                  ),
                  child: Container(
                    height: 50,
                    color: Colors.red,
                    alignment: Alignment.center,
                    child: const Icon(Icons.delete, color: Colors.white),
                  ),
                ),
                InkWell(
                  onTap: () {
                    int? unitId = controller.units[index].id;
                    if (unitId != null) {
                      jump2page(HousePage(unitId));
                    } else {
                      showToast('请先保存单元');
                    }
                  },
                  child: Container(
                    height: 50,
                    color: Colors.blue,
                    alignment: Alignment.center,
                    child: const Icon(Icons.visibility, color: Colors.white),
                  ),
                ),
              ],
              child: Row(
                children: List.generate(4, (i) {
                  return Expanded(
                    child: Container(
                      height: 50,
                      color: index % 2 == 1
                          ? const Color(0xd3021a37)
                          : const Color(0xd804254c),
                      alignment: Alignment.center,
                      margin: const EdgeInsets.all(.5),
                      child: InputText(
                        textAlign: TextAlign.center,
                        inputFormatters: i != 0
                            ? [
                                FilteringTextInputFormatter.allow(
                                    RegExp('[0-9]'))
                              ]
                            : null,
                        defaultText: _setDefaultText(i, controller, index),
                        style: const TextStyle(
                            color: Color(0xf0e5edff), fontSize: 16),
                        onChanged: (text) =>
                            _onChanged(text, controller, index, i),
                      ),
                    ),
                  );
                }),
              ),
            );
          },
        );
      },
    );
  }

  String _setDefaultText(int i, UnitController controller, int index) {
    var unit = controller.units[index];
    switch (i) {
      case 0:
        return unit.name ?? '';
      case 1:
        return unit.residents.toString();
      case 2:
        return unit.residentsCount.toString();
      default:
        return unit.merchants.toString();
    }
  }

  void _onChanged(String input, UnitController controller, int index, int i) {
    VillageUnitModel unit = controller.units[index];

    switch (i) {
      case 0:
        unit.name = input;
        break;
      case 1:
        unit.residents = int.parse(input);
        break;
      case 2:
        unit.residentsCount = int.parse(input);
        break;
      case 3:
        unit.merchants = int.parse(input);
        break;
    }
  }
}
