import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/house_api.dart';
import 'package:police/common/api/people_api.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/model/house_model.dart';
import 'package:police/common/model/people_detail_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/page/housedetail/index.dart';

import '../../common/model/people_rela_model/people_rela_model.dart';

/// 可更新的 Builder ID
class PeopledetailControllerIDS {
  static String photo = 'photo';
  static String gender = 'gender';
  static String birthDate = 'birthDate';
  static String peopleType = 'people_type';
  static String foucsPeopleInfo = 'foucs_people_info';
  static String idCard = 'id_card';
  static String progress = 'progress';
  static String joinWechat = 'join_wechat';
  static String scars = 'scars';
  static String tattoos = 'tattoos';
  static String peopleOtherInfo = 'people_other_info';
  static String peopleInfo = 'people_info';
  static String personType = 'person_type';
}

class PeopledetailController extends GetxController {
  // PeopleDetailModel peopleDetailInfo = PeopleDetailModel();

  // double imageUploadProgress = 0.0;

  int code = 0;

  /// 家庭关系
  List<PeopleRelaModel> house = [];

  /// 所在家庭
  HouseModel detail = HouseModel(address: '该人员未加入家庭');

  /// 修改是否保存
  // bool isSave = true;

  /// 选中的人员照片
  String photoPath = '';

  String idCardErrorText = '';

  /// 保存
  Future<PeopleDetailModel> save(
      BuildContext context, int? id, Map<String, dynamic> data) async {
    Dialogs.showLoadingDialog(context);
    PeopleDetailModel result = id == null
        ? await PeopleApi.createPeople(data, pageBack: true)
        : await PeopleApi.patchPeopleDetail(id, data, pageBack: true);
    // isSave = true;
    showToast('保存成功');
    return result;
  }

  /// 删除人员
  Future deletePeople(BuildContext context, int id, String name) async {
    Dialogs.showConfirmDialog(
      context,
      title: '是否确认删除',
      subTitle: ' $name',
      content: '删除后无法恢复',
      onConfirm: () async {
        Dialogs.showLoadingDialog(context);
        await PeopleApi.deletePeople(id);
        pageback(result: 'remove');
      },
    );
  }

  /// 获取人员所在家庭
  Future getHouse(int personId) async {
    code = 0;

    house = await HouseApi.getHouseRelationFromPerson(
      personId: personId,
      fail: (result) {
        code = 404;
        update(['detail']);
      },
    );

    if (house.isNotEmpty) {
      if (house[0].household != null) {
        HousedetailController controller = Get.put(HousedetailController());

        detail = await controller.getHouseInfo(
          house[0].household!.id!,
          fail: (result) {
            code = 404;
            update(['detail']);
          },
        );
      }
    }
    code = 200;
    update(['detail']);
  }
}
