import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/model/people_detail_model.dart';
import 'package:police/common/widget/form_input.dart';
import 'package:police/common/widget/radio.dart';
import 'package:police/common/widget/rich_editor.dart';
import 'package:police/page/peopledetail/controller.dart';

class FocusPeopleInfo extends StatelessWidget {
  const FocusPeopleInfo(
      {super.key, required this.controller, required this.detail});

  final PeopledetailController controller;
  final PeopleDetailModel detail;

  @override
  Widget build(BuildContext context) {
    List<String> personType = const [
      '刑满释放',
      '吸毒涉毒',
      '缠访、闹访',
      '监视居住',
      '取保候审',
      '精神疾病',
      '邪教',
      '其他'
    ];
    return GetBuilder<PeopledetailController>(
        id: 'foucs_people_info',
        builder: (context) {
          return Visibility(
            visible: detail.isKeyPersonnel,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('重点人员信息',
                    style: TextStyle(
                      color: Color(0xc2ffffff),
                      fontSize: 40,
                      fontWeight: FontWeight.bold,
                    )),
                const SizedBox(height: 20),
                FormInput(
                  label: '实际居住',
                  // maxLength: 50,
                  defaultText: detail.actualResidence ?? '',
                  onChanged: (val) {
                    detail.actualResidence = val.trim();
                    _updata();
                  },
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              FormInput(
                                label: '编号',
                                maxLength: 10,
                                defaultText: detail.serialNumber ?? '',
                                onChanged: (val) {
                                  detail.serialNumber = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '曾用名',
                                maxLength: 20,
                                defaultText: detail.formerName ?? '',
                                onChanged: (val) {
                                  detail.formerName = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '绰号',
                                maxLength: 10,
                                defaultText: detail.nickname ?? '',
                                onChanged: (val) {
                                  detail.nickname = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '工作单位',
                                maxLength: 20,
                                defaultText: detail.employer ?? '',
                                onChanged: (val) {
                                  detail.employer = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '健康状况',
                                maxLength: 20,
                                defaultText: detail.healthStatus ?? '',
                                onChanged: (val) {
                                  detail.healthStatus = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '民族',
                                maxLength: 10,
                                defaultText: detail.nationality ?? '',
                                onChanged: (val) {
                                  detail.nationality = val.trim();
                                  _updata();
                                },
                              ),

                              FormInput(
                                label: '身高',
                                maxLength: 5,
                                defaultText: detail.height ?? '',
                                onChanged: (val) {
                                  detail.height = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '体重',
                                maxLength: 5,
                                defaultText: detail.weight ?? '',
                                onChanged: (val) {
                                  detail.weight = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '肤色',
                                maxLength: 10,
                                defaultText: detail.skinColor ?? '',
                                onChanged: (val) {
                                  detail.skinColor = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '口音',
                                maxLength: 10,
                                defaultText: detail.accent ?? '',
                                onChanged: (val) {
                                  detail.accent = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '发型',
                                maxLength: 10,
                                defaultText: detail.hairstyle ?? '',
                                onChanged: (val) {
                                  detail.hairstyle = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '脸型',
                                maxLength: 10,
                                defaultText: detail.faceShape ?? '',
                                onChanged: (val) {
                                  detail.faceShape = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '眼睛',
                                maxLength: 10,
                                defaultText: detail.eyes ?? '',
                                onChanged: (val) {
                                  detail.eyes = val.trim();
                                  _updata();
                                },
                              ),

                              FormInput(
                                label: '其他特征',
                                maxLength: 20,
                                defaultText: detail.otherCharacteristics ?? '',
                                onChanged: (val) {
                                  detail.otherCharacteristics = val.trim();
                                  _updata();
                                },
                              ),
                              GetBuilder<PeopledetailController>(
                                  id: PeopledetailControllerIDS.scars,
                                  builder: (context) {
                                    return RadioWidget(
                                      label: '有无疤痕',
                                      fontSize: 17,
                                      onChanged: (val) {
                                        detail.scars =
                                            val == '是' ? true : false;
                                        controller.update(
                                            [PeopledetailControllerIDS.scars]);
                                        _updata();
                                      },
                                      valueGroup: detail.scars! ? '是' : '否',
                                      radios: const ['是', '否'],
                                    );
                                  }),
                              GetBuilder<PeopledetailController>(
                                  id: PeopledetailControllerIDS.tattoos,
                                  builder: (context) {
                                    return RadioWidget(
                                      label: '有无纹身',
                                      fontSize: 17,
                                      onChanged: (val) {
                                        detail.tattoos =
                                            val == '是' ? true : false;
                                        controller.update([
                                          PeopledetailControllerIDS.tattoos
                                        ]);
                                        _updata();
                                      },
                                      valueGroup: detail.tattoos! ? '是' : '否',
                                      radios: const ['是', '否'],
                                    );
                                  }),

                              // FormInput(
                              //   label: '有无纹身',
                              //
                              //   defaultText: detail.weight ?? '',
                              //   onChanged: (val) {
                              //   detail.serialNumber = val.trim();
                              //   _updata();
                              // },
                              // ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // FormInput(
                              //   label: '人员类型',
                              //   maxLength: 10,
                              //   defaultText: detail.personType ?? '',
                              //   onChanged: (val) {
                              //     detail.personType = val.trim();
                              //     _updata();
                              //   },
                              // ),
                              GetBuilder<PeopledetailController>(
                                  id: PeopledetailControllerIDS.personType,
                                  builder: (context) {
                                    return RadioWidget(
                                      label: '人员类型',
                                      onChanged: (val) {
                                        detail.personType = val;
                                        controller.update([
                                          PeopledetailControllerIDS.personType
                                        ]);
                                        _updata();
                                      },
                                      onChangedOther: (val, index) {
                                        detail.personType = val;

                                        _updata();
                                      },
                                      valueGroup: !personType
                                              .contains(detail.personType)
                                          ? '其他'
                                          : detail.personType ?? '',
                                      otherText: !personType
                                              .contains(detail.personType)
                                          ? detail.personType ?? ''
                                          : '',
                                      canEdit: (value) =>
                                          value == detail.personType ||
                                          !personType
                                              .contains(detail.personType),
                                      radios: personType,
                                    );
                                  }),
                              FormInput(
                                label: '个人特长',
                                maxLength: 10,
                                defaultText: detail.strengths ?? '',
                                onChanged: (val) {
                                  detail.strengths = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '微信号',
                                maxLength: 20,
                                defaultText: detail.wechat ?? '',
                                onChanged: (val) {
                                  detail.wechat = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '支付宝',
                                maxLength: 20,
                                defaultText: detail.alipay ?? '',
                                onChanged: (val) {
                                  detail.alipay = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: 'QQ号',
                                maxLength: 10,
                                defaultText: detail.qq ?? '',
                                onChanged: (val) {
                                  detail.qq = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '驾驶证',
                                maxLength: 20,
                                defaultText: detail.driverLicense ?? '',
                                onChanged: (val) {
                                  detail.driverLicense = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '护照 (港澳通行证)',
                                maxLength: 20,
                                defaultText: detail.passport ?? '',
                                onChanged: (val) {
                                  detail.passport = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '指纹编号',
                                maxLength: 50,
                                defaultText: detail.fingerprint ?? '',
                                onChanged: (val) {
                                  detail.fingerprint = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: 'DNA编号',
                                maxLength: 50,
                                defaultText: detail.dna ?? '',
                                onChanged: (val) {
                                  detail.dna = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '声纹编号',
                                maxLength: 50,
                                defaultText: detail.voiceprint ?? '',
                                onChanged: (val) {
                                  detail.voiceprint = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '虹膜编号',
                                maxLength: 50,
                                defaultText: detail.iris ?? '',
                                onChanged: (val) {
                                  detail.iris = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '个人简历',
                                defaultText: detail.resume ?? '',
                                useMaxHeight: false,
                                maxHeight: double.infinity,
                                minLines: 3,
                                maxLines: 999,
                                textInputAction: TextInputAction.newline,
                                onChanged: (val) {
                                  detail.resume = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '经济来源',
                                defaultText: detail.sourceIncome ?? '',
                                useMaxHeight: false,
                                minLines: 3,
                                maxLines: 999,
                                textInputAction: TextInputAction.newline,
                                onChanged: (val) {
                                  detail.sourceIncome = val.trim();
                                  _updata();
                                },
                              ),
                              FormInput(
                                label: '交往人员情况',
                                defaultText: detail.associates ?? '',
                                useMaxHeight: false,
                                minLines: 3,
                                maxLines: 999,
                                textInputAction: TextInputAction.newline,
                                onChanged: (val) {
                                  detail.associates = val.trim();
                                  _updata();
                                },
                              ),
                              GetBuilder<PeopledetailController>(
                                  id: PeopledetailControllerIDS.joinWechat,
                                  builder: (context) {
                                    return RadioWidget(
                                      label: '是否加入微信群',
                                      fontSize: 17,
                                      onChanged: (val) {
                                        detail.wechatGroup =
                                            val == '是' ? true : false;
                                        controller.update([
                                          PeopledetailControllerIDS.joinWechat
                                        ]);
                                        _updata();
                                      },
                                      valueGroup:
                                          detail.wechatGroup! ? '是' : '否',
                                      radios: const ['是', '否'],
                                    );
                                  }),
                            ],
                          ),
                        ),
                      ],
                    ),
                    FormInput(
                      label: '家庭主要成员及社会关系',
                      defaultText: detail.socialRelations ?? '',
                      useMaxHeight: false,
                      minLines: 3,
                      maxLines: 999,
                      textInputAction: TextInputAction.newline,
                      onChanged: (val) {
                        detail.socialRelations = val.trim();
                        _updata();
                      },
                    ),
                    FormInput(
                      label: '风险评估家庭稳定',
                      useMaxHeight: false,
                      maxLines: 999,
                      minLines: 3,
                      textInputAction: TextInputAction.newline,
                      defaultText: detail.riskAssessmentFamilyStability ?? '',
                      onChanged: (val) {
                        detail.riskAssessmentFamilyStability = val.trim();
                        _updata();
                      },
                    ),
                    FormInput(
                      label: '社会扶持情况',
                      useMaxHeight: false,
                      maxLines: 999,
                      minLines: 3,
                      textInputAction: TextInputAction.newline,
                      defaultText: detail.socialSupportStatus ?? '',
                      onChanged: (val) {
                        detail.socialSupportStatus = val.trim();
                        _updata();
                      },
                    ),
                    FormInput(
                      label: '本人活动范围',
                      useMaxHeight: false,
                      maxLines: 999,
                      minLines: 3,
                      textInputAction: TextInputAction.newline,
                      defaultText: detail.personalActivityRange ?? '',
                      onChanged: (val) {
                        detail.personalActivityRange = val.trim();
                        _updata();
                      },
                    ),
                    FormInput(
                      label: '现实表现',
                      minLines: 3,
                      useMaxHeight: false,
                      maxLines: 999,
                      textInputAction: TextInputAction.newline,
                      defaultText: detail.realPerformance ?? '',
                      onChanged: (val) {
                        detail.realPerformance = val.trim();
                        _updata();
                      },
                    ),
                    FormInput(
                      label: '综合评估',
                      minLines: 3,
                      useMaxHeight: false,
                      maxLines: 999,
                      textInputAction: TextInputAction.newline,
                      defaultText: detail.overallAssessment ?? '',
                      onChanged: (val) {
                        detail.overallAssessment = val.trim();
                        _updata();
                      },
                    ),
                    FormInput(
                      label: '违法犯罪事实',
                      minLines: 3,
                      useMaxHeight: false,
                      maxLines: 999,
                      textInputAction: TextInputAction.newline,
                      defaultText: detail.criminalRecord ?? '',
                      onChanged: (val) {
                        detail.criminalRecord = val.trim();
                        _updata();
                      },
                    ),
                    Container(
                      constraints: BoxConstraints(maxHeight: 500),
                      child: RichEditor(
                        label: '工作情况：',
                        document: detail.work,
                        onDocumentChange: (document) {
                          List<Map<String, dynamic>> data = [];
                          for (var element in document) {
                            data.add(element.toJson());
                          }
                          detail.work = data;
                          _updata();
                        },
                        // onSave: (result) {},
                      ),
                    ),
                    FormInput(
                      label: '备注',
                      minLines: 3,
                      useMaxHeight: false,
                      maxLines: 999,
                      textInputAction: TextInputAction.newline,
                      defaultText: detail.notes ?? '',
                      onChanged: (val) {
                        detail.notes = val.trim();
                        _updata();
                      },
                    ),
                  ],
                ),
              ],
            ),
          );
        });
  }

  void _updata() {
    // controller.isSave = false;
  }
}
