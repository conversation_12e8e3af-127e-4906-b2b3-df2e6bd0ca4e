import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/model/village_area_model/village_building.dart';
import 'package:police/page/area/controller.dart';

import '../../../common/widget/float_square_button.dart';

class FloatButton extends StatelessWidget {
  const FloatButton({super.key, required this.villageId});

  final int villageId;

  @override
  Widget build(BuildContext context) {
    final AreaController contoller = Get.find<AreaController>();
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.only(right: 36),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FloatSquareButton(
                onTap: () {
                  Get.find<AreaController>().getAreas(villageId);
                },
                icon: Icons.refresh),
            const SizedBox(height: 10),
            FloatSquareButton(
                onTap: () async {
                  await contoller.createBuilds(context, villageId);
                },
                icon: FontAwesomeIcons.floppyDisk),
            const SizedBox(height: 10),
            FloatSquareButton(
              onTap: () {
                if (contoller.areas[contoller.currentArea].id == null) {
                  showToast('请先保存新增的区域');
                } else {
                  contoller.areas[contoller.currentArea].villageBuilding!
                      .add(VillageBuilding());
                  contoller.builds =
                      contoller.areas[contoller.currentArea].villageBuilding!;
                  contoller.update(
                      [AreaControllerIDS.build, AreaControllerIDS.drawer]);
                }
              },
              icon: Icons.add,
            ),
          ],
        ),
      ),
    );
  }
}
