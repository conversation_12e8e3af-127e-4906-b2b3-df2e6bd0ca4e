; 定义安装程序的基本信息
[Setup]
AppName=综合指挥室指挥系统
AppVersion=115
DefaultDirName={pf}\综合指挥室指挥系统
DefaultGroupName=综合指挥室指挥系统
OutputBaseFilename=综合指挥室指挥系统
Compression=lzma
SolidCompression=yes
AppId={{123e4567-e89b-12d3-a456-426614174000}}
UninstallDisplayIcon={app}\police.exe
VersionInfoVersion=1.1.5
VersionInfoCompany=Your Company
VersionInfoDescription=综合指挥室指挥系统
; 设置安装程序的图标
SetupIconFile=build\windows\x64\runner\Release\app_icon.ico


; 选择中文作为安装界面语言
[Languages]
Name: "ChineseSimplified"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

; 定义要安装的文件
[Files]
Source: "build\windows\x64\runner\Release\police.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "build\windows\x64\runner\Release\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; 创建桌面图标和开始菜单快捷方式
[Icons]
Name: "{autoprograms}\综合指挥室指挥系统"; Filename: "{app}\police.exe"; IconFilename: "{app}\app_icon.ico"
Name: "{userdesktop}\综合指挥室指挥系统"; Filename: "{app}\police.exe"; Tasks: desktopicon; IconFilename: "{app}\app_icon.ico"

; 任务选项（比如创建桌面图标）
[Tasks]
Name: "desktopicon"; Description: "创建桌面图标"; GroupDescription: "额外任务"

; 卸载时要删除的文件和目录
[UninstallDelete]
Type: files; Name: "{app}\*.*"
Type: dirifempty; Name: "{app}"

; 设置安装过程中的页面和动作
[Run]
Filename: "{app}\police.exe"; Description: "{cm:LaunchProgram,综合指挥室指挥系统}"; Flags: nowait postinstall skipifsilent
