// import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:police/common/util/https.dart';
import 'package:police/common/widget/drag_window.dart';
import 'package:police/common/widget/normol_button.dart';
import 'package:police/common/widget/scaffold_widget.dart';

import 'index.dart';

class SplashPage extends GetView<SplashController> {
  const SplashPage({super.key, this.keyError = false});

  final bool keyError; // 是否密钥错误

  // 主视图
  Widget _buildView(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          'assets/pic/splash_bg.jpg',
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover,
        ),
        Center(
          child: Image.asset(
            'assets/pic/logo_001.png',
            width: 400,
          ),
        ),
        const DragWindowWidget(
          child: Si<PERSON>Box(
            height: 60,
            width: double.infinity,
          ),
        ),
        GetBuilder<SplashController>(
            id: 'server_button',
            builder: (contro) {
              return Visibility(
                visible: contro.countdown <= 0,
                child: Padding(
                  padding: const EdgeInsets.all(60),
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: NormolButton(
                      width: 300,
                      lable: '更改服务器地址',
                      onTap: () => contro.changeServer(context),
                    ),
                  ),
                ),
              );
            }),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SplashController>(
      init: SplashController(),
      id: "splash",
      builder: (_) {
        controller.startCountdown();
        controller.jump2Home(keyError);

        return ScaffoldWidget(
          showBack: false,
          body: _buildView(context),
        );
      },
    );
  }
}
