class Person {
  String? id;
  String? job;
  String? name;
  String? photo;
  String? telephone;

  Person({this.id, this.job, this.name, this.photo, this.telephone});

  factory Person.fromJson(Map<String, dynamic> json) => Person(
        id: json['id'] as String?,
        job: json['job'] as String?,
        name: json['name'] as String?,
        photo: json['photo'] as String?,
        telephone: json['telephone'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'job': job,
        'name': name,
        'photo': photo,
        'telephone': telephone,
      };
}
